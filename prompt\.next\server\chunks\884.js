exports.id=884,exports.ids=[884],exports.modules={4161:(a,b,c)=>{"use strict";c.d(b,{g:()=>x});var d,e=c(39163),f=c(87955),g=c.n(f),h=c(43210),i=c.n(h);function j(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function k(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){k(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{if(f=(c=c.call(a)).next,0===b){if(Object(c)!==c)return;i=!1}else for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(a,b)||q(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(a){return function(a){if(Array.isArray(a))return j(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||q(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(a){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function q(a,b){if(a){if("string"==typeof a)return j(a,b);var c=({}).toString.call(a).slice(8,-1);return"Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c?Array.from(a):"Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?j(a,b):void 0}}try{d=c(96999).version}catch(a){d=process.env.FA_VERSION||"7.0.0-alpha8"}function r(a){var b;return(b=a-0)==b?a:(a=a.replace(/[\-_\s]+(.)?/g,function(a,b){return b?b.toUpperCase():""})).substr(0,1).toLowerCase()+a.substr(1)}var s=["style"],t=!1;try{t=!0}catch(a){}function u(a){return a&&"object"===p(a)&&a.prefix&&a.iconName&&a.icon?a:e.qg.icon?e.qg.icon(a):null===a?null:a&&"object"===p(a)&&a.prefix&&a.iconName?a:Array.isArray(a)&&2===a.length?{prefix:a[0],iconName:a[1]}:"string"==typeof a?{prefix:"fas",iconName:a}:void 0}function v(a,b){return Array.isArray(b)&&b.length>0||!Array.isArray(b)&&b?k({},a,b):{}}var w={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,rotateBy:!1,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1,widthAuto:!1},x=i().forwardRef(function(a,b){var c,f,g,h,i,j,l,p,q,r,s,x,z,A,B,C,D,E,F,G,H,I,J,K=m(m({},w),a),L=K.icon,M=K.mask,N=K.symbol,O=K.className,P=K.title,Q=K.titleId,R=K.maskId,S=u(L),T=v("classes",[].concat(o((c=K.beat,f=K.fade,g=K.beatFade,h=K.bounce,i=K.shake,j=K.flash,l=K.spin,p=K.spinPulse,q=K.spinReverse,r=K.pulse,s=K.fixedWidth,x=K.inverse,z=K.border,A=K.listItem,B=K.flip,C=K.size,D=K.rotation,E=K.pull,F=K.swapOpacity,G=K.rotateBy,H=K.widthAuto,I=function(a,b){for(var c=n(a.split("-"),2),d=c[0],e=c[1],f=n(b.split("-"),2),g=f[0],h=f[1],i=d.split("."),j=g.split("."),k=0;k<Math.max(i.length,j.length);k++){var l=i[k]||"0",m=j[k]||"0",o=parseInt(l,10),p=parseInt(m,10);if(o!==p)return o>p}for(var q=0;q<Math.max(i.length,j.length);q++){var r=i[q]||"0",s=j[q]||"0";if(r!==s&&r.length!==s.length)return r.length<s.length}return!e||!!h}(d,"7.0.0-alpha1"),Object.keys(J=k(k(k(k(k(k({"fa-beat":c,"fa-fade":f,"fa-beat-fade":g,"fa-bounce":h,"fa-shake":i,"fa-flash":j,"fa-spin":l,"fa-spin-reverse":q,"fa-spin-pulse":p,"fa-pulse":r,"fa-fw":s,"fa-inverse":x,"fa-border":z,"fa-li":A,"fa-flip":!0===B,"fa-flip-horizontal":"horizontal"===B||"both"===B,"fa-flip-vertical":"vertical"===B||"both"===B},"fa-".concat(C),null!=C),"fa-rotate-".concat(D),null!=D&&0!==D),"fa-pull-".concat(E),null!=E),"fa-swap-opacity",F),"fa-rotate-by",I&&G),"fa-width-auto",I&&H)).map(function(a){return J[a]?a:null}).filter(function(a){return a}))),o((O||"").split(" ")))),U=v("transform","string"==typeof K.transform?e.qg.transform(K.transform):K.transform),V=v("mask",u(M)),W=(0,e.Kk)(S,m(m(m(m({},T),U),V),{},{symbol:N,title:P,titleId:Q,maskId:R}));if(!W)return!function(){if(!t&&console&&"function"==typeof console.error){var a;(a=console).error.apply(a,arguments)}}("Could not find icon",S),null;var X=W.abstract,Y={ref:b};return Object.keys(K).forEach(function(a){w.hasOwnProperty(a)||(Y[a]=K[a])}),y(X[0],Y)});x.displayName="FontAwesomeIcon",x.propTypes={beat:g().bool,border:g().bool,beatFade:g().bool,bounce:g().bool,className:g().string,fade:g().bool,flash:g().bool,mask:g().oneOfType([g().object,g().array,g().string]),maskId:g().string,fixedWidth:g().bool,inverse:g().bool,flip:g().oneOf([!0,!1,"horizontal","vertical","both"]),icon:g().oneOfType([g().object,g().array,g().string]),listItem:g().bool,pull:g().oneOf(["right","left"]),pulse:g().bool,rotation:g().oneOf([0,90,180,270]),rotateBy:g().bool,shake:g().bool,size:g().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:g().bool,spinPulse:g().bool,spinReverse:g().bool,symbol:g().oneOfType([g().bool,g().string]),title:g().string,titleId:g().string,transform:g().oneOfType([g().string,g().object]),swapOpacity:g().bool,widthAuto:g().bool};var y=(function a(b,c){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof c)return c;var e=(c.children||[]).map(function(c){return a(b,c)}),f=Object.keys(c.attributes||{}).reduce(function(a,b){var d=c.attributes[b];switch(b){case"class":a.attrs.className=d,delete c.attributes.class;break;case"style":a.attrs.style=d.split(";").map(function(a){return a.trim()}).filter(function(a){return a}).reduce(function(a,b){var c=b.indexOf(":"),d=r(b.slice(0,c)),e=b.slice(c+1).trim();return d.startsWith("webkit")?a[d.charAt(0).toUpperCase()+d.slice(1)]=e:a[d]=e,a},{});break;default:0===b.indexOf("aria-")||0===b.indexOf("data-")?a.attrs[b.toLowerCase()]=d:a.attrs[r(b)]=d}return a},{attrs:{}}),g=d.style,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,s);return f.attrs.style=m(m({},f.attrs.style),void 0===g?{}:g),b.apply(void 0,[c.tag,m(m({},f.attrs),h)].concat(o(e)))}).bind(null,i().createElement)},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useSearchParams")&&c.d(b,{useSearchParams:function(){return d.useSearchParams}})},34452:a=>{"use strict";a.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},39163:(a,b,c)=>{"use strict";function d(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}c.d(b,{Kk:()=>cb,Yv:()=>b9,qg:()=>ca});function e(a,b){var c="undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(!c){if(Array.isArray(a)||(c=m(a))||b&&a&&"number"==typeof a.length){c&&(a=c);var d=0,e=function(){};return{s:e,n:function(){return d>=a.length?{done:!0}:{done:!1,value:a[d++]}},e:function(a){throw a},f:e}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var f,g=!0,h=!1;return{s:function(){c=c.call(a)},n:function(){var a=c.next();return g=a.done,a},e:function(a){h=!0,f=a},f:function(){try{g||null==c.return||c.return()}finally{if(h)throw f}}}}function f(a,b,c){return(b=k(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function g(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function h(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?g(Object(c),!0).forEach(function(b){f(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):g(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function i(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{if(f=(c=c.call(a)).next,0===b){if(Object(c)!==c)return;i=!1}else for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(a,b)||m(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(a){return function(a){if(Array.isArray(a))return d(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||m(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}function l(a){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function m(a,b){if(a){if("string"==typeof a)return d(a,b);var c=({}).toString.call(a).slice(8,-1);return"Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c?Array.from(a):"Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?d(a,b):void 0}}var n,o,p,q=function(){},r={},s={},t=null,u={mark:q,measure:q};try{"undefined"!=typeof window&&(r=window),"undefined"!=typeof document&&(s=document),"undefined"!=typeof MutationObserver&&(t=MutationObserver),"undefined"!=typeof performance&&(u=performance)}catch(a){}var v=(r.navigator||{}).userAgent,w=void 0===v?"":v,x=r,y=s,z=t,A=u;x.document;var B=!!y.documentElement&&!!y.head&&"function"==typeof y.addEventListener&&"function"==typeof y.createElement,C=~w.indexOf("MSIE")||~w.indexOf("Trident/"),D={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"},slab:{"fa-regular":"regular",faslr:"regular"},"slab-press":{"fa-regular":"regular",faslpr:"regular"},thumbprint:{"fa-light":"light",fatl:"light"},whiteboard:{"fa-semibold":"semibold",fawsb:"semibold"},notdog:{"fa-solid":"solid",fans:"solid"},"notdog-duo":{"fa-solid":"solid",fands:"solid"},etch:{"fa-solid":"solid",faes:"solid"},jelly:{"fa-regular":"regular",fajr:"regular"},"jelly-fill":{"fa-regular":"regular",fajfr:"regular"},"jelly-duo":{"fa-regular":"regular",fajdr:"regular"},chisel:{"fa-regular":"regular",facr:"regular"}},E=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],F="classic",G="duotone",H="sharp",I="sharp-duotone",J="chisel",K="etch",L="jelly",M="jelly-duo",N="jelly-fill",O="notdog",P="notdog-duo",Q="slab",R="slab-press",S="thumbprint",T="whiteboard",U=[F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T];f(f(f(f(f(f(f(f(f(f(o={},F,"Classic"),G,"Duotone"),H,"Sharp"),I,"Sharp Duotone"),J,"Chisel"),K,"Etch"),L,"Jelly"),M,"Jelly Duo"),N,"Jelly Fill"),O,"Notdog"),f(f(f(f(f(o,P,"Notdog Duo"),Q,"Slab"),R,"Slab Press"),S,"Thumbprint"),T,"Whiteboard");var V=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["chisel",{defaultShortPrefixId:"facr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["etch",{defaultShortPrefixId:"faes",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["jelly",{defaultShortPrefixId:"fajr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-duo",{defaultShortPrefixId:"fajdr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-fill",{defaultShortPrefixId:"fajfr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["notdog",{defaultShortPrefixId:"fans",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["notdog-duo",{defaultShortPrefixId:"fands",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["slab",{defaultShortPrefixId:"faslr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["slab-press",{defaultShortPrefixId:"faslpr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["thumbprint",{defaultShortPrefixId:"fatl",defaultStyleId:"light",styleIds:["light"],futureStyleIds:[],defaultFontWeight:300}],["whiteboard",{defaultShortPrefixId:"fawsb",defaultStyleId:"semibold",styleIds:["semibold"],futureStyleIds:[],defaultFontWeight:600}]]),W=["fak","fa-kit","fakd","fa-kit-duotone"],X={kit:{fak:"kit","fa-kit":"kit"},"kit-duotone":{fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"}};f(f({},"kit","Kit"),"kit-duotone","Kit Duotone");var Y={kit:{kit:"fak"},"kit-duotone":{"kit-duotone":"fakd"}},Z={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"};f(f(f(f(f(f(f(f(f(f(p={},"classic","Classic"),"duotone","Duotone"),"sharp","Sharp"),"sharp-duotone","Sharp Duotone"),"chisel","Chisel"),"etch","Etch"),"jelly","Jelly"),"jelly-duo","Jelly Duo"),"jelly-fill","Jelly Fill"),"notdog","Notdog"),f(f(f(f(f(p,"notdog-duo","Notdog Duo"),"slab","Slab"),"slab-press","Slab Press"),"thumbprint","Thumbprint"),"whiteboard","Whiteboard"),f(f({},"kit","Kit"),"kit-duotone","Kit Duotone");var $={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"},slab:{faslr:"fa-regular"},"slab-press":{faslpr:"fa-regular"},whiteboard:{fawsb:"fa-semibold"},thumbprint:{fatl:"fa-light"},notdog:{fans:"fa-solid"},"notdog-duo":{fands:"fa-solid"},etch:{faes:"fa-solid"},jelly:{fajr:"fa-regular"},"jelly-fill":{fajfr:"fa-regular"},"jelly-duo":{fajdr:"fa-regular"},chisel:{facr:"fa-regular"}},_=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","faslr","faslpr","fawsb","fatl","fans","fands","faes","fajr","fajfr","fajdr","facr"].concat(["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],["fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands","fa-semibold"]),aa=[1,2,3,4,5,6,7,8,9,10],ab=aa.concat([11,12,13,14,15,16,17,18,19,20]),ac=[].concat(j(["classic","duotone","sharp","sharp-duotone","slab","slab-press","whiteboard","thumbprint","notdog","notdog-duo","etch","jelly","jelly-fill","jelly-duo","chisel"]),["solid","regular","light","thin","duotone","brands","semibold"],["aw","fw","pull-left","pull-right"],["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","inverse","layers","layers-bottom-left","layers-bottom-right","layers-counter","layers-text","layers-top-left","layers-top-right","li","pull-end","pull-start","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul","width-auto","width-fixed",Z.GROUP,Z.SWAP_OPACITY,Z.PRIMARY,Z.SECONDARY]).concat(aa.map(function(a){return"".concat(a,"x")})).concat(ab.map(function(a){return"w-".concat(a)})),ad="___FONT_AWESOME___",ae="svg-inline--fa",af="data-fa-i2svg",ag="data-fa-pseudo-element",ah="data-prefix",ai="data-icon",aj="fontawesome-i2svg",ak=["HTML","HEAD","STYLE","SCRIPT"],al=["::before","::after",":before",":after"],am=function(){try{return!0}catch(a){return!1}}();function an(a){return new Proxy(a,{get:function(a,b){return b in a?a[b]:a[F]}})}var ao=h({},D);ao[F]=h(h(h(h({},{"fa-duotone":"duotone"}),D[F]),X.kit),X["kit-duotone"]);var ap=an(ao),aq=h({},{chisel:{regular:"facr"},classic:{brands:"fab",light:"fal",regular:"far",solid:"fas",thin:"fat"},duotone:{light:"fadl",regular:"fadr",solid:"fad",thin:"fadt"},etch:{solid:"faes"},jelly:{regular:"fajr"},"jelly-duo":{regular:"fajdr"},"jelly-fill":{regular:"fajfr"},notdog:{solid:"fans"},"notdog-duo":{solid:"fands"},sharp:{light:"fasl",regular:"fasr",solid:"fass",thin:"fast"},"sharp-duotone":{light:"fasdl",regular:"fasdr",solid:"fasds",thin:"fasdt"},slab:{regular:"faslr"},"slab-press":{regular:"faslpr"},thumbprint:{light:"fatl"},whiteboard:{semibold:"fawsb"}});aq[F]=h(h(h(h({},{duotone:"fad"}),aq[F]),Y.kit),Y["kit-duotone"]);var ar=an(aq),as=h({},$);as[F]=h(h({},as[F]),{fak:"fa-kit"});var at=an(as),au=h({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"},slab:{"fa-regular":"faslr"},"slab-press":{"fa-regular":"faslpr"},whiteboard:{"fa-semibold":"fawsb"},thumbprint:{"fa-light":"fatl"},notdog:{"fa-solid":"fans"},"notdog-duo":{"fa-solid":"fands"},etch:{"fa-solid":"faes"},jelly:{"fa-regular":"fajr"},"jelly-fill":{"fa-regular":"fajfr"},"jelly-duo":{"fa-regular":"fajdr"},chisel:{"fa-regular":"facr"}});au[F]=h(h({},au[F]),{"fa-kit":"fak"}),an(au);var av=/fa(k|kd|s|r|l|t|d|dr|dl|dt|b|slr|slpr|wsb|tl|ns|nds|es|jr|jfr|jdr|cr|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,aw="fa-layers-text",ax=/Font ?Awesome ?([567 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit|Notdog Duo|Notdog|Chisel|Etch|Thumbprint|Jelly Fill|Jelly Duo|Jelly|Slab Press|Slab|Whiteboard)?.*/i;an(h({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"},slab:{400:"faslr"},"slab-press":{400:"faslpr"},whiteboard:{600:"fawsb"},thumbprint:{300:"fatl"},notdog:{900:"fans"},"notdog-duo":{900:"fands"},etch:{900:"faes"},chisel:{400:"facr"},jelly:{400:"fajr"},"jelly-fill":{400:"fajfr"},"jelly-duo":{400:"fajdr"}}));var ay=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],az={GROUP:"duotone-group",PRIMARY:"primary",SECONDARY:"secondary"},aA=[].concat(j(["kit"]),j(ac)),aB=x.FontAwesomeConfig||{};y&&"function"==typeof y.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-search-pseudo-elements","searchPseudoElements"],["data-search-pseudo-elements-warnings","searchPseudoElementsWarnings"],["data-search-pseudo-elements-full-scan","searchPseudoElementsFullScan"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(a){var b,c=i(a,2),d=c[0],e=c[1],f=""===(b=function(a){var b=y.querySelector("script["+a+"]");if(b)return b.getAttribute(a)}(d))||"false"!==b&&("true"===b||b);null!=f&&(aB[e]=f)});var aC={styleDefault:"solid",familyDefault:F,cssPrefix:"fa",replacementClass:ae,autoReplaceSvg:!0,autoAddCss:!0,searchPseudoElements:!1,searchPseudoElementsWarnings:!0,searchPseudoElementsFullScan:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};aB.familyPrefix&&(aB.cssPrefix=aB.familyPrefix);var aD=h(h({},aC),aB);aD.autoReplaceSvg||(aD.observeMutations=!1);var aE={};Object.keys(aC).forEach(function(a){Object.defineProperty(aE,a,{enumerable:!0,set:function(b){aD[a]=b,aF.forEach(function(a){return a(aE)})},get:function(){return aD[a]}})}),Object.defineProperty(aE,"familyPrefix",{enumerable:!0,set:function(a){aD.cssPrefix=a,aF.forEach(function(a){return a(aE)})},get:function(){return aD.cssPrefix}}),x.FontAwesomeConfig=aE;var aF=[],aG={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function aH(){for(var a=12,b="";a-- >0;)b+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return b}function aI(a){for(var b=[],c=(a||[]).length>>>0;c--;)b[c]=a[c];return b}function aJ(a){return a.classList?aI(a.classList):(a.getAttribute("class")||"").split(" ").filter(function(a){return a})}function aK(a){return"".concat(a).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function aL(a){return Object.keys(a||{}).reduce(function(b,c){return b+"".concat(c,": ").concat(a[c].trim(),";")},"")}function aM(a){return a.size!==aG.size||a.x!==aG.x||a.y!==aG.y||a.rotate!==aG.rotate||a.flipX||a.flipY}function aN(){var a=aE.cssPrefix,b=aE.replacementClass,c=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 7 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 7 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 7 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 7 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 7 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-slab-regular: normal 400 1em/1 "Font Awesome 7 Slab";\n  --fa-font-slab-press-regular: normal 400 1em/1 "Font Awesome 7 Slab Press";\n  --fa-font-whiteboard-semibold: normal 600 1em/1 "Font Awesome 7 Whiteboard";\n  --fa-font-thumbprint-light: normal 300 1em/1 "Font Awesome 7 Thumbprint";\n  --fa-font-notdog-solid: normal 900 1em/1 "Font Awesome 7 Notdog";\n  --fa-font-notdog-duo-solid: normal 900 1em/1 "Font Awesome 7 Notdog Duo";\n  --fa-font-etch-solid: normal 900 1em/1 "Font Awesome 7 Etch";\n  --fa-font-jelly-regular: normal 400 1em/1 "Font Awesome 7 Jelly";\n  --fa-font-jelly-fill-regular: normal 400 1em/1 "Font Awesome 7 Jelly Fill";\n  --fa-font-jelly-duo-regular: normal 400 1em/1 "Font Awesome 7 Jelly Duo";\n  --fa-font-chisel-regular: normal 400 1em/1 "Font Awesome 7 Chisel";\n}\n\n.svg-inline--fa {\n  box-sizing: content-box;\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285714em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left,\n.svg-inline--fa .fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-pull-right,\n.svg-inline--fa .fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  inset-block-start: 0.25em; /* syncing vertical alignment with Web Font rendering */\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.fa-layers .svg-inline--fa {\n  inset: 0;\n  margin: auto;\n  position: absolute;\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: calc(10 / 16 * 1em); /* converts a 10px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 10 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 10 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xs {\n  font-size: calc(12 / 16 * 1em); /* converts a 12px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 12 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 12 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-sm {\n  font-size: calc(14 / 16 * 1em); /* converts a 14px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 14 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 14 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-lg {\n  font-size: calc(20 / 16 * 1em); /* converts a 20px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 20 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 20 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xl {\n  font-size: calc(24 / 16 * 1em); /* converts a 24px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 24 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 24 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-2xl {\n  font-size: calc(32 / 16 * 1em); /* converts a 32px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 32 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 32 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-width-auto {\n  --fa-width: auto;\n}\n\n.fa-fw,\n.fa-width-fixed {\n  --fa-width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-inline-start: var(--fa-li-margin, 2.5em);\n  padding-inline-start: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n/* Heads Up: Bordered Icons will not be supported in the future!\n  - This feature will be deprecated in the next major release of Font Awesome (v8)!\n  - You may continue to use it in this version *v7), but it will not be supported in Font Awesome v8.\n*/\n/* Notes:\n* --@{v.$css-prefix}-border-width = 1/16 by default (to render as ~1px based on a 16px default font-size)\n* --@{v.$css-prefix}-border-padding =\n  ** 3/16 for vertical padding (to give ~2px of vertical whitespace around an icon considering it\'s vertical alignment)\n  ** 4/16 for horizontal padding (to give ~4px of horizontal whitespace around an icon)\n*/\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.0625em);\n  box-sizing: var(--fa-border-box-sizing, content-box);\n  padding: var(--fa-border-padding, 0.1875em 0.25em);\n}\n\n.fa-pull-left,\n.fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right,\n.fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n  .fa-bounce,\n  .fa-fade,\n  .fa-beat-fade,\n  .fa-flip,\n  .fa-pulse,\n  .fa-shake,\n  .fa-spin,\n  .fa-spin-pulse {\n    animation: none !important;\n    transition: none !important;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.svg-inline--fa.fa-inverse {\n  fill: var(--fa-inverse, #fff);\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  line-height: 2em;\n  position: relative;\n  vertical-align: middle;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}';if("fa"!==a||b!==ae){var d=RegExp("\\.".concat("fa","\\-"),"g"),e=RegExp("\\--".concat("fa","\\-"),"g"),f=RegExp("\\.".concat(ae),"g");c=c.replace(d,".".concat(a,"-")).replace(e,"--".concat(a,"-")).replace(f,".".concat(b))}return c}var aO=!1;function aP(){aE.autoAddCss&&!aO&&(!function(a){if(a&&B){var b=y.createElement("style");b.setAttribute("type","text/css"),b.innerHTML=a;for(var c=y.head.childNodes,d=null,e=c.length-1;e>-1;e--){var f=c[e];["STYLE","LINK"].indexOf((f.tagName||"").toUpperCase())>-1&&(d=f)}y.head.insertBefore(b,d)}}(aN()),aO=!0)}var aQ=x||{};aQ[ad]||(aQ[ad]={}),aQ[ad].styles||(aQ[ad].styles={}),aQ[ad].hooks||(aQ[ad].hooks={}),aQ[ad].shims||(aQ[ad].shims=[]);var aR=aQ[ad],aS=[],aT=function(){y.removeEventListener("DOMContentLoaded",aT),aU=1,aS.map(function(a){return a()})},aU=!1;function aV(a){var b,c=a.tag,d=a.attributes,e=a.children;return"string"==typeof a?aK(a):"<".concat(c," ").concat(Object.keys((b=void 0===d?{}:d)||{}).reduce(function(a,c){return a+"".concat(c,'="').concat(aK(b[c]),'" ')},"").trim(),">").concat((void 0===e?[]:e).map(aV).join(""),"</").concat(c,">")}function aW(a,b,c){if(a&&a[b]&&a[b][c])return{prefix:b,iconName:c,icon:a[b][c]}}B&&((aU=(y.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(y.readyState))||y.addEventListener("DOMContentLoaded",aT));var aX=function(a,b,c,d){var e,f,g,h=Object.keys(a),i=h.length,j=void 0!==d?function(a,c,e,f){return b.call(d,a,c,e,f)}:b;for(void 0===c?(e=1,g=a[h[0]]):(e=0,g=c);e<i;e++)g=j(g,a[f=h[e]],f,a);return g};function aY(a){return 1!==j(a).length?null:a.codePointAt(0).toString(16)}function aZ(a){return Object.keys(a).reduce(function(b,c){var d=a[c];return d.icon?b[d.iconName]=d.icon:b[c]=d,b},{})}var a$=aR.styles,a_=aR.shims,a0=Object.keys(at),a1=a0.reduce(function(a,b){return a[b]=Object.keys(at[b]),a},{}),a2=null,a3={},a4={},a5={},a6={},a7={},a8=function(){var a=function(a){return aX(a$,function(b,c,d){return b[d]=aX(c,a,{}),b},{})};a3=a(function(a,b,c){return b[3]&&(a[b[3]]=c),b[2]&&b[2].filter(function(a){return"number"==typeof a}).forEach(function(b){a[b.toString(16)]=c}),a}),a4=a(function(a,b,c){return a[c]=c,b[2]&&b[2].filter(function(a){return"string"==typeof a}).forEach(function(b){a[b]=c}),a}),a7=a(function(a,b,c){var d=b[2];return a[c]=c,d.forEach(function(b){a[b]=c}),a});var b="far"in a$||aE.autoFetchSvg,c=aX(a_,function(a,c){var d=c[0],e=c[1],f=c[2];return"far"!==e||b||(e="fas"),"string"==typeof d&&(a.names[d]={prefix:e,iconName:f}),"number"==typeof d&&(a.unicodes[d.toString(16)]={prefix:e,iconName:f}),a},{names:{},unicodes:{}});a5=c.names,a6=c.unicodes,a2=bd(aE.styleDefault,{family:aE.familyDefault})};function a9(a,b){return(a3[a]||{})[b]}function ba(a,b){return(a7[a]||{})[b]}function bb(a){return a5[a]||{prefix:null,iconName:null}}n=function(a){a2=bd(a.styleDefault,{family:aE.familyDefault})},aF.push(n),a8();var bc=function(){return{prefix:null,iconName:null,rest:[]}};function bd(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.family,d=void 0===c?F:c,e=ap[d][a];if(d===G&&!a)return"fad";var f=ar[d][a]||ar[d][e],g=a in aR.styles?a:null;return f||g||null}function be(a){return a.sort().filter(function(a,b,c){return c.indexOf(a)===b})}var bf=_.concat(W);function bg(a){var b,c,d,e,f,g,j,k,l,m,n,o,p,q,r,s,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},u=t.skipLookups,v=null,w=be(a.filter(function(a){return bf.includes(a)})),x=be(a.filter(function(a){return!bf.includes(a)})),y=i(w.filter(function(a){return v=a,!E.includes(a)}),1)[0],z=(p=F,q=a0.reduce(function(a,b){return a[b]="".concat(aE.cssPrefix,"-").concat(b),a},{}),U.forEach(function(a){(w.includes(q[a])||w.some(function(b){return a1[a].includes(b)}))&&(p=a)}),p),A=h(h({},(r=[],s=null,x.forEach(function(a){var b,c,d,e,f=(b=aE.cssPrefix,d=(c=a.split("-"))[0],e=c.slice(1).join("-"),d!==b||""===e||~aA.indexOf(e)?null:e);f?s=f:a&&r.push(a)}),{iconName:s,rest:r})),{},{prefix:bd(void 0===y?null:y,{family:z})});return h(h(h({},A),(c=(b={values:a,family:z,styles:a$,config:aE,canonical:A,givenPrefix:v}).values,d=b.family,e=b.canonical,f=b.givenPrefix,g=b.styles,k=void 0===(j=b.config)?{}:j,l=d===G,m=c.includes("fa-duotone")||c.includes("fad"),n="duotone"===k.familyDefault,o="fad"===e.prefix||"fa-duotone"===e.prefix,!l&&(m||n||o)&&(e.prefix="fad"),(c.includes("fa-brands")||c.includes("fab"))&&(e.prefix="fab"),!e.prefix&&bh.includes(d)&&(Object.keys(void 0===g?{}:g).find(function(a){return bi.includes(a)})||k.autoFetchSvg)&&(e.prefix=V.get(d).defaultShortPrefixId,e.iconName=ba(e.prefix,e.iconName)||e.iconName),("fa"===e.prefix||"fa"===(void 0===f?"":f))&&(e.prefix=a2||"fas"),e)),function(a,b,c){var d=c.prefix,e=c.iconName;if(a||!d||!e)return{prefix:d,iconName:e};var f="fa"===b?bb(e):{},g=ba(d,e);return e=f.iconName||g||e,"far"!==(d=f.prefix||d)||a$.far||!a$.fas||aE.autoFetchSvg||(d="fas"),{prefix:d,iconName:e}}(void 0!==u&&u,v,A))}var bh=U.filter(function(a){return a!==F||a!==G}),bi=Object.keys($).filter(function(a){return a!==F}).map(function(a){return Object.keys($[a])}).flat(),bj=function(){var a,b;return a=function a(){if(!(this instanceof a))throw TypeError("Cannot call a class as a function");this.definitions={}},b=[{key:"add",value:function(){for(var a=this,b=arguments.length,c=Array(b),d=0;d<b;d++)c[d]=arguments[d];var e=c.reduce(this._pullDefinitions,{});Object.keys(e).forEach(function(b){a.definitions[b]=h(h({},a.definitions[b]||{}),e[b]),function a(b,c){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},e=d.skipHooks,f=aZ(c);"function"!=typeof aR.hooks.addPack||void 0!==e&&e?aR.styles[b]=h(h({},aR.styles[b]||{}),f):aR.hooks.addPack(b,aZ(c)),"fas"===b&&a("fa",c)}(b,e[b]),a8()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(a,b){var c=b.prefix&&b.iconName&&b.icon?{0:b}:b;return Object.keys(c).map(function(b){var d=c[b],e=d.prefix,f=d.iconName,g=d.icon,h=g[2];a[e]||(a[e]={}),h.length>0&&h.forEach(function(b){"string"==typeof b&&(a[e][b]=g)}),a[e][f]=g}),a}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,k(d.key),d)}}(a.prototype,b),Object.defineProperty(a,"prototype",{writable:!1}),a}(),bk=[],bl={},bm={},bn=Object.keys(bm);function bo(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e];return(bl[a]||[]).forEach(function(a){b=a.apply(null,[b].concat(d))}),b}function bp(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];(bl[a]||[]).forEach(function(a){a.apply(null,c)})}function bq(){var a=arguments[0],b=Array.prototype.slice.call(arguments,1);return bm[a]?bm[a].apply(null,b):void 0}function br(a){"fa"===a.prefix&&(a.prefix="fas");var b=a.iconName,c=a.prefix||a2;if(b)return b=ba(c,b)||b,aW(bs.definitions,c,b)||aW(aR.styles,c,b)}var bs=new bj,bt={noAuto:function(){aE.autoReplaceSvg=!1,aE.observeMutations=!1,bp("noAuto")},config:aE,dom:{i2svg:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return B?(bp("beforeI2svg",a),bq("pseudoElements2svg",a),bq("i2svg",a)):Promise.reject(Error("Operation requires a DOM of some kind."))},watch:function(){var a,b=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},c=b.autoReplaceSvgRoot;!1===aE.autoReplaceSvg&&(aE.autoReplaceSvg=!0),aE.observeMutations=!0,a=function(){bu({autoReplaceSvgRoot:c}),bp("watch",b)},B&&(aU?setTimeout(a,0):aS.push(a))}},parse:{icon:function(a){if(null===a)return null;if("object"===l(a)&&a.prefix&&a.iconName)return{prefix:a.prefix,iconName:ba(a.prefix,a.iconName)||a.iconName};if(Array.isArray(a)&&2===a.length){var b=0===a[1].indexOf("fa-")?a[1].slice(3):a[1],c=bd(a[0]);return{prefix:c,iconName:ba(c,b)||b}}if("string"==typeof a&&(a.indexOf("".concat(aE.cssPrefix,"-"))>-1||a.match(av))){var d=bg(a.split(" "),{skipLookups:!0});return{prefix:d.prefix||a2,iconName:ba(d.prefix,d.iconName)||d.iconName}}if("string"==typeof a){var e=a2;return{prefix:e,iconName:ba(e,a)||a}}}},library:bs,findIconDefinition:br,toHtml:aV},bu=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},b=a.autoReplaceSvgRoot,c=void 0===b?y:b;(Object.keys(aR.styles).length>0||aE.autoFetchSvg)&&B&&aE.autoReplaceSvg&&bt.dom.i2svg({node:c})};function bv(a,b){return Object.defineProperty(a,"abstract",{get:b}),Object.defineProperty(a,"html",{get:function(){return a.abstract.map(function(a){return aV(a)})}}),Object.defineProperty(a,"node",{get:function(){if(B){var b=y.createElement("div");return b.innerHTML=a.html,b.children}}}),a}function bw(a){var b,c,d,e,f,g,i,j=a.icons,k=j.main,l=j.mask,m=a.prefix,n=a.iconName,o=a.transform,p=a.symbol,q=a.maskId,r=a.extra,s=a.watchable,t=l.found?l:k,u=t.width,v=t.height,w=[aE.replacementClass,n?"".concat(aE.cssPrefix,"-").concat(n):""].filter(function(a){return -1===r.classes.indexOf(a)}).filter(function(a){return""!==a||!!a}).concat(r.classes).join(" "),x={children:[],attributes:h(h({},r.attributes),{},{"data-prefix":m,"data-icon":n,class:w,role:r.attributes.role||"img",viewBox:"0 0 ".concat(u," ").concat(v)})};b=r.attributes,["aria-label","aria-labelledby","title","role"].some(function(a){return a in b})||r.attributes["aria-hidden"]||(x.attributes["aria-hidden"]="true"),void 0!==s&&s&&(x.attributes[af]="");var y=h(h({},x),{},{prefix:m,iconName:n,main:k,mask:l,maskId:q,transform:o,symbol:p,styles:h({},r.styles)}),z=l.found&&k.found?bq("generateAbstractMask",y)||{children:[],attributes:{}}:bq("generateAbstractIcon",y)||{children:[],attributes:{}},A=z.children,B=z.attributes;return(y.children=A,y.attributes=B,p)?(c=y.prefix,d=y.iconName,e=y.children,f=y.attributes,i=!0===(g=y.symbol)?"".concat(c,"-").concat(aE.cssPrefix,"-").concat(d):g,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:h(h({},f),{},{id:i}),children:e}]}]):function(a){var b=a.children,c=a.main,d=a.mask,e=a.attributes,f=a.styles,g=a.transform;if(aM(g)&&c.found&&!d.found){var i={x:c.width/c.height/2,y:.5};e.style=aL(h(h({},f),{},{"transform-origin":"".concat(i.x+g.x/16,"em ").concat(i.y+g.y/16,"em")}))}return[{tag:"svg",attributes:e,children:b}]}(y)}function bx(a){var b,c,d,e,f,g,i,j=a.content,k=a.width,l=a.height,m=a.transform,n=a.extra,o=a.watchable,p=h(h({},n.attributes),{},{class:n.classes.join(" ")});void 0!==o&&o&&(p[af]="");var q=h({},n.styles);aM(m)&&(c=(b={transform:m,startCentered:!0,width:k,height:l}).transform,d=b.width,e=b.height,g=void 0!==(f=b.startCentered)&&f,i="",g&&C?i+="translate(".concat(c.x/16-(void 0===d?16:d)/2,"em, ").concat(c.y/16-(void 0===e?16:e)/2,"em) "):g?i+="translate(calc(-50% + ".concat(c.x/16,"em), calc(-50% + ").concat(c.y/16,"em)) "):i+="translate(".concat(c.x/16,"em, ").concat(c.y/16,"em) "),i+="scale(".concat(c.size/16*(c.flipX?-1:1),", ").concat(c.size/16*(c.flipY?-1:1),") "),q.transform=i+="rotate(".concat(c.rotate,"deg) "),q["-webkit-transform"]=q.transform);var r=aL(q);r.length>0&&(p.style=r);var s=[];return s.push({tag:"span",attributes:p,children:[j]}),s}var by=aR.styles;function bz(a){var b=a[0],c=a[1],d=i(a.slice(4),1)[0];return{found:!0,width:b,height:c,icon:Array.isArray(d)?{tag:"g",attributes:{class:"".concat(aE.cssPrefix,"-").concat(az.GROUP)},children:[{tag:"path",attributes:{class:"".concat(aE.cssPrefix,"-").concat(az.SECONDARY),fill:"currentColor",d:d[0]}},{tag:"path",attributes:{class:"".concat(aE.cssPrefix,"-").concat(az.PRIMARY),fill:"currentColor",d:d[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:d}}}}var bA={found:!1,width:512,height:512};function bB(a,b){var c=b;return"fa"===b&&null!==aE.styleDefault&&(b=a2),new Promise(function(d,e){if("fa"===c){var f,g,i=bb(a)||{};a=i.iconName||a,b=i.prefix||b}if(a&&b&&by[b]&&by[b][a])return d(bz(by[b][a]));f=a,g=b,am||aE.showMissingIcons||!f||console.error('Icon with name "'.concat(f,'" and prefix "').concat(g,'" is missing.')),d(h(h({},bA),{},{icon:aE.showMissingIcons&&a&&bq("missingIconAbstract")||{}}))})}var bC=function(){},bD=aE.measurePerformance&&A&&A.mark&&A.measure?A:{mark:bC,measure:bC},bE='FA "7.0.0"',bF=function(a){bD.mark("".concat(bE," ").concat(a," ends")),bD.measure("".concat(bE," ").concat(a),"".concat(bE," ").concat(a," begins"),"".concat(bE," ").concat(a," ends"))},bG={begin:function(a){return bD.mark("".concat(bE," ").concat(a," begins")),function(){return bF(a)}}},bH=function(){};function bI(a){return"string"==typeof(a.getAttribute?a.getAttribute(af):null)}function bJ(a){return y.createElementNS("http://www.w3.org/2000/svg",a)}function bK(a){return y.createElement(a)}var bL={replace:function(a){var b=a[0];if(b.parentNode)if(a[1].forEach(function(a){b.parentNode.insertBefore(function a(b){var c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=c.ceFn,e=void 0===d?"svg"===b.tag?bJ:bK:d;if("string"==typeof b)return y.createTextNode(b);var f=e(b.tag);return Object.keys(b.attributes||[]).forEach(function(a){f.setAttribute(a,b.attributes[a])}),(b.children||[]).forEach(function(b){f.appendChild(a(b,{ceFn:e}))}),f}(a),b)}),null===b.getAttribute(af)&&aE.keepOriginalSource){var c,d=y.createComment((c=" ".concat(b.outerHTML," "),c="".concat(c,"Font Awesome fontawesome.com ")));b.parentNode.replaceChild(d,b)}else b.remove()},nest:function(a){var b=a[0],c=a[1];if(~aJ(b).indexOf(aE.replacementClass))return bL.replace(a);var d=new RegExp("".concat(aE.cssPrefix,"-.*"));if(delete c[0].attributes.id,c[0].attributes.class){var e=c[0].attributes.class.split(" ").reduce(function(a,b){return b===aE.replacementClass||b.match(d)?a.toSvg.push(b):a.toNode.push(b),a},{toNode:[],toSvg:[]});c[0].attributes.class=e.toSvg.join(" "),0===e.toNode.length?b.removeAttribute("class"):b.setAttribute("class",e.toNode.join(" "))}var f=c.map(function(a){return aV(a)}).join("\n");b.setAttribute(af,""),b.innerHTML=f}};function bM(a){a()}function bN(a,b){var c="function"==typeof b?b:bH;if(0===a.length)c();else{var d=bM;"async"===aE.mutateApproach&&(d=x.requestAnimationFrame||bM),d(function(){var b=!0===aE.autoReplaceSvg?bL.replace:bL[aE.autoReplaceSvg]||bL.replace,d=bG.begin("mutate");a.map(b),d(),c()})}}var bO=!1,bP=null;function bQ(a){if(z&&aE.observeMutations){var b=a.treeCallback,c=void 0===b?bH:b,d=a.nodeCallback,e=void 0===d?bH:d,f=a.pseudoElementsCallback,g=void 0===f?bH:f,h=a.observeMutationsRoot,i=void 0===h?y:h;bP=new z(function(a){if(!bO){var b=a2;aI(a).forEach(function(a){if("childList"===a.type&&a.addedNodes.length>0&&!bI(a.addedNodes[0])&&(aE.searchPseudoElements&&g(a.target),c(a.target)),"attributes"===a.type&&a.target.parentNode&&aE.searchPseudoElements&&g([a.target],!0),"attributes"===a.type&&bI(a.target)&&~ay.indexOf(a.attributeName))if("class"===a.attributeName&&(f=(d=a.target).getAttribute?d.getAttribute(ah):null,h=d.getAttribute?d.getAttribute(ai):null,f&&h)){var d,f,h,i,j=bg(aJ(a.target)),k=j.prefix,l=j.iconName;a.target.setAttribute(ah,k||b),l&&a.target.setAttribute(ai,l)}else(i=a.target)&&i.classList&&i.classList.contains&&i.classList.contains(aE.replacementClass)&&e(a.target)})}}),B&&bP.observe(i,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function bR(a){var b,c,d,e,f,g,i,j,k,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},m=(e=(b=a).getAttribute("data-prefix"),f=b.getAttribute("data-icon"),g=void 0!==b.innerText?b.innerText.trim():"",((i=bg(aJ(b))).prefix||(i.prefix=a2),e&&f&&(i.prefix=e,i.iconName=f),i.iconName&&i.prefix)?i:(i.prefix&&g.length>0&&(c=i.prefix,d=b.innerText,i.iconName=(a4[c]||{})[d]||a9(i.prefix,aY(b.innerText))),!i.iconName&&aE.autoFetchSvg&&b.firstChild&&b.firstChild.nodeType===Node.TEXT_NODE&&(i.iconName=b.firstChild.data),i)),n=m.iconName,o=m.prefix,p=m.rest,q=aI(a.attributes).reduce(function(a,b){return"class"!==a.name&&"style"!==a.name&&(a[b.name]=b.value),a},{}),r=bo("parseNodeAttributes",{},a);return h({iconName:n,prefix:o,transform:aG,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:p,styles:l.styleParser?(j=a.getAttribute("style"),k=[],j&&(k=j.split(";").reduce(function(a,b){var c=b.split(":"),d=c[0],e=c.slice(1);return d&&e.length>0&&(a[d]=e.join(":").trim()),a},{})),k):[],attributes:q}},r)}var bS=aR.styles;function bT(a){var b="nest"===aE.autoReplaceSvg?bR(a,{styleParser:!1}):bR(a);return~b.extra.classes.indexOf(aw)?bq("generateLayersText",a,b):bq("generateSvgReplacementMutation",a,b)}function bU(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!B)return Promise.resolve();var c=y.documentElement.classList,d=function(a){return c.add("".concat(aj,"-").concat(a))},e=function(a){return c.remove("".concat(aj,"-").concat(a))},f=aE.autoFetchSvg?[].concat(j(W),j(_)):E.concat(Object.keys(bS));f.includes("fa")||f.push("fa");var g=[".".concat(aw,":not([").concat(af,"])")].concat(f.map(function(a){return".".concat(a,":not([").concat(af,"])")})).join(", ");if(0===g.length)return Promise.resolve();var h=[];try{h=aI(a.querySelectorAll(g))}catch(a){}if(!(h.length>0))return Promise.resolve();d("pending"),e("complete");var i=bG.begin("onTree"),k=h.reduce(function(a,b){try{var c=bT(b);c&&a.push(c)}catch(a){am||"MissingIcon"!==a.name||console.error(a)}return a},[]);return new Promise(function(a,c){Promise.all(k).then(function(c){bN(c,function(){d("active"),d("complete"),e("pending"),"function"==typeof b&&b(),i(),a()})}).catch(function(a){i(),c(a)})})}function bV(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;bT(a).then(function(a){a&&bN([a],b)})}var bW=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.transform,d=void 0===c?aG:c,e=b.symbol,f=void 0!==e&&e,g=b.mask,i=void 0===g?null:g,j=b.maskId,k=void 0===j?null:j,l=b.classes,m=void 0===l?[]:l,n=b.attributes,o=void 0===n?{}:n,p=b.styles,q=void 0===p?{}:p;if(a){var r=a.prefix,s=a.iconName,t=a.icon;return bv(h({type:"icon"},a),function(){return bp("beforeDOMElementCreation",{iconDefinition:a,params:b}),bw({icons:{main:bz(t),mask:i?bz(i.icon):{found:!1,width:null,height:null,icon:{}}},prefix:r,iconName:s,transform:h(h({},aG),d),symbol:f,maskId:k,extra:{attributes:o,styles:q,classes:m}})})}},bX=RegExp('"',"ug"),bY=h(h(h(h({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 7 Free":{900:"fas",400:"far"},"Font Awesome 7 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 7 Brands":{400:"fab",normal:"fab"},"Font Awesome 7 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 7 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 7 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"},"Font Awesome 7 Jelly":{400:"fajr",normal:"fajr"},"Font Awesome 7 Jelly Fill":{400:"fajfr",normal:"fajfr"},"Font Awesome 7 Jelly Duo":{400:"fajdr",normal:"fajdr"},"Font Awesome 7 Slab":{400:"faslr",normal:"faslr"},"Font Awesome 7 Slab Press":{400:"faslpr",normal:"faslpr"},"Font Awesome 7 Thumbprint":{300:"fatl",normal:"fatl"},"Font Awesome 7 Notdog":{900:"fans",normal:"fans"},"Font Awesome 7 Notdog Duo":{900:"fands",normal:"fands"},"Font Awesome 7 Etch":{900:"faes",normal:"faes"},"Font Awesome 7 Chisel":{400:"facr",normal:"facr"},"Font Awesome 7 Whiteboard":{600:"fawsb",normal:"fawsb"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),bZ=Object.keys(bY).reduce(function(a,b){return a[b.toLowerCase()]=bY[b],a},{}),b$=Object.keys(bZ).reduce(function(a,b){var c=bZ[b];return a[b]=c[900]||j(Object.entries(c))[0][1],a},{});function b_(a,b){var c="".concat("data-fa-pseudo-element-pending").concat(b.replace(":","-"));return new Promise(function(d,e){if(null!==a.getAttribute(c))return d();var f=aI(a.children).filter(function(a){return a.getAttribute(ag)===b})[0],g=x.getComputedStyle(a,b),i=g.getPropertyValue("font-family"),k=i.match(ax),l=g.getPropertyValue("font-weight"),m=g.getPropertyValue("content");if(f&&!k)return a.removeChild(f),d();if(k&&"none"!==m&&""!==m){var n=g.getPropertyValue("content"),o=(u=i.replace(/^['"]|['"]$/g,"").toLowerCase(),w=isNaN(v=parseInt(l))?"normal":v,(bZ[u]||{})[w]||b$[u]),p=aY(j(n.replace(bX,""))[0]||""),q=k[0].startsWith("FontAwesome"),r=(z=g.getPropertyValue("font-feature-settings").includes("ss01"),C=(B=(A=g.getPropertyValue("content").replace(bX,"")).codePointAt(0))>=1105920&&B<=1112319,D=2===A.length&&A[0]===A[1],C||D||z),s=a9(o,p),t=s;if(q){var u,v,w,z,A,B,C,D,E,F,G=(E=a6[p],F=a9("fas",p),E||(F?{prefix:"fas",iconName:F}:null)||{prefix:null,iconName:null});G.iconName&&G.prefix&&(s=G.iconName,o=G.prefix)}if(!s||r||f&&f.getAttribute(ah)===o&&f.getAttribute(ai)===t)d();else{a.setAttribute(c,t),f&&a.removeChild(f);var H={iconName:null,prefix:null,transform:aG,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},I=H.extra;I.attributes[ag]=b,bB(s,o).then(function(e){var f=bw(h(h({},H),{},{icons:{main:e,mask:bc()},prefix:o,iconName:t,extra:I,watchable:!0})),g=y.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===b?a.insertBefore(g,a.firstChild):a.appendChild(g),g.outerHTML=f.map(function(a){return aV(a)}).join("\n"),a.removeAttribute(c),d()}).catch(e)}}else d()})}function b0(a){return Promise.all([b_(a,"::before"),b_(a,"::after")])}function b1(a){return a.parentNode!==document.head&&!~ak.indexOf(a.tagName.toUpperCase())&&!a.getAttribute(ag)&&(!a.parentNode||"svg"!==a.parentNode.tagName)}var b2=function(a){return!!a&&al.some(function(b){return a.includes(b)})},b3=function(a){if(!a)return[];for(var b=new Set,c=[a],d=0,f=[/(?=\s:)/,/(?<=\)\)?[^,]*,)/];d<f.length;d++)!function(){var a=f[d];c=c.flatMap(function(b){return b.split(a).map(function(a){return a.replace(/,\s*$/,"").trim()})})}();var g,h=e(c=c.flatMap(function(a){return a.includes("(")?a:a.split(",").map(function(a){return a.trim()})}));try{for(h.s();!(g=h.n()).done;){var i=g.value;if(b2(i)){var j=al.reduce(function(a,b){return a.replace(b,"")},i);""!==j&&"*"!==j&&b.add(j)}}}catch(a){h.e(a)}finally{h.f()}return b};function b4(a){var b=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(B){if(b)c=a;else if(aE.searchPseudoElementsFullScan)c=a.querySelectorAll("*");else{var c,d,f=new Set,g=e(document.styleSheets);try{for(g.s();!(d=g.n()).done;){var h=d.value;try{var i,j=e(h.cssRules);try{for(j.s();!(i=j.n()).done;){var k,l=i.value,m=b3(l.selectorText),n=e(m);try{for(n.s();!(k=n.n()).done;){var o=k.value;f.add(o)}}catch(a){n.e(a)}finally{n.f()}}}catch(a){j.e(a)}finally{j.f()}}catch(a){aE.searchPseudoElementsWarnings&&console.warn("Font Awesome: cannot parse stylesheet: ".concat(h.href," (").concat(a.message,')\nIf it declares any Font Awesome CSS pseudo-elements, they will not be rendered as SVG icons. Add crossorigin="anonymous" to the <link>, enable searchPseudoElementsFullScan for slower but more thorough DOM parsing, or suppress this warning by setting searchPseudoElementsWarnings to false.'))}}}catch(a){g.e(a)}finally{g.f()}if(!f.size)return;var p=Array.from(f).join(", ");try{c=a.querySelectorAll(p)}catch(a){}}return new Promise(function(a,b){var d=aI(c).filter(b1).map(b0),e=bG.begin("searchPseudoElements");bO=!0,Promise.all(d).then(function(){e(),bO=!1,a()}).catch(function(){e(),bO=!1,b()})})}}var b5=!1,b6=function(a){return a.toLowerCase().split(" ").reduce(function(a,b){var c=b.toLowerCase().split("-"),d=c[0],e=c.slice(1).join("-");if(d&&"h"===e)return a.flipX=!0,a;if(d&&"v"===e)return a.flipY=!0,a;if(isNaN(e=parseFloat(e)))return a;switch(d){case"grow":a.size=a.size+e;break;case"shrink":a.size=a.size-e;break;case"left":a.x=a.x-e;break;case"right":a.x=a.x+e;break;case"up":a.y=a.y-e;break;case"down":a.y=a.y+e;break;case"rotate":a.rotate=a.rotate+e}return a},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})},b7={x:0,y:0,width:"100%",height:"100%"};function b8(a){var b=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return a.attributes&&(a.attributes.fill||b)&&(a.attributes.fill="black"),a}!function(a,b){var c=b.mixoutsTo;bk=a,bl={},Object.keys(bm).forEach(function(a){-1===bn.indexOf(a)&&delete bm[a]}),bk.forEach(function(a){var b=a.mixout?a.mixout():{};if(Object.keys(b).forEach(function(a){"function"==typeof b[a]&&(c[a]=b[a]),"object"===l(b[a])&&Object.keys(b[a]).forEach(function(d){c[a]||(c[a]={}),c[a][d]=b[a][d]})}),a.hooks){var d=a.hooks();Object.keys(d).forEach(function(a){bl[a]||(bl[a]=[]),bl[a].push(d[a])})}a.provides&&a.provides(bm)})}([{mixout:function(){return{dom:{css:aN,insertCss:aP}}},hooks:function(){return{beforeDOMElementCreation:function(){aP()},beforeI2svg:function(){aP()}}}},{mixout:function(){return{icon:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=(a||{}).icon?a:br(a||{}),d=b.mask;return d&&(d=(d||{}).icon?d:br(d||{})),bW(c,h(h({},b),{},{mask:d}))}}},hooks:function(){return{mutationObserverCallbacks:function(a){return a.treeCallback=bU,a.nodeCallback=bV,a}}},provides:function(a){a.i2svg=function(a){var b=a.node,c=void 0===b?y:b,d=a.callback;return bU(c,void 0===d?function(){}:d)},a.generateSvgReplacementMutation=function(a,b){var c=b.iconName,d=b.prefix,e=b.transform,f=b.symbol,g=b.mask,h=b.maskId,j=b.extra;return new Promise(function(b,k){Promise.all([bB(c,d),g.iconName?bB(g.iconName,g.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(function(g){var k=i(g,2);b([a,bw({icons:{main:k[0],mask:k[1]},prefix:d,iconName:c,transform:e,symbol:f,maskId:h,extra:j,watchable:!0})])}).catch(k)})},a.generateAbstractIcon=function(a){var b,c=a.children,d=a.attributes,e=a.main,f=a.transform,g=aL(a.styles);return g.length>0&&(d.style=g),aM(f)&&(b=bq("generateAbstractTransformGrouping",{main:e,transform:f,containerWidth:e.width,iconWidth:e.width})),c.push(b||e.icon),{children:c,attributes:d}}}},{mixout:function(){return{layer:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.classes,d=void 0===c?[]:c;return bv({type:"layer"},function(){bp("beforeDOMElementCreation",{assembler:a,params:b});var c=[];return a(function(a){Array.isArray(a)?a.map(function(a){c=c.concat(a.abstract)}):c=c.concat(a.abstract)}),[{tag:"span",attributes:{class:["".concat(aE.cssPrefix,"-layers")].concat(j(d)).join(" ")},children:c}]})}}}},{mixout:function(){return{counter:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.title,d=void 0===c?null:c,e=b.classes,f=void 0===e?[]:e,g=b.attributes,i=void 0===g?{}:g,k=b.styles,l=void 0===k?{}:k;return bv({type:"counter",content:a},function(){var c,e,g,k,m,n;return bp("beforeDOMElementCreation",{content:a,params:b}),e=(c={content:a.toString(),title:d,extra:{attributes:i,styles:l,classes:["".concat(aE.cssPrefix,"-layers-counter")].concat(j(f))}}).content,k=h(h({},(g=c.extra).attributes),{},{class:g.classes.join(" ")}),(m=aL(g.styles)).length>0&&(k.style=m),(n=[]).push({tag:"span",attributes:k,children:[e]}),n})}}}},{mixout:function(){return{text:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.transform,d=void 0===c?aG:c,e=b.classes,f=void 0===e?[]:e,g=b.attributes,i=void 0===g?{}:g,k=b.styles,l=void 0===k?{}:k;return bv({type:"text",content:a},function(){return bp("beforeDOMElementCreation",{content:a,params:b}),bx({content:a,transform:h(h({},aG),d),extra:{attributes:i,styles:l,classes:["".concat(aE.cssPrefix,"-layers-text")].concat(j(f))}})})}}},provides:function(a){a.generateLayersText=function(a,b){var c=b.transform,d=b.extra,e=null,f=null;if(C){var g=parseInt(getComputedStyle(a).fontSize,10),h=a.getBoundingClientRect();e=h.width/g,f=h.height/g}return Promise.resolve([a,bx({content:a.innerHTML,width:e,height:f,transform:c,extra:d,watchable:!0})])}}},{hooks:function(){return{mutationObserverCallbacks:function(a){return a.pseudoElementsCallback=b4,a}}},provides:function(a){a.pseudoElements2svg=function(a){var b=a.node,c=void 0===b?y:b;aE.searchPseudoElements&&b4(c)}}},{mixout:function(){return{dom:{unwatch:function(){bO=!0,b5=!0}}}},hooks:function(){return{bootstrap:function(){bQ(bo("mutationObserverCallbacks",{}))},noAuto:function(){bP&&bP.disconnect()},watch:function(a){var b=a.observeMutationsRoot;b5?bO=!1:bQ(bo("mutationObserverCallbacks",{observeMutationsRoot:b}))}}}},{mixout:function(){return{parse:{transform:function(a){return b6(a)}}}},hooks:function(){return{parseNodeAttributes:function(a,b){var c=b.getAttribute("data-fa-transform");return c&&(a.transform=b6(c)),a}}},provides:function(a){a.generateAbstractTransformGrouping=function(a){var b=a.main,c=a.transform,d=a.containerWidth,e=a.iconWidth,f="translate(".concat(32*c.x,", ").concat(32*c.y,") "),g="scale(".concat(c.size/16*(c.flipX?-1:1),", ").concat(c.size/16*(c.flipY?-1:1),") "),i="rotate(".concat(c.rotate," 0 0)"),j={transform:"".concat(f," ").concat(g," ").concat(i)},k={outer:{transform:"translate(".concat(d/2," 256)")},inner:j,path:{transform:"translate(".concat(-(e/2*1)," -256)")}};return{tag:"g",attributes:h({},k.outer),children:[{tag:"g",attributes:h({},k.inner),children:[{tag:b.icon.tag,children:b.icon.children,attributes:h(h({},b.icon.attributes),k.path)}]}]}}}},{hooks:function(){return{parseNodeAttributes:function(a,b){var c=b.getAttribute("data-fa-mask"),d=c?bg(c.split(" ").map(function(a){return a.trim()})):bc();return d.prefix||(d.prefix=a2),a.mask=d,a.maskId=b.getAttribute("data-fa-mask-id"),a}}},provides:function(a){a.generateAbstractMask=function(a){var b,c,d,e,f,g,i,j,k=a.children,l=a.attributes,m=a.main,n=a.mask,o=a.maskId,p=a.transform,q=m.width,r=m.icon,s=n.width,t=n.icon,u=(c=(b={transform:p,containerWidth:s,iconWidth:q}).transform,d=b.containerWidth,e=b.iconWidth,f="translate(".concat(32*c.x,", ").concat(32*c.y,") "),g="scale(".concat(c.size/16*(c.flipX?-1:1),", ").concat(c.size/16*(c.flipY?-1:1),") "),i="rotate(".concat(c.rotate," 0 0)"),j={transform:"".concat(f," ").concat(g," ").concat(i)},{outer:{transform:"translate(".concat(d/2," 256)")},inner:j,path:{transform:"translate(".concat(-(e/2*1)," -256)")}}),v={tag:"rect",attributes:h(h({},b7),{},{fill:"white"})},w=r.children?{children:r.children.map(b8)}:{},x={tag:"g",attributes:h({},u.inner),children:[b8(h({tag:r.tag,attributes:h(h({},r.attributes),u.path)},w))]},y={tag:"g",attributes:h({},u.outer),children:[x]},z="mask-".concat(o||aH()),A="clip-".concat(o||aH()),B={tag:"mask",attributes:h(h({},b7),{},{id:z,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[v,y]},C={tag:"defs",children:[{tag:"clipPath",attributes:{id:A},children:"g"===t.tag?t.children:[t]},B]};return k.push(C,{tag:"rect",attributes:h({fill:"currentColor","clip-path":"url(#".concat(A,")"),mask:"url(#".concat(z,")")},b7)}),{children:k,attributes:l}}}},{provides:function(a){var b=!1;x.matchMedia&&(b=x.matchMedia("(prefers-reduced-motion: reduce)").matches),a.missingIconAbstract=function(){var a=[],c={fill:"currentColor"},d={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};a.push({tag:"path",attributes:h(h({},c),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var e=h(h({},d),{},{attributeName:"opacity"}),f={tag:"circle",attributes:h(h({},c),{},{cx:"256",cy:"364",r:"28"}),children:[]};return b||f.children.push({tag:"animate",attributes:h(h({},d),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:h(h({},e),{},{values:"1;0;1;1;0;1;"})}),a.push(f),a.push({tag:"path",attributes:h(h({},c),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:b?[]:[{tag:"animate",attributes:h(h({},e),{},{values:"1;0;0;0;0;1;"})}]}),b||a.push({tag:"path",attributes:h(h({},c),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:h(h({},e),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:a}}}},{hooks:function(){return{parseNodeAttributes:function(a,b){var c=b.getAttribute("data-fa-symbol");return a.symbol=null!==c&&(""===c||c),a}}}}],{mixoutsTo:bt}),bt.noAuto,bt.config;var b9=bt.library;bt.dom;var ca=bt.parse;bt.findIconDefinition,bt.toHtml;var cb=bt.icon;bt.layer,bt.text,bt.counter},84031:(a,b,c)=>{"use strict";var d=c(34452);function e(){}function f(){}f.resetWarningCache=e,a.exports=function(){function a(a,b,c,e,f,g){if(g!==d){var h=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}function b(){return a}a.isRequired=a;var c={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:b,element:a,elementType:a,instanceOf:b,node:a,objectOf:b,oneOf:b,oneOfType:b,shape:b,exact:b,checkPropTypes:f,resetWarningCache:e};return c.PropTypes=c,c}},87955:(a,b,c)=>{a.exports=c(84031)()},96999:a=>{"use strict";a.exports=JSON.parse('{"description":"The iconic font, CSS, and SVG framework","keywords":["font","awesome","fontawesome","icon","svg","bootstrap"],"homepage":"https://fontawesome.com","bugs":{"url":"https://github.com/FortAwesome/Font-Awesome/issues"},"author":"The Font Awesome Team (https://github.com/orgs/FortAwesome/people)","repository":{"type":"git","url":"https://github.com/FortAwesome/Font-Awesome"},"engines":{"node":">=6"},"dependencies":{"@fortawesome/fontawesome-common-types":"7.0.0"},"version":"7.0.0","name":"@fortawesome/fontawesome-svg-core","main":"index.js","module":"index.mjs","jsnext:main":"index.mjs","style":"styles.css","license":"MIT","types":"./index.d.ts","exports":{".":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","style":"./styles.css","default":"./index.js"},"./index":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","default":"./index.js"},"./index.js":{"types":"./index.d.ts","module":"./index.mjs","import":"./index.mjs","require":"./index.js","default":"./index.js"},"./plugins":{"types":"./index.d.ts","module":"./plugins.mjs","import":"./plugins.mjs","default":"./plugins.mjs"},"./import.macro":"./import.macro.js","./import.macro.js":"./import.macro.js","./styles":"./styles.css","./styles.css":"./styles.css","./package.json":"./package.json"},"sideEffects":["./index.js","./index.mjs","./styles.css"]}')},97e3:(a,b,c)=>{"use strict";c.d(b,{$Fj:()=>Y,A4h:()=>U,AaJ:()=>i,ArK:()=>aH,B9e:()=>d,Bwz:()=>au,CQO:()=>P,CYF:()=>aL,Cyq:()=>J,D6w:()=>aa,DW4:()=>ar,DX_:()=>ah,G06:()=>ac,GRI:()=>ad,GrJ:()=>g,GxD:()=>T,Hzw:()=>f,ITF:()=>aj,Iae:()=>q,JmV:()=>av,Jt$:()=>ak,KKb:()=>m,KMJ:()=>x,KTq:()=>F,LFz:()=>u,LPI:()=>at,MT7:()=>v,MjD:()=>j,Pcr:()=>aI,Q9Y:()=>aG,QLR:()=>ax,R70:()=>V,TBz:()=>aK,TJL:()=>I,U23:()=>aF,Uj9:()=>z,V2x:()=>aP,Vpu:()=>aM,Wzs:()=>am,X46:()=>ab,XaT:()=>aw,XkK:()=>A,YBv:()=>G,YpS:()=>aA,_eQ:()=>n,a$:()=>w,ao0:()=>N,cbP:()=>as,ckx:()=>aJ,dB:()=>R,e4L:()=>al,e5w:()=>M,e68:()=>X,eST:()=>aq,fyG:()=>l,gr3:()=>aB,h8M:()=>af,hSh:()=>k,hem:()=>C,iHh:()=>h,iW_:()=>aO,jBL:()=>O,jPR:()=>ay,jTw:()=>L,kNw:()=>B,l9V:()=>aN,mRM:()=>e,n2W:()=>E,nET:()=>_,oZK:()=>s,okg:()=>W,p1w:()=>aE,pS3:()=>o,pvD:()=>Q,qcK:()=>y,rC2:()=>K,ruc:()=>aC,s6x:()=>ao,t5Z:()=>D,v02:()=>Z,vZS:()=>ag,vaG:()=>t,w2A:()=>r,w7B:()=>ai,wRm:()=>S,yLS:()=>p,yek:()=>az,yy:()=>an,z1G:()=>$,zm_:()=>aD,zpE:()=>ap,zqi:()=>H});var d={prefix:"fas",iconName:"medal",icon:[448,512,[127941],"f5a2","M224.3 128L139.7-12.9c-6.5-10.8-20.1-14.7-31.3-9.1L21.8 21.3C9.9 27.2 5.1 41.6 11 53.5L80.6 192.6c-30.1 33.9-48.3 78.5-48.3 127.4 0 106 86 192 192 192s192-86 192-192c0-48.9-18.3-93.5-48.3-127.4L437.6 53.5c5.9-11.9 1.1-26.3-10.7-32.2L340.2-22.1c-11.2-5.6-24.9-1.6-31.3 9.1L224.3 128zm30.8 142.5c1.4 2.8 4 4.7 7 5.1l50.1 7.3c7.7 1.1 10.7 10.5 5.2 16l-36.3 35.4c-2.2 2.2-3.2 5.2-2.7 8.3l8.6 49.9c1.3 7.6-6.7 13.5-13.6 9.9l-44.8-23.6c-2.7-1.4-6-1.4-8.7 0l-44.8 23.6c-6.9 3.6-14.9-2.2-13.6-9.9l8.6-49.9c.5-3-.5-6.1-2.7-8.3l-36.3-35.4c-5.6-5.4-2.5-14.8 5.2-16l50.1-7.3c3-.4 5.7-2.4 7-5.1l22.4-45.4c3.4-7 13.3-7 16.8 0l22.4 45.4z"]},e={prefix:"fas",iconName:"filter",icon:[512,512,[],"f0b0","M32 64C19.1 64 7.4 71.8 2.4 83.8S.2 109.5 9.4 118.6L192 301.3 192 416c0 8.5 3.4 16.6 9.4 22.6l64 64c9.2 9.2 22.9 11.9 34.9 6.9S320 492.9 320 480l0-178.7 182.6-182.6c9.2-9.2 11.9-22.9 6.9-34.9S492.9 64 480 64L32 64z"]},f={prefix:"fas",iconName:"at",icon:[512,512,[61946],"40","M256 64C150 64 64 150 64 256s86 192 192 192c17.7 0 32 14.3 32 32s-14.3 32-32 32C114.6 512 0 397.4 0 256S114.6 0 256 0 512 114.6 512 256l0 32c0 53-43 96-96 96-29.3 0-55.6-13.2-73.2-33.9-22.8 21-53.3 33.9-86.8 33.9-70.7 0-128-57.3-128-128s57.3-128 128-128c27.9 0 53.7 8.9 74.7 24.1 5.7-5 13.1-8.1 21.3-8.1 17.7 0 32 14.3 32 32l0 112c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32c0-106-86-192-192-192zm64 192a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z"]},g={prefix:"fas",iconName:"mobile",icon:[384,512,[128241,"mobile-android","mobile-phone"],"f3ce","M80 0C44.7 0 16 28.7 16 64l0 384c0 35.3 28.7 64 64 64l224 0c35.3 0 64-28.7 64-64l0-384c0-35.3-28.7-64-64-64L80 0zm72 416l80 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24z"]},h={prefix:"fas",iconName:"trophy",icon:[512,512,[127942],"f091","M144.3 0l224 0c26.5 0 48.1 21.8 47.1 48.2-.2 5.3-.4 10.6-.7 15.8l49.6 0c26.1 0 49.1 21.6 47.1 49.8-7.5 103.7-60.5 160.7-118 190.5-15.8 8.2-31.9 14.3-47.2 18.8-20.2 28.6-41.2 43.7-57.9 51.8l0 73.1 64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-192 0c-17.7 0-32-14.3-32-32s14.3-32 32-32l64 0 0-73.1c-16-7.7-35.9-22-55.3-48.3-18.4-4.8-38.4-12.1-57.9-23.1-54.1-30.3-102.9-87.4-109.9-189.9-1.9-28.1 21-49.7 47.1-49.7l49.6 0c-.3-5.2-.5-10.4-.7-15.8-1-26.5 20.6-48.2 47.1-48.2zM101.5 112l-52.4 0c6.2 84.7 45.1 127.1 85.2 149.6-14.4-37.3-26.3-86-32.8-149.6zM380 256.8c40.5-23.8 77.1-66.1 83.3-144.8L411 112c-6.2 60.9-17.4 108.2-31 144.8z"]},i={prefix:"fas",iconName:"up-right-from-square",icon:[512,512,["external-link-alt"],"f35d","M290.4 19.8C295.4 7.8 307.1 0 320 0L480 0c17.7 0 32 14.3 32 32l0 160c0 12.9-7.8 24.6-19.8 29.6s-25.7 2.2-34.9-6.9L400 157.3 246.6 310.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L354.7 112 297.4 54.6c-9.2-9.2-11.9-22.9-6.9-34.9zM0 176c0-44.2 35.8-80 80-80l80 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-80 0c-8.8 0-16 7.2-16 16l0 256c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-80c0-17.7 14.3-32 32-32s32 14.3 32 32l0 80c0 44.2-35.8 80-80 80L80 512c-44.2 0-80-35.8-80-80L0 176z"]},j={prefix:"fas",iconName:"magnifying-glass",icon:[512,512,[128269,"search"],"f002","M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376C296.3 401.1 253.9 416 208 416 93.1 416 0 322.9 0 208S93.1 0 208 0 416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"]},k={prefix:"fas",iconName:"floppy-disk",icon:[448,512,[128190,128426,"save"],"f0c7","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-242.7c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32L64 32zm32 96c0-17.7 14.3-32 32-32l160 0c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32l-160 0c-17.7 0-32-14.3-32-32l0-64zM224 288a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"]},l={prefix:"fas",iconName:"palette",icon:[512,512,[127912],"f53f","M512 256c0 .9 0 1.8 0 2.7-.4 36.5-33.6 61.3-70.1 61.3L344 320c-26.5 0-48 21.5-48 48 0 3.4 .4 6.7 1 9.9 2.1 10.2 6.5 20 10.8 29.9 6.1 13.8 12.1 27.5 12.1 42 0 31.8-21.6 60.7-53.4 62-3.5 .1-7 .2-10.6 .2-141.4 0-256-114.6-256-256S114.6 0 256 0 512 114.6 512 256zM128 288a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm0-96a32 32 0 1 0 0-64 32 32 0 1 0 0 64zM288 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm96 96a32 32 0 1 0 0-64 32 32 0 1 0 0 64z"]},m={prefix:"fas",iconName:"unlock",icon:[384,512,[128275],"f09c","M128 96c0-35.3 28.7-64 64-64 31.7 0 58 23 63.1 53.3 2.9 17.4 19.4 29.2 36.9 26.3s29.2-19.4 26.3-36.9C308.1 14.1 255.5-32 192-32 121.3-32 64 25.3 64 96l0 64c-35.3 0-64 28.7-64 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-224c0-35.3-28.7-64-64-64l-192 0 0-64z"]},n={prefix:"fas",iconName:"paintbrush",icon:[576,512,[128396,"paint-brush"],"f1fc","M480.5 10.3L259.1 158c-29.1 19.4-47.6 50.9-50.6 85.3 62.3 12.8 111.4 61.9 124.3 124.3 34.5-3 65.9-21.5 85.3-50.6L565.7 95.5c6.7-10.1 10.3-21.9 10.3-34.1 0-33.9-27.5-61.4-61.4-61.4-12.1 0-24 3.6-34.1 10.3zM288 400c0-61.9-50.1-112-112-112S64 338.1 64 400c0 3.9 .2 7.8 .6 11.6 1.8 17.5-10.2 36.4-27.8 36.4L32 448c-17.7 0-32 14.3-32 32s14.3 32 32 32l144 0c61.9 0 112-50.1 112-112z"]},o={prefix:"fas",iconName:"eye",icon:[576,512,[128065],"f06e","M288 32c-80.8 0-145.5 36.8-192.6 80.6-46.8 43.5-78.1 95.4-93 131.1-3.3 7.9-3.3 16.7 0 24.6 14.9 35.7 46.2 87.7 93 131.1 47.1 43.7 111.8 80.6 192.6 80.6s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1 3.3-7.9 3.3-16.7 0-24.6-14.9-35.7-46.2-87.7-93-131.1-47.1-43.7-111.8-80.6-192.6-80.6zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64-11.5 0-22.3-3-31.7-8.4-1 10.9-.1 22.1 2.9 33.2 13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-12.2-45.7-55.5-74.8-101.1-70.8 5.3 9.3 8.4 20.1 8.4 31.7z"]},p={prefix:"fas",iconName:"trash",icon:[448,512,[],"f1f8","M136.7 5.9L128 32 32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l384 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0-8.7-26.1C306.9-7.2 294.7-16 280.9-16L167.1-16c-13.8 0-26 8.8-30.4 21.9zM416 144L32 144 53.1 467.1C54.7 492.4 75.7 512 101 512L347 512c25.3 0 46.3-19.6 47.9-44.9L416 144z"]},q={prefix:"fas",iconName:"clipboard-check",icon:[384,512,[],"f46c","M311.4 32l8.6 0c35.3 0 64 28.7 64 64l0 352c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l8.6 0C83.6 12.9 104.3 0 128 0L256 0c23.7 0 44.4 12.9 55.4 32zM248 112c13.3 0 24-10.7 24-24s-10.7-24-24-24L136 64c-13.3 0-24 10.7-24 24s10.7 24 24 24l112 0zm28.4 148.7c7-11.2 3.6-26-7.6-33.1s-26-3.6-33.1 7.6l-61.4 98.3-27-36c-8-10.6-23-12.8-33.6-4.8s-12.8 23-4.8 33.6l48 64c4.7 6.3 12.3 9.9 20.2 9.6s15.1-4.5 19.3-11.3l80-128z"]},r={prefix:"fas",iconName:"chevron-up",icon:[448,512,[],"f077","M201.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L224 173.3 54.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"]},s={prefix:"fas",iconName:"expand",icon:[448,512,[],"f065","M32 32C14.3 32 0 46.3 0 64l0 96c0 17.7 14.3 32 32 32s32-14.3 32-32l0-64 64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 32zM64 352c0-17.7-14.3-32-32-32S0 334.3 0 352l0 96c0 17.7 14.3 32 32 32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0 0-64zM320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32l64 0 0 64c0 17.7 14.3 32 32 32s32-14.3 32-32l0-96c0-17.7-14.3-32-32-32l-96 0zM448 352c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 64-64 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l96 0c17.7 0 32-14.3 32-32l0-96z"]},t={prefix:"fas",iconName:"ellipsis-vertical",icon:[128,512,["ellipsis-v"],"f142","M64 144a56 56 0 1 1 0-112 56 56 0 1 1 0 112zm0 224c30.9 0 56 25.1 56 56s-25.1 56-56 56-56-25.1-56-56 25.1-56 56-56zm56-112c0 30.9-25.1 56-56 56s-56-25.1-56-56 25.1-56 56-56 56 25.1 56 56z"]},u={prefix:"fas",iconName:"pen-to-square",icon:[512,512,["edit"],"f044","M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L368 46.1 465.9 144 490.3 119.6c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L432 177.9 334.1 80 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z"]},v=u,w={prefix:"fas",iconName:"clock",icon:[512,512,[128339,"clock-four"],"f017","M256 0a256 256 0 1 1 0 512 256 256 0 1 1 0-512zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"]},x={prefix:"fas",iconName:"rocket",icon:[512,512,[],"f135","M128 320L24.5 320c-24.9 0-40.2-27.1-27.4-48.5L50 183.3C58.7 168.8 74.3 160 91.2 160l95 0c76.1-128.9 189.6-135.4 265.5-124.3 12.8 1.9 22.8 11.9 24.6 24.6 11.1 75.9 4.6 189.4-124.3 265.5l0 95c0 16.9-8.8 32.5-23.3 41.2l-88.2 52.9c-21.3 12.8-48.5-2.6-48.5-27.4L192 384c0-35.3-28.7-64-64-64l-.1 0zM400 160a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"]},y={prefix:"fas",iconName:"heart",icon:[512,512,[128153,128154,128155,128156,128420,129293,129294,129505,9829,10084,61578],"f004","M241 87.1l15 20.7 15-20.7C296 52.5 336.2 32 378.9 32 452.4 32 512 91.6 512 165.1l0 2.6c0 112.2-139.9 242.5-212.9 298.2-12.4 9.4-27.6 14.1-43.1 14.1s-30.8-4.6-43.1-14.1C139.9 410.2 0 279.9 0 167.7l0-2.6C0 91.6 59.6 32 133.1 32 175.8 32 216 52.5 241 87.1z"]},z={prefix:"fas",iconName:"folder-open",icon:[576,512,[128194,128449,61717],"f07c","M56 225.6L32.4 296.2 32.4 96c0-35.3 28.7-64 64-64l138.7 0c13.8 0 27.3 4.5 38.4 12.8l38.4 28.8c5.5 4.2 12.3 6.4 19.2 6.4l117.3 0c35.3 0 64 28.7 64 64l0 16-365.4 0c-41.3 0-78 26.4-91.1 65.6zM477.8 448L99 448c-32.8 0-55.9-32.1-45.5-63.2l48-144C108 221.2 126.4 208 147 208l378.8 0c32.8 0 55.9 32.1 45.5 63.2l-48 144c-6.5 19.6-24.9 32.8-45.5 32.8z"]},A={prefix:"fas",iconName:"chevron-right",icon:[320,512,[9002],"f054","M311.1 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L243.2 256 73.9 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"]},B={prefix:"fas",iconName:"fire",icon:[448,512,[128293],"f06d","M160.5-26.4c9.3-7.8 23-7.5 31.9 .9 12.3 11.6 23.3 24.4 33.9 37.4 13.5 16.5 29.7 38.3 45.3 64.2 5.2-6.8 10-12.8 14.2-17.9 1.1-1.3 2.2-2.7 3.3-4.1 7.9-9.8 17.7-22.1 30.8-22.1 13.4 0 22.8 11.9 30.8 22.1 1.3 1.7 2.6 3.3 3.9 4.8 10.3 12.4 24 30.3 37.7 52.4 27.2 43.9 55.6 106.4 55.6 176.6 0 123.7-100.3 224-224 224S0 411.7 0 288c0-91.1 41.1-170 80.5-225 19.9-27.7 39.7-49.9 54.6-65.1 8.2-8.4 16.5-16.7 25.5-24.2zM225.7 416c25.3 0 47.7-7 68.8-21 42.1-29.4 53.4-88.2 28.1-134.4-4.5-9-16-9.6-22.5-2l-25.2 29.3c-6.6 7.6-18.5 7.4-24.7-.5-17.3-22.1-49.1-62.4-65.3-83-5.4-6.9-15.2-8-21.5-1.9-18.3 17.8-51.5 56.8-51.5 104.3 0 68.6 50.6 109.2 113.7 109.2z"]},C={prefix:"fas",iconName:"database",icon:[448,512,[],"f1c0","M448 205.8c-14.8 9.8-31.8 17.7-49.5 24-47 16.8-108.7 26.2-174.5 26.2S96.4 246.5 49.5 229.8c-17.6-6.3-34.7-14.2-49.5-24L0 288c0 44.2 100.3 80 224 80s224-35.8 224-80l0-82.2zm0-77.8l0-48C448 35.8 347.7 0 224 0S0 35.8 0 80l0 48c0 44.2 100.3 80 224 80s224-35.8 224-80zM398.5 389.8C351.6 406.5 289.9 416 224 416S96.4 406.5 49.5 389.8c-17.6-6.3-34.7-14.2-49.5-24L0 432c0 44.2 100.3 80 224 80s224-35.8 224-80l0-66.2c-14.8 9.8-31.8 17.7-49.5 24z"]},D={prefix:"fas",iconName:"share",icon:[512,512,["mail-forward"],"f064","M307.8 18.4c-12 5-19.8 16.6-19.8 29.6l0 80-112 0c-97.2 0-176 78.8-176 176 0 113.3 81.5 163.9 100.2 174.1 2.5 1.4 5.3 1.9 8.1 1.9 10.9 0 19.7-8.9 19.7-19.7 0-7.5-4.3-14.4-9.8-19.5-9.4-8.8-22.2-26.4-22.2-56.7 0-53 43-96 96-96l96 0 0 80c0 12.9 7.8 24.6 19.8 29.6s25.7 2.2 34.9-6.9l160-160c12.5-12.5 12.5-32.8 0-45.3l-160-160c-9.2-9.2-22.9-11.9-34.9-6.9z"]},E={prefix:"fas",iconName:"video",icon:[576,512,["video-camera"],"f03d","M96 64c-35.3 0-64 28.7-64 64l0 256c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-256c0-35.3-28.7-64-64-64L96 64zM464 336l73.5 58.8c4.2 3.4 9.4 5.2 14.8 5.2 13.1 0 23.7-10.6 23.7-23.7l0-240.6c0-13.1-10.6-23.7-23.7-23.7-5.4 0-10.6 1.8-14.8 5.2L464 176 464 336z"]},F={prefix:"fas",iconName:"clipboard",icon:[384,512,[128203],"f328","M320 32l-8.6 0C300.4 12.9 279.7 0 256 0L128 0C104.3 0 83.6 12.9 72.6 32L64 32C28.7 32 0 60.7 0 96L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-352c0-35.3-28.7-64-64-64zM136 112c-13.3 0-24-10.7-24-24s10.7-24 24-24l112 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-112 0z"]},G={prefix:"fas",iconName:"laptop",icon:[640,512,[128187],"f109","M128 32C92.7 32 64 60.7 64 96l0 240 64 0 0-240 384 0 0 240 64 0 0-240c0-35.3-28.7-64-64-64L128 32zM19.2 384C8.6 384 0 392.6 0 403.2 0 445.6 34.4 480 76.8 480l486.4 0c42.4 0 76.8-34.4 76.8-76.8 0-10.6-8.6-19.2-19.2-19.2L19.2 384z"]},H={prefix:"fas",iconName:"crown",icon:[576,512,[128081],"f521","M313 87.2c9.2-7.3 15-18.6 15-31.2 0-22.1-17.9-40-40-40s-40 17.9-40 40c0 12.6 5.9 23.9 15 31.2L194.6 194.8c-10 15.7-31.3 19.6-46.2 8.4L88.9 158.7c4.5-6.4 7.1-14.3 7.1-22.7 0-22.1-17.9-40-40-40s-40 17.9-40 40c0 21.8 17.5 39.6 39.2 40L87.8 393.5c4.7 31.3 31.6 54.5 63.3 54.5l273.8 0c31.7 0 58.6-23.2 63.3-54.5L520.8 176c21.7-.4 39.2-18.2 39.2-40 0-22.1-17.9-40-40-40s-40 17.9-40 40c0 8.4 2.6 16.3 7.1 22.7l-59.4 44.6c-14.9 11.2-36.2 7.3-46.2-8.4L313 87.2z"]},I={prefix:"fas",iconName:"arrow-up-wide-short",icon:[576,512,["sort-amount-up"],"f161","M150.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-96 96c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L96 141.3 96 448c0 17.7 14.3 32 32 32s32-14.3 32-32l0-306.7 41.4 41.4c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-96-96zM320 480l32 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0c-17.7 0-32 14.3-32 32s14.3 32 32 32zm0-128l224 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L320 32c-17.7 0-32 14.3-32 32s14.3 32 32 32z"]},J={prefix:"fas",iconName:"image",icon:[448,512,[],"f03e","M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zm64 80a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM272 224c8.4 0 16.1 4.4 20.5 11.5l88 144c4.5 7.4 4.7 16.7 .5 24.3S368.7 416 360 416L88 416c-8.9 0-17.2-5-21.3-12.9s-3.5-17.5 1.6-24.8l56-80c4.5-6.4 11.8-10.2 19.7-10.2s15.2 3.8 19.7 10.2l26.4 37.8 61.4-100.5c4.4-7.1 12.1-11.5 20.5-11.5z"]},K={prefix:"fas",iconName:"lightbulb",icon:[384,512,[128161],"f0eb","M292.9 384c7.3-22.3 21.9-42.5 38.4-59.9 32.7-34.4 52.7-80.9 52.7-132.1 0-106-86-192-192-192S0 86 0 192c0 51.2 20 97.7 52.7 132.1 16.5 17.4 31.2 37.6 38.4 59.9l201.7 0zM288 432l-192 0 0 16c0 44.2 35.8 80 80 80l32 0c44.2 0 80-35.8 80-80l0-16zM184 112c-39.8 0-72 32.2-72 72 0 13.3-10.7 24-24 24s-24-10.7-24-24c0-66.3 53.7-120 120-120 13.3 0 24 10.7 24 24s-10.7 24-24 24z"]},L={prefix:"fas",iconName:"code",icon:[576,512,[],"f121","M360.8 1.2c-17-4.9-34.7 5-39.6 22l-128 448c-4.9 17 5 34.7 22 39.6s34.7-5 39.6-22l128-448c4.9-17-5-34.7-22-39.6zm64.6 136.1c-12.5 12.5-12.5 32.8 0 45.3l73.4 73.4-73.4 73.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l96-96c12.5-12.5 12.5-32.8 0-45.3l-96-96c-12.5-12.5-32.8-12.5-45.3 0zm-274.7 0c-12.5-12.5-32.8-12.5-45.3 0l-96 96c-12.5 12.5-12.5 32.8 0 45.3l96 96c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 150.6 182.6c12.5-12.5 12.5-32.8 0-45.3z"]},M={prefix:"fas",iconName:"map",icon:[512,512,[128506,62072],"f279","M512 48c0-11.1-5.7-21.4-15.2-27.2s-21.2-6.4-31.1-1.4L349.5 77.5 170.1 17.6c-8.1-2.7-16.8-2.1-24.4 1.7l-128 64C6.8 88.8 0 99.9 0 112L0 464c0 11.1 5.7 21.4 15.2 27.2s21.2 6.4 31.1 1.4l116.1-58.1 179.4 59.8c8.1 2.7 16.8 2.1 24.4-1.7l128-64c10.8-5.4 17.7-16.5 17.7-28.6l0-352zM192 376.9l0-284.5 128 42.7 0 284.5-128-42.7z"]},N={prefix:"fas",iconName:"folder",icon:[512,512,[128193,128447,61716,"folder-blank"],"f07b","M64 448l384 0c35.3 0 64-28.7 64-64l0-240c0-35.3-28.7-64-64-64L298.7 80c-6.9 0-13.7-2.2-19.2-6.4L241.1 44.8C230 36.5 216.5 32 202.7 32L64 32C28.7 32 0 60.7 0 96L0 384c0 35.3 28.7 64 64 64z"]},O={prefix:"fas",iconName:"cloud",icon:[576,512,[9729],"f0c2","M0 336c0 79.5 64.5 144 144 144l304 0c70.7 0 128-57.3 128-128 0-51.6-30.5-96.1-74.5-116.3 6.7-13.1 10.5-28 10.5-43.7 0-53-43-96-96-96-17.7 0-34.2 4.8-48.4 13.1-24.1-45.8-72.2-77.1-127.6-77.1-79.5 0-144 64.5-144 144 0 8 .7 15.9 1.9 23.5-56.9 19.2-97.9 73.1-97.9 136.5z"]},P={prefix:"fas",iconName:"link",icon:[576,512,[128279,"chain"],"f0c1","M419.5 96c-16.6 0-32.7 4.5-46.8 12.7-15.8-16-34.2-29.4-54.5-39.5 28.2-24 64.1-37.2 101.3-37.2 86.4 0 156.5 70 156.5 156.5 0 41.5-16.5 81.3-45.8 110.6l-71.1 71.1c-29.3 29.3-69.1 45.8-110.6 45.8-86.4 0-156.5-70-156.5-156.5 0-1.5 0-3 .1-4.5 .5-17.7 15.2-31.6 32.9-31.1s31.6 15.2 31.1 32.9c0 .9 0 1.8 0 2.6 0 51.1 41.4 92.5 92.5 92.5 24.5 0 48-9.7 65.4-27.1l71.1-71.1c17.3-17.3 27.1-40.9 27.1-65.4 0-51.1-41.4-92.5-92.5-92.5zM275.2 173.3c-1.9-.8-3.8-1.9-5.5-3.1-12.6-6.5-27-10.2-42.1-10.2-24.5 0-48 9.7-65.4 27.1L91.1 258.2c-17.3 17.3-27.1 40.9-27.1 65.4 0 51.1 41.4 92.5 92.5 92.5 16.5 0 32.6-4.4 46.7-12.6 15.8 16 34.2 29.4 54.6 39.5-28.2 23.9-64 37.2-101.3 37.2-86.4 0-156.5-70-156.5-156.5 0-41.5 16.5-81.3 45.8-110.6l71.1-71.1c29.3-29.3 69.1-45.8 110.6-45.8 86.6 0 156.5 70.6 156.5 156.9 0 1.3 0 2.6 0 3.9-.4 17.7-15.1 31.6-32.8 31.2s-31.6-15.1-31.2-32.8c0-.8 0-1.5 0-2.3 0-33.7-18-63.3-44.8-79.6z"]},Q={prefix:"fas",iconName:"bullseye",icon:[512,512,[],"f140","M448 256a192 192 0 1 0 -384 0 192 192 0 1 0 384 0zM0 256a256 256 0 1 1 512 0 256 256 0 1 1 -512 0zm256 80a80 80 0 1 0 0-160 80 80 0 1 0 0 160zm0-224a144 144 0 1 1 0 288 144 144 0 1 1 0-288zM224 256a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},R={prefix:"fas",iconName:"gear",icon:[512,512,[9881,"cog"],"f013","M195.1 9.5C198.1-5.3 211.2-16 226.4-16l59.8 0c15.2 0 28.3 10.7 31.3 25.5L332 79.5c14.1 6 27.3 13.7 39.3 22.8l67.8-22.5c14.4-4.8 30.2 1.2 37.8 14.4l29.9 51.8c7.6 13.2 4.9 29.8-6.5 39.9L447 233.3c.9 7.4 1.3 15 1.3 22.7s-.5 15.3-1.3 22.7l53.4 47.5c11.4 10.1 14 26.8 6.5 39.9l-29.9 51.8c-7.6 13.1-23.4 19.2-37.8 14.4l-67.8-22.5c-12.1 9.1-25.3 16.7-39.3 22.8l-14.4 69.9c-3.1 14.9-16.2 25.5-31.3 25.5l-59.8 0c-15.2 0-28.3-10.7-31.3-25.5l-14.4-69.9c-14.1-6-27.2-13.7-39.3-22.8L73.5 432.3c-14.4 4.8-30.2-1.2-37.8-14.4L5.8 366.1c-7.6-13.2-4.9-29.8 6.5-39.9l53.4-47.5c-.9-7.4-1.3-15-1.3-22.7s.5-15.3 1.3-22.7L12.3 185.8c-11.4-10.1-14-26.8-6.5-39.9L35.7 94.1c7.6-13.2 23.4-19.2 37.8-14.4l67.8 22.5c12.1-9.1 25.3-16.7 39.3-22.8L195.1 9.5zM256.3 336a80 80 0 1 0 -.6-160 80 80 0 1 0 .6 160z"]},S={prefix:"fas",iconName:"circle-question",icon:[512,512,[62108,"question-circle"],"f059","M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512zm0-336c-17.7 0-32 14.3-32 32 0 13.3-10.7 24-24 24s-24-10.7-24-24c0-44.2 35.8-80 80-80s80 35.8 80 80c0 47.2-36 67.2-56 74.5l0 3.8c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-8.1c0-20.5 14.8-35.2 30.1-40.2 6.4-2.1 13.2-5.5 18.2-10.3 4.3-4.2 7.7-10 7.7-19.6 0-17.7-14.3-32-32-32zM224 368a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"]},T={prefix:"fas",iconName:"right-from-bracket",icon:[512,512,["sign-out-alt"],"f2f5","M505 273c9.4-9.4 9.4-24.6 0-33.9L361 95c-6.9-6.9-17.2-8.9-26.2-5.2S320 102.3 320 112l0 80-112 0c-26.5 0-48 21.5-48 48l0 32c0 26.5 21.5 48 48 48l112 0 0 80c0 9.7 5.8 18.5 14.8 22.2s19.3 1.7 26.2-5.2L505 273zM160 96c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 32C43 32 0 75 0 128L0 384c0 53 43 96 96 96l64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l64 0z"]},U={prefix:"fas",iconName:"file",icon:[384,512,[128196,128459,61462],"f15b","M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-277.5c0-17-6.7-33.3-18.7-45.3L258.7 18.7C246.7 6.7 230.5 0 213.5 0L64 0zM325.5 176L232 176c-13.3 0-24-10.7-24-24L208 58.5 325.5 176z"]},V={prefix:"fas",iconName:"gem",icon:[512,512,[128142],"f3a5","M116.7 33.8c4.5-6.1 11.7-9.8 19.3-9.8l240 0c7.6 0 14.8 3.6 19.3 9.8l112 152c6.8 9.2 6.1 21.9-1.5 30.4l-232 256c-4.5 5-11 7.9-17.8 7.9s-13.2-2.9-17.8-7.9l-232-256c-7.7-8.5-8.3-21.2-1.5-30.4l112-152zm38.5 39.8c-3.3 2.5-4.2 7-2.1 10.5L210.5 179.8 63.3 192c-4.1 .3-7.3 3.8-7.3 8s3.2 7.6 7.3 8l192 16c.4 0 .9 0 1.3 0l192-16c4.1-.3 7.3-3.8 7.3-8s-3.2-7.6-7.3-8l-147.2-12.3 57.4-95.6c2.1-3.5 1.2-8.1-2.1-10.5s-7.9-2-10.7 1L256 172.2 165.9 74.6c-2.8-3-7.4-3.4-10.7-1z"]},W={prefix:"fas",iconName:"calendar",icon:[448,512,[128197,128198],"f133","M128 0C110.3 0 96 14.3 96 32l0 32-32 0C28.7 64 0 92.7 0 128l0 48 448 0 0-48c0-35.3-28.7-64-64-64l-32 0 0-32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 32-128 0 0-32c0-17.7-14.3-32-32-32zM0 224L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-192-448 0z"]},X={prefix:"fas",iconName:"check",icon:[448,512,[10003,10004],"f00c","M434.8 70.1c14.3 10.4 17.5 30.4 7.1 44.7l-256 352c-5.5 7.6-14 12.3-23.4 13.1s-18.5-2.7-25.1-9.3l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l101.5 101.5 234-321.7c10.4-14.3 30.4-17.5 44.7-7.1z"]},Y={prefix:"fas",iconName:"chart-bar",icon:[512,512,["bar-chart"],"f080","M32 32c17.7 0 32 14.3 32 32l0 336c0 8.8 7.2 16 16 16l400 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L80 480c-44.2 0-80-35.8-80-80L0 64C0 46.3 14.3 32 32 32zm96 64c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-192 0c-17.7 0-32-14.3-32-32zm32 80l128 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32zm0 112l256 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"]},Z={prefix:"fas",iconName:"house",icon:[512,512,[127968,63498,63500,"home","home-alt","home-lg-alt"],"f015","M277.8 8.6c-12.3-11.4-31.3-11.4-43.5 0l-224 208c-9.6 9-12.8 22.9-8 35.1S18.8 272 32 272l16 0 0 176c0 35.3 28.7 64 64 64l288 0c35.3 0 64-28.7 64-64l0-176 16 0c13.2 0 25-8.1 29.8-20.3s1.6-26.2-8-35.1l-224-208zM240 320l32 0c26.5 0 48 21.5 48 48l0 96-128 0 0-96c0-26.5 21.5-48 48-48z"]},$={prefix:"fas",iconName:"spinner",icon:[512,512,[],"f110","M208 48a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm0 416a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zM48 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm368 48a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zM75 369.1A48 48 0 1 1 142.9 437 48 48 0 1 1 75 369.1zM75 75A48 48 0 1 1 142.9 142.9 48 48 0 1 1 75 75zM437 369.1A48 48 0 1 1 369.1 437 48 48 0 1 1 437 369.1z"]},_={prefix:"fas",iconName:"wand-magic",icon:[512,512,["magic"],"f0d0","M398.5 12.2l-88.2 88.2 101.3 101.3 88.2-88.2C507.6 105.6 512 95 512 84s-4.4-21.6-12.2-29.5L457.5 12.2C449.6 4.4 439 0 428 0s-21.6 4.4-29.5 12.2zM276.4 134.3L12.2 398.5C4.4 406.4 0 417 0 428s4.4 21.6 12.2 29.5l42.3 42.3C62.4 507.6 73 512 84 512s21.6-4.4 29.5-12.2L377.7 235.6 276.4 134.3z"]},aa={prefix:"fas",iconName:"server",icon:[448,512,[],"f233","M64 32C28.7 32 0 60.7 0 96l0 64c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 32zm216 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm56 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zM64 288c-35.3 0-64 28.7-64 64l0 64c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-64c0-35.3-28.7-64-64-64L64 288zm216 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm56 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"]},ab={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144,62470,"user-alt","user-large"],"f007","M224 248a120 120 0 1 0 0-240 120 120 0 1 0 0 240zm-29.7 56C95.8 304 16 383.8 16 482.3 16 498.7 29.3 512 45.7 512l356.6 0c16.4 0 29.7-13.3 29.7-29.7 0-98.5-79.8-178.3-178.3-178.3l-59.4 0z"]},ac={prefix:"fas",iconName:"bookmark",icon:[384,512,[128278,61591],"f02e","M64 0C28.7 0 0 28.7 0 64L0 480c0 11.5 6.2 22.2 16.2 27.8s22.3 5.5 32.2-.4L192 421.3 335.5 507.4c9.9 5.9 22.2 6.1 32.2 .4S384 491.5 384 480l0-416c0-35.3-28.7-64-64-64L64 0z"]},ad={prefix:"fas",iconName:"xmark",icon:[384,512,[128473,10005,10006,10060,215,"close","multiply","remove","times"],"f00d","M55.1 73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L147.2 256 9.9 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192.5 301.3 329.9 438.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.8 256 375.1 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192.5 210.7 55.1 73.4z"]},ae={prefix:"fas",iconName:"file-lines",icon:[384,512,[128441,128462,61686,"file-alt","file-text"],"f15c","M0 64C0 28.7 28.7 0 64 0L213.5 0c17 0 33.3 6.7 45.3 18.7L365.3 125.3c12 12 18.7 28.3 18.7 45.3L384 448c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64zm208-5.5l0 93.5c0 13.3 10.7 24 24 24L325.5 176 208 58.5zM120 256c-13.3 0-24 10.7-24 24s10.7 24 24 24l144 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-144 0zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24l144 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-144 0z"]},af=ae,ag=ae,ah={prefix:"fas",iconName:"tags",icon:[576,512,[],"f02c","M401.2 39.1L549.4 189.4c27.7 28.1 27.7 73.1 0 101.2L393 448.9c-9.3 9.4-24.5 9.5-33.9 .2s-9.5-24.5-.2-33.9L515.3 256.8c9.2-9.3 9.2-24.4 0-33.7L367 72.9c-9.3-9.4-9.2-24.6 .2-33.9s24.6-9.2 33.9 .2zM32.1 229.5L32.1 96c0-35.3 28.7-64 64-64l133.5 0c17 0 33.3 6.7 45.3 18.7l144 144c25 25 25 65.5 0 90.5L285.4 418.7c-25 25-65.5 25-90.5 0l-144-144c-12-12-18.7-28.3-18.7-45.3zm144-85.5a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},ai={prefix:"fas",iconName:"tablet",icon:[448,512,["tablet-android"],"f3fb","M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-384c0-35.3-28.7-64-64-64L64 0zM184 400l80 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24z"]},aj={prefix:"fas",iconName:"list",icon:[512,512,["list-squares"],"f03a","M40 48C26.7 48 16 58.7 16 72l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24L40 48zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM16 232l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24l0 48c0 13.3 10.7 24 24 24l48 0c13.3 0 24-10.7 24-24l0-48c0-13.3-10.7-24-24-24l-48 0z"]},ak={prefix:"fas",iconName:"chevron-down",icon:[448,512,[],"f078","M201.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 338.7 54.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"]},al={prefix:"fas",iconName:"bullhorn",icon:[512,512,[128226,128363],"f0a1","M461.2 18.9C472.7 24 480 35.4 480 48l0 416c0 12.6-7.3 24-18.8 29.1s-24.8 3.2-34.3-5.1l-46.6-40.7c-43.6-38.1-98.7-60.3-156.4-63l0 95.7c0 17.7-14.3 32-32 32l-32 0c-17.7 0-32-14.3-32-32l0-96C57.3 384 0 326.7 0 256S57.3 128 128 128l84.5 0c61.8-.2 121.4-22.7 167.9-63.3l46.6-40.7c9.4-8.3 22.9-10.2 34.3-5.1zM224 320l0 .2c70.3 2.7 137.8 28.5 192 73.4l0-275.3c-54.2 44.9-121.7 70.7-192 73.4L224 320z"]},am={prefix:"fas",iconName:"chevron-left",icon:[320,512,[9001],"f053","M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"]},an={prefix:"fas",iconName:"star",icon:[576,512,[11088,61446],"f005","M309.5-18.9c-4.1-8-12.4-13.1-21.4-13.1s-17.3 5.1-21.4 13.1L193.1 125.3 33.2 150.7c-8.9 1.4-16.3 7.7-19.1 16.3s-.5 18 5.8 24.4l114.4 114.5-25.2 159.9c-1.4 8.9 2.3 17.9 9.6 23.2s16.9 6.1 25 2L288.1 417.6 432.4 491c8 4.1 17.7 3.3 25-2s11-14.2 9.6-23.2L441.7 305.9 556.1 191.4c6.4-6.4 8.6-15.8 5.8-24.4s-10.1-14.9-19.1-16.3L383 125.3 309.5-18.9z"]},ao={prefix:"fas",iconName:"flag",icon:[448,512,[127988,61725],"f024","M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32L0 480c0 17.7 14.3 32 32 32s32-14.3 32-32l0-121.6 62.7-18.8c41.9-12.6 87.1-8.7 126.2 10.9 42.7 21.4 92.5 24 137.2 7.2l37.1-13.9c12.5-4.7 20.8-16.6 20.8-30l0-247.7c0-23-24.2-38-44.8-27.7l-11.8 5.9c-44.9 22.5-97.8 22.5-142.8 0-36.4-18.2-78.3-21.8-117.2-10.1L64 54.4 64 32z"]},ap={prefix:"fas",iconName:"triangle-exclamation",icon:[512,512,[9888,"exclamation-triangle","warning"],"f071","M256 0c14.7 0 28.2 8.1 35.2 21l216 400c6.7 12.4 6.4 27.4-.8 39.5S486.1 480 472 480L40 480c-14.1 0-27.1-7.4-34.4-19.5s-7.5-27.1-.8-39.5l216-400c7-12.9 20.5-21 35.2-21zm0 168c-13.3 0-24 10.7-24 24l0 112c0 13.3 10.7 24 24 24s24-10.7 24-24l0-112c0-13.3-10.7-24-24-24zm26.7 216a26.7 26.7 0 1 0 -53.3 0 26.7 26.7 0 1 0 53.3 0z"]},aq={prefix:"fas",iconName:"graduation-cap",icon:[576,512,[127891,"mortar-board"],"f19d","M48 195.8l209.2 86.1c9.8 4 20.2 6.1 30.8 6.1s21-2.1 30.8-6.1l242.4-99.8c9-3.7 14.8-12.4 14.8-22.1s-5.8-18.4-14.8-22.1L318.8 38.1C309 34.1 298.6 32 288 32s-21 2.1-30.8 6.1L14.8 137.9C5.8 141.6 0 150.3 0 160L0 456c0 13.3 10.7 24 24 24s24-10.7 24-24l0-260.2zm48 71.7L96 384c0 53 86 96 192 96s192-43 192-96l0-116.6-142.9 58.9c-15.6 6.4-32.2 9.7-49.1 9.7s-33.5-3.3-49.1-9.7L96 267.4z"]},ar={prefix:"fas",iconName:"lock",icon:[384,512,[128274],"f023","M128 96l0 64 128 0 0-64c0-35.3-28.7-64-64-64s-64 28.7-64 64zM64 160l0-64C64 25.3 121.3-32 192-32S320 25.3 320 96l0 64c35.3 0 64 28.7 64 64l0 224c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 224c0-35.3 28.7-64 64-64z"]},as={prefix:"fas",iconName:"download",icon:[448,512,[],"f019","M256 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 210.7-41.4-41.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l96 96c12.5 12.5 32.8 12.5 45.3 0l96-96c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 242.7 256 32zM64 320c-35.3 0-64 28.7-64 64l0 32c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-32c0-35.3-28.7-64-64-64l-46.9 0-56.6 56.6c-31.2 31.2-81.9 31.2-113.1 0L110.9 320 64 320zm304 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"]},at={prefix:"fas",iconName:"award",icon:[448,512,[],"f559","M245.9-25.9c-13.4-8.2-30.3-8.2-43.7 0-24.4 14.9-39.5 18.9-68.1 18.3-15.7-.4-30.3 8.1-37.9 21.9-13.7 25.1-24.8 36.2-49.9 49.9-13.8 7.5-22.2 22.2-21.9 37.9 .7 28.6-3.4 43.7-18.3 68.1-8.2 13.4-8.2 30.3 0 43.7 14.9 24.4 18.9 39.5 18.3 68.1-.4 15.7 8.1 30.3 21.9 37.9 22.1 12.1 33.3 22.1 45.1 41.5L42.7 458.5c-5.9 11.9-1.1 26.3 10.7 32.2l86 43c11.5 5.7 25.5 1.4 31.7-9.8l52.8-95.1 52.8 95.1c6.2 11.2 20.2 15.6 31.7 9.8l86-43c11.9-5.9 16.7-20.3 10.7-32.2l-48.6-97.2c11.7-19.4 23-29.4 45.1-41.5 13.8-7.5 22.2-22.2 21.9-37.9-.7-28.6 3.4-43.7 18.3-68.1 8.2-13.4 8.2-30.3 0-43.7-14.9-24.4-18.9-39.5-18.3-68.1 .4-15.7-8.1-30.3-21.9-37.9-25.1-13.7-36.2-24.8-49.9-49.9-7.5-13.8-22.2-22.2-37.9-21.9-28.6 .7-43.7-3.4-68.1-18.3zM224 96a96 96 0 1 1 0 192 96 96 0 1 1 0-192z"]},au={prefix:"fas",iconName:"globe",icon:[512,512,[127760],"f0ac","M351.9 280l-190.9 0c2.9 64.5 17.2 123.9 37.5 167.4 11.4 24.5 23.7 41.8 35.1 52.4 11.2 10.5 18.9 12.2 22.9 12.2s11.7-1.7 22.9-12.2c11.4-10.6 23.7-28 35.1-52.4 20.3-43.5 34.6-102.9 37.5-167.4zM160.9 232l190.9 0C349 167.5 334.7 108.1 314.4 64.6 303 40.2 290.7 22.8 279.3 12.2 268.1 1.7 260.4 0 256.4 0s-11.7 1.7-22.9 12.2c-11.4 10.6-23.7 28-35.1 52.4-20.3 43.5-34.6 102.9-37.5 167.4zm-48 0C116.4 146.4 138.5 66.9 170.8 14.7 78.7 47.3 10.9 131.2 1.5 232l111.4 0zM1.5 280c9.4 100.8 77.2 184.7 169.3 217.3-32.3-52.2-54.4-131.7-57.9-217.3L1.5 280zm398.4 0c-3.5 85.6-25.6 165.1-57.9 217.3 92.1-32.7 159.9-116.5 169.3-217.3l-111.4 0zm111.4-48C501.9 131.2 434.1 47.3 342 14.7 374.3 66.9 396.4 146.4 399.9 232l111.4 0z"]},av={prefix:"fas",iconName:"upload",icon:[448,512,[],"f093","M256 109.3L256 320c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-210.7-41.4 41.4c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l96-96c12.5-12.5 32.8-12.5 45.3 0l96 96c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 109.3zM224 400c44.2 0 80-35.8 80-80l80 0c35.3 0 64 28.7 64 64l0 32c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64l0-32c0-35.3 28.7-64 64-64l80 0c0 44.2 35.8 80 80 80zm144 24a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"]},aw={prefix:"fas",iconName:"music",icon:[512,512,[127925],"f001","M468 7c7.6 6.1 12 15.3 12 25l0 304c0 44.2-43 80-96 80s-96-35.8-96-80 43-80 96-80c11.2 0 22 1.6 32 4.6l0-116.7-224 49.8 0 206.3c0 44.2-43 80-96 80s-96-35.8-96-80 43-80 96-80c11.2 0 22 1.6 32 4.6L128 96c0-15 10.4-28 25.1-31.2l288-64c9.5-2.1 19.4 .2 27 6.3z"]},ax={prefix:"fas",iconName:"plus",icon:[448,512,[10133,61543,"add"],"2b","M256 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 160-160 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l160 0 0 160c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-160z"]},ay={prefix:"fas",iconName:"copy",icon:[448,512,[],"f0c5","M192 0c-35.3 0-64 28.7-64 64l0 256c0 35.3 28.7 64 64 64l192 0c35.3 0 64-28.7 64-64l0-200.6c0-17.4-7.1-34.1-19.7-46.2L370.6 17.8C358.7 6.4 342.8 0 326.3 0L192 0zM64 128c-35.3 0-64 28.7-64 64L0 448c0 35.3 28.7 64 64 64l192 0c35.3 0 64-28.7 64-64l0-16-64 0 0 16-192 0 0-256 16 0 0-64-16 0z"]},az={prefix:"fas",iconName:"arrow-rotate-right",icon:[512,512,[8635,"arrow-right-rotate","arrow-rotate-forward","redo"],"f01e","M436.7 74.7L448 85.4 448 32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 128c0 17.7-14.3 32-32 32l-128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32l47.9 0-7.6-7.2c-.2-.2-.4-.4-.6-.6-75-75-196.5-75-271.5 0s-75 196.5 0 271.5 196.5 75 271.5 0c8.2-8.2 15.5-16.9 21.9-26.1 10.1-14.5 30.1-18 44.6-7.9s18 30.1 7.9 44.6c-8.5 12.2-18.2 23.8-29.1 34.7-100 100-262.1 100-362 0S-25 175 75 75c99.9-99.9 261.7-100 361.7-.3z"]},aA={prefix:"fas",iconName:"arrow-down-wide-short",icon:[576,512,["sort-amount-asc","sort-amount-down"],"f160","M246.6 374.6l-96 96c-12.5 12.5-32.8 12.5-45.3 0l-96-96c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L96 370.7 96 64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 306.7 41.4-41.4c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3zM320 480c-17.7 0-32-14.3-32-32s14.3-32 32-32l32 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-32 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-96 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l160 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-160 0zm0-128c-17.7 0-32-14.3-32-32s14.3-32 32-32l224 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L320 96z"]},aB={prefix:"fas",iconName:"compress",icon:[448,512,[],"f066","M160 64c0-17.7-14.3-32-32-32S96 46.3 96 64l0 64-64 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l96 0c17.7 0 32-14.3 32-32l0-96zM32 320c-17.7 0-32 14.3-32 32s14.3 32 32 32l64 0 0 64c0 17.7 14.3 32 32 32s32-14.3 32-32l0-96c0-17.7-14.3-32-32-32l-96 0zM352 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7 14.3 32 32 32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-64 0 0-64zM320 320c-17.7 0-32 14.3-32 32l0 96c0 17.7 14.3 32 32 32s32-14.3 32-32l0-64 64 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0z"]},aC={prefix:"fas",iconName:"file-code",icon:[384,512,[],"f1c9","M0 64C0 28.7 28.7 0 64 0L213.5 0c17 0 33.3 6.7 45.3 18.7L365.3 125.3c12 12 18.7 28.3 18.7 45.3L384 448c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64zm208-5.5l0 93.5c0 13.3 10.7 24 24 24L325.5 176 208 58.5zM154.2 295.6c8.6-10.1 7.5-25.2-2.6-33.8s-25.2-7.5-33.8 2.6l-48 56c-7.7 9-7.7 22.2 0 31.2l48 56c8.6 10.1 23.8 11.2 33.8 2.6s11.2-23.8 2.6-33.8l-34.6-40.4 34.6-40.4zm112-31.2c-8.6-10.1-23.8-11.2-33.8-2.6s-11.2 23.8-2.6 33.8l34.6 40.4-34.6 40.4c-8.6 10.1-7.5 25.2 2.6 33.8s25.2 7.5 33.8-2.6l48-56c7.7-9 7.7-22.2 0-31.2l-48-56z"]},aD={prefix:"fas",iconName:"bolt",icon:[448,512,[9889,"zap"],"f0e7","M338.8-9.9c11.9 8.6 16.3 24.2 10.9 37.8L271.3 224 416 224c13.5 0 25.5 8.4 30.1 21.1s.7 26.9-9.6 35.5l-288 240c-11.3 9.4-27.4 9.9-39.3 1.3s-16.3-24.2-10.9-37.8L176.7 288 32 288c-13.5 0-25.5-8.4-30.1-21.1s-.7-26.9 9.6-35.5l288-240c11.3-9.4 27.4-9.9 39.3-1.3z"]},aE=aD,aF={prefix:"fas",iconName:"arrow-rotate-left",icon:[512,512,[8634,"arrow-left-rotate","arrow-rotate-back","arrow-rotate-backward","undo"],"f0e2","M256 64c-56.8 0-107.9 24.7-143.1 64l47.1 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 192c-17.7 0-32-14.3-32-32L0 32C0 14.3 14.3 0 32 0S64 14.3 64 32l0 54.7C110.9 33.6 179.5 0 256 0 397.4 0 512 114.6 512 256S397.4 512 256 512c-87 0-163.9-43.4-210.1-109.7-10.1-14.5-6.6-34.4 7.9-44.6s34.4-6.6 44.6 7.9c34.8 49.8 92.4 82.3 157.6 82.3 106 0 192-86 192-192S362 64 256 64z"]},aG={prefix:"fas",iconName:"compass",icon:[512,512,[129517],"f14e","M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512zm50.7-186.9L162.4 380.6c-19.4 7.5-38.5-11.6-31-31l55.5-144.3c3.3-8.5 9.9-15.1 18.4-18.4l144.3-55.5c19.4-7.5 38.5 11.6 31 31L325.1 306.7c-3.2 8.5-9.9 15.1-18.4 18.4zM288 256a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"]},aH={prefix:"fas",iconName:"desktop",icon:[512,512,[128421,61704,"desktop-alt"],"f390","M64 32C28.7 32 0 60.7 0 96L0 352c0 35.3 28.7 64 64 64l144 0-16 48-72 0c-13.3 0-24 10.7-24 24s10.7 24 24 24l272 0c13.3 0 24-10.7 24-24s-10.7-24-24-24l-72 0-16-48 144 0c35.3 0 64-28.7 64-64l0-256c0-35.3-28.7-64-64-64L64 32zM96 96l320 0c17.7 0 32 14.3 32 32l0 160c0 17.7-14.3 32-32 32L96 320c-17.7 0-32-14.3-32-32l0-160c0-17.7 14.3-32 32-32z"]},aI={prefix:"fas",iconName:"location-dot",icon:[384,512,["map-marker-alt"],"f3c5","M0 188.6C0 84.4 86 0 192 0S384 84.4 384 188.6c0 119.3-120.2 262.3-170.4 316.8-11.8 12.8-31.5 12.8-43.3 0-50.2-54.5-170.4-197.5-170.4-316.8zM192 256a64 64 0 1 0 0-128 64 64 0 1 0 0 128z"]},aJ={prefix:"fas",iconName:"bars",icon:[448,512,["navicon"],"f0c9","M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z"]},aK={prefix:"fas",iconName:"route",icon:[512,512,[],"f4d7","M512 96c0 50.2-59.1 125.1-84.6 155-3.8 4.4-9.4 6.1-14.5 5L320 256c-17.7 0-32 14.3-32 32s14.3 32 32 32l96 0c53 0 96 43 96 96s-43 96-96 96l-276.4 0c8.7-9.9 19.3-22.6 30-36.8 6.3-8.4 12.8-17.6 19-27.2L416 448c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0c-53 0-96-43-96-96s43-96 96-96l39.8 0c-21-31.5-39.8-67.7-39.8-96 0-53 43-96 96-96s96 43 96 96zM117.1 489.1c-3.8 4.3-7.2 8.1-10.1 11.3l-1.8 2-.2-.2c-6 4.6-14.6 4-20-1.8-25.2-27.4-85-97.9-85-148.4 0-53 43-96 96-96s96 43 96 96c0 30-21.1 67-43.5 97.9-10.7 14.7-21.7 28-30.8 38.5l-.6 .7zM128 352a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM416 128a32 32 0 1 0 0-64 32 32 0 1 0 0 64z"]},aL={prefix:"fas",iconName:"hashtag",icon:[512,512,[62098],"23","M214.7 .7c17.3 3.7 28.3 20.7 24.6 38l-19.1 89.3 126.5 0 22-102.7C372.4 8 389.4-3 406.7 .7s28.3 20.7 24.6 38L412.2 128 480 128c17.7 0 32 14.3 32 32s-14.3 32-32 32l-81.6 0-27.4 128 67.8 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-81.6 0-22 102.7c-3.7 17.3-20.7 28.3-38 24.6s-28.3-20.7-24.6-38l19.1-89.3-126.5 0-22 102.7c-3.7 17.3-20.7 28.3-38 24.6s-28.3-20.7-24.6-38L99.8 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l81.6 0 27.4-128-67.8 0c-17.7 0-32-14.3-32-32s14.3-32 32-32l81.6 0 22-102.7C180.4 8 197.4-3 214.7 .7zM206.4 192l-27.4 128 126.5 0 27.4-128-126.5 0z"]},aM={prefix:"fas",iconName:"arrows-rotate",icon:[512,512,[128472,"refresh","sync"],"f021","M65.9 228.5c13.3-93 93.4-164.5 190.1-164.5 53 0 101 21.5 135.8 56.2 .2 .2 .4 .4 .6 .6l7.6 7.2-47.9 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l128 0c17.7 0 32-14.3 32-32l0-128c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 53.4-11.3-10.7C390.5 28.6 326.5 0 256 0 127 0 20.3 95.4 2.6 219.5 .1 237 12.2 253.2 29.7 255.7s33.7-9.7 36.2-27.1zm443.5 64c2.5-17.5-9.7-33.7-27.1-36.2s-33.7 9.7-36.2 27.1c-13.3 93-93.4 164.5-190.1 164.5-53 0-101-21.5-135.8-56.2-.2-.2-.4-.4-.6-.6l-7.6-7.2 47.9 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L32 320c-8.5 0-16.7 3.4-22.7 9.5S-.1 343.7 0 352.3l1 127c.1 17.7 14.6 31.9 32.3 31.7S65.2 496.4 65 478.7l-.4-51.5 10.7 10.1c46.3 46.1 110.2 74.7 180.7 74.7 129 0 235.7-95.4 253.4-219.5z"]},aN={prefix:"fas",iconName:"table-cells-large",icon:[448,512,["th-large"],"f009","M384 96l-128 0 0 128 128 0 0-128zm64 128l0 192c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96C0 60.7 28.7 32 64 32l320 0c35.3 0 64 28.7 64 64l0 128zM64 288l0 128 128 0 0-128-128 0zm128-64l0-128-128 0 0 128 128 0zm64 64l0 128 128 0 0-128-128 0z"]},aO={prefix:"fas",iconName:"circle-info",icon:[512,512,["info-circle"],"f05a","M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512zM224 160a32 32 0 1 1 64 0 32 32 0 1 1 -64 0zm-8 64l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24z"]},aP={prefix:"fas",iconName:"shield",icon:[512,512,[128737,"shield-blank"],"f132","M256 0c4.6 0 9.2 1 13.4 2.9L457.8 82.8c22 9.3 38.4 31 38.3 57.2-.5 99.2-41.3 280.7-213.6 363.2-16.7 8-36.1 8-52.8 0-172.4-82.5-213.1-264-213.6-363.2-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.9 1 251.4 0 256 0z"]}}};