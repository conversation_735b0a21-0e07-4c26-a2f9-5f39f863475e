[{"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\categories\\route.ts": "1", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\categories\\[id]\\route.ts": "2", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\preferences\\route.ts": "3", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\route.ts": "4", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\[id]\\route.ts": "5", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\[id]\\usage\\route.ts": "6", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\tags\\route.ts": "7", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\tags\\[id]\\route.ts": "8", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\categories\\page.tsx": "9", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\page.tsx": "10", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\search\\page.tsx": "11", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\stats\\page.tsx": "12", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\layout.tsx": "13", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\page.tsx": "14", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\layout.tsx": "15", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\page.tsx": "16", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\advanced-search-modal.tsx": "17", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\batch-operations-toolbar.tsx": "18", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\category-form-modal.tsx": "19", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\copy-history.tsx": "20", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\dashboard-header.tsx": "21", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\data-import-export-modal.tsx": "22", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\delete-confirm-dialog.tsx": "23", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\deploy-button.tsx": "24", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\env-var-warning.tsx": "25", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\forgot-password-form.tsx": "26", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\hero.tsx": "27", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\next-logo.tsx": "28", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-card.tsx": "29", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-detail-modal.tsx": "30", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-form-modal.tsx": "31", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\search-bar.tsx": "32", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\search-results.tsx": "33", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\selectable-prompt-card.tsx": "34", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\sidebar.tsx": "35", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\sortable-category-item.tsx": "36", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\stats-dashboard.tsx": "37", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\supabase-logo.tsx": "38", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\theme-switcher.tsx": "39", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\code-block.tsx": "40", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\connect-supabase-steps.tsx": "41", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\fetch-data-steps.tsx": "42", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\sign-up-user-steps.tsx": "43", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\tutorial-step.tsx": "44", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\alert.tsx": "45", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\badge.tsx": "46", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\button.tsx": "47", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\card.tsx": "48", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\checkbox.tsx": "49", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\code-block.tsx": "50", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\dialog.tsx": "51", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\dropdown-menu.tsx": "52", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\icon.tsx": "53", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\input.tsx": "54", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\label.tsx": "55", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\markdown-preview.tsx": "56", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\progress.tsx": "57", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\skeleton.tsx": "58", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\tabs.tsx": "59", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\textarea.tsx": "60", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\toast.tsx": "61", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\toaster.tsx": "62", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\update-password-form.tsx": "63", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\api\\client.ts": "64", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\batch-operations.ts": "65", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\cache.ts": "66", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\client-cache.ts": "67", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\data-export.ts": "68", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\app-preferences.ts": "69", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\categories.ts": "70", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\client.ts": "71", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\index.ts": "72", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\prompts.ts": "73", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\search.ts": "74", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\tags.ts": "75", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\fontawesome.ts": "76", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\performance.ts": "77", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils\\clipboard.ts": "78", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils\\format.ts": "79", "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils.ts": "80"}, {"size": 1521, "mtime": 1753707031955, "results": "81", "hashOfConfig": "82"}, {"size": 1702, "mtime": 1753707050538, "results": "83", "hashOfConfig": "82"}, {"size": 1088, "mtime": 1753707674254, "results": "84", "hashOfConfig": "82"}, {"size": 1571, "mtime": 1753706995917, "results": "85", "hashOfConfig": "82"}, {"size": 1725, "mtime": 1753707010082, "results": "86", "hashOfConfig": "82"}, {"size": 548, "mtime": 1753707019035, "results": "87", "hashOfConfig": "82"}, {"size": 1177, "mtime": 1753707060532, "results": "88", "hashOfConfig": "82"}, {"size": 1638, "mtime": 1753707069672, "results": "89", "hashOfConfig": "82"}, {"size": 9847, "mtime": 1753710441652, "results": "90", "hashOfConfig": "82"}, {"size": 30650, "mtime": 1753724444582, "results": "91", "hashOfConfig": "82"}, {"size": 10677, "mtime": 1753710751586, "results": "92", "hashOfConfig": "82"}, {"size": 4895, "mtime": 1753526924669, "results": "93", "hashOfConfig": "82"}, {"size": 1156, "mtime": 1753478691517, "results": "94", "hashOfConfig": "82"}, {"size": 165, "mtime": 1753706662737, "results": "95", "hashOfConfig": "82"}, {"size": 1736, "mtime": 1753475419000, "results": "96", "hashOfConfig": "82"}, {"size": 1225, "mtime": 1753475419000, "results": "97", "hashOfConfig": "82"}, {"size": 12859, "mtime": 1753519456052, "results": "98", "hashOfConfig": "82"}, {"size": 9775, "mtime": 1753718826849, "results": "99", "hashOfConfig": "82"}, {"size": 10530, "mtime": 1753711152166, "results": "100", "hashOfConfig": "82"}, {"size": 6780, "mtime": 1753527053605, "results": "101", "hashOfConfig": "82"}, {"size": 3687, "mtime": 1753706694132, "results": "102", "hashOfConfig": "82"}, {"size": 11955, "mtime": 1753718826849, "results": "103", "hashOfConfig": "82"}, {"size": 2393, "mtime": 1753479007272, "results": "104", "hashOfConfig": "82"}, {"size": 1321, "mtime": 1753475419000, "results": "105", "hashOfConfig": "82"}, {"size": 537, "mtime": 1753475419000, "results": "106", "hashOfConfig": "82"}, {"size": 3564, "mtime": 1753475419000, "results": "107", "hashOfConfig": "82"}, {"size": 1451, "mtime": 1753475419000, "results": "108", "hashOfConfig": "82"}, {"size": 3993, "mtime": 1753475419000, "results": "109", "hashOfConfig": "82"}, {"size": 8307, "mtime": 1753720134672, "results": "110", "hashOfConfig": "82"}, {"size": 10282, "mtime": 1753527053606, "results": "111", "hashOfConfig": "82"}, {"size": 11628, "mtime": 1753710524781, "results": "112", "hashOfConfig": "82"}, {"size": 8479, "mtime": 1753527053607, "results": "113", "hashOfConfig": "82"}, {"size": 7096, "mtime": 1753709438801, "results": "114", "hashOfConfig": "82"}, {"size": 8149, "mtime": 1753724418004, "results": "115", "hashOfConfig": "82"}, {"size": 6752, "mtime": 1753724545945, "results": "116", "hashOfConfig": "82"}, {"size": 4063, "mtime": 1753518917673, "results": "117", "hashOfConfig": "82"}, {"size": 11728, "mtime": 1753709584362, "results": "118", "hashOfConfig": "82"}, {"size": 7226, "mtime": 1753475419000, "results": "119", "hashOfConfig": "82"}, {"size": 2287, "mtime": 1753475419000, "results": "120", "hashOfConfig": "82"}, {"size": 1365, "mtime": 1753475419000, "results": "121", "hashOfConfig": "82"}, {"size": 2128, "mtime": 1753475419000, "results": "122", "hashOfConfig": "82"}, {"size": 3500, "mtime": 1753475419000, "results": "123", "hashOfConfig": "82"}, {"size": 3606, "mtime": 1753475419000, "results": "124", "hashOfConfig": "82"}, {"size": 683, "mtime": 1753475419000, "results": "125", "hashOfConfig": "82"}, {"size": 1584, "mtime": 1753718826849, "results": "126", "hashOfConfig": "82"}, {"size": 1147, "mtime": 1753475419000, "results": "127", "hashOfConfig": "82"}, {"size": 1915, "mtime": 1753475419000, "results": "128", "hashOfConfig": "82"}, {"size": 1857, "mtime": 1753475419000, "results": "129", "hashOfConfig": "82"}, {"size": 1035, "mtime": 1753475419000, "results": "130", "hashOfConfig": "82"}, {"size": 2332, "mtime": 1753520536021, "results": "131", "hashOfConfig": "82"}, {"size": 3850, "mtime": 1753478117628, "results": "132", "hashOfConfig": "82"}, {"size": 7647, "mtime": 1753475419000, "results": "133", "hashOfConfig": "82"}, {"size": 800, "mtime": 1753518890998, "results": "134", "hashOfConfig": "82"}, {"size": 776, "mtime": 1753475419000, "results": "135", "hashOfConfig": "82"}, {"size": 734, "mtime": 1753475419000, "results": "136", "hashOfConfig": "82"}, {"size": 3342, "mtime": 1753520586333, "results": "137", "hashOfConfig": "82"}, {"size": 791, "mtime": 1753718826849, "results": "138", "hashOfConfig": "82"}, {"size": 6281, "mtime": 1753537172084, "results": "139", "hashOfConfig": "82"}, {"size": 1897, "mtime": 1753478161131, "results": "140", "hashOfConfig": "82"}, {"size": 772, "mtime": 1753478126492, "results": "141", "hashOfConfig": "82"}, {"size": 4853, "mtime": 1753478148989, "results": "142", "hashOfConfig": "82"}, {"size": 786, "mtime": 1753478207836, "results": "143", "hashOfConfig": "82"}, {"size": 2488, "mtime": 1753475419000, "results": "144", "hashOfConfig": "82"}, {"size": 6680, "mtime": 1753711116918, "results": "145", "hashOfConfig": "82"}, {"size": 8379, "mtime": 1753718826849, "results": "146", "hashOfConfig": "82"}, {"size": 2978, "mtime": 1753523182465, "results": "147", "hashOfConfig": "82"}, {"size": 5742, "mtime": 1753706880464, "results": "148", "hashOfConfig": "82"}, {"size": 7856, "mtime": 1753718826849, "results": "149", "hashOfConfig": "82"}, {"size": 4913, "mtime": 1753706637688, "results": "150", "hashOfConfig": "82"}, {"size": 7153, "mtime": 1753724509942, "results": "151", "hashOfConfig": "82"}, {"size": 1906, "mtime": 1753706813800, "results": "152", "hashOfConfig": "82"}, {"size": 2451, "mtime": 1753707711763, "results": "153", "hashOfConfig": "82"}, {"size": 13143, "mtime": 1753722205366, "results": "154", "hashOfConfig": "82"}, {"size": 9880, "mtime": 1753727815707, "results": "155", "hashOfConfig": "82"}, {"size": 6018, "mtime": 1753709177489, "results": "156", "hashOfConfig": "82"}, {"size": 5132, "mtime": 1753480610155, "results": "157", "hashOfConfig": "82"}, {"size": 3920, "mtime": 1753535913878, "results": "158", "hashOfConfig": "82"}, {"size": 3291, "mtime": 1753478601027, "results": "159", "hashOfConfig": "82"}, {"size": 5597, "mtime": 1753523999847, "results": "160", "hashOfConfig": "82"}, {"size": 361, "mtime": 1753475419000, "results": "161", "hashOfConfig": "82"}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1uid1vj", {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\categories\\route.ts", ["402"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\categories\\[id]\\route.ts", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\preferences\\route.ts", ["403"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\route.ts", ["404", "405", "406", "407"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\[id]\\route.ts", ["408"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\prompts\\[id]\\usage\\route.ts", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\tags\\route.ts", ["409"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\tags\\[id]\\route.ts", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\categories\\page.tsx", ["410", "411", "412", "413"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\page.tsx", ["414", "415", "416", "417", "418", "419", "420", "421", "422"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\search\\page.tsx", ["423", "424", "425", "426", "427"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\dashboard\\stats\\page.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\layout.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\page.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\layout.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\page.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\advanced-search-modal.tsx", ["428", "429", "430", "431", "432"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\batch-operations-toolbar.tsx", ["433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\category-form-modal.tsx", ["445"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\copy-history.tsx", ["446", "447", "448", "449", "450"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\dashboard-header.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\data-import-export-modal.tsx", ["451", "452", "453"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\delete-confirm-dialog.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\deploy-button.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\env-var-warning.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\forgot-password-form.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\hero.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\next-logo.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-card.tsx", ["454", "455"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-detail-modal.tsx", ["456"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\prompt-form-modal.tsx", ["457"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\search-bar.tsx", ["458"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\search-results.tsx", ["459", "460", "461", "462", "463"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\selectable-prompt-card.tsx", ["464"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\sidebar.tsx", ["465"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\sortable-category-item.tsx", ["466"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\stats-dashboard.tsx", ["467"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\supabase-logo.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\theme-switcher.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\code-block.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\connect-supabase-steps.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\fetch-data-steps.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\sign-up-user-steps.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\tutorial-step.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\alert.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\badge.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\button.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\card.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\checkbox.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\code-block.tsx", ["468"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\dialog.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\icon.tsx", ["469"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\input.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\label.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\markdown-preview.tsx", ["470", "471"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\progress.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\skeleton.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\tabs.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\textarea.tsx", ["472"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\toast.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\toaster.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\update-password-form.tsx", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\api\\client.ts", ["473", "474", "475", "476"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\batch-operations.ts", [], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\cache.ts", ["477"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\client-cache.ts", ["478", "479", "480", "481", "482", "483", "484", "485", "486"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\data-export.ts", ["487", "488", "489", "490", "491", "492", "493"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\app-preferences.ts", ["494"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\categories.ts", ["495", "496", "497"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\client.ts", ["498", "499"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\index.ts", ["500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\prompts.ts", ["518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\search.ts", ["530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\database\\tags.ts", ["542", "543", "544"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\fontawesome.ts", ["545", "546", "547", "548"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\performance.ts", ["549", "550", "551", "552", "553"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils\\clipboard.ts", ["554"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils\\format.ts", ["555", "556"], [], "D:\\Cursor Project\\prompy augment - 副本\\prompt\\lib\\utils.ts", [], [], {"ruleId": "557", "severity": 2, "message": "558", "line": 6, "column": 27, "nodeType": null, "messageId": "559", "endLine": 6, "endColumn": 34}, {"ruleId": "557", "severity": 2, "message": "558", "line": 6, "column": 27, "nodeType": null, "messageId": "559", "endLine": 6, "endColumn": 34}, {"ruleId": "557", "severity": 2, "message": "560", "line": 2, "column": 36, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 48}, {"ruleId": "557", "severity": 2, "message": "561", "line": 2, "column": 50, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 62}, {"ruleId": "557", "severity": 2, "message": "562", "line": 2, "column": 64, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 83}, {"ruleId": "557", "severity": 2, "message": "563", "line": 3, "column": 29, "nodeType": null, "messageId": "559", "endLine": 3, "endColumn": 41}, {"ruleId": "557", "severity": 2, "message": "562", "line": 2, "column": 53, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 72}, {"ruleId": "557", "severity": 2, "message": "558", "line": 6, "column": 27, "nodeType": null, "messageId": "559", "endLine": 6, "endColumn": 34}, {"ruleId": "557", "severity": 2, "message": "564", "line": 15, "column": 3, "nodeType": null, "messageId": "559", "endLine": 15, "endColumn": 16}, {"ruleId": "557", "severity": 2, "message": "565", "line": 16, "column": 3, "nodeType": null, "messageId": "559", "endLine": 16, "endColumn": 17}, {"ruleId": "566", "severity": 1, "message": "567", "line": 44, "column": 6, "nodeType": "568", "endLine": 44, "endColumn": 8, "suggestions": "569"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 137, "column": 39, "nodeType": "572", "messageId": "573", "endLine": 137, "endColumn": 42, "suggestions": "574"}, {"ruleId": "557", "severity": 2, "message": "575", "line": 7, "column": 10, "nodeType": null, "messageId": "559", "endLine": 7, "endColumn": 20}, {"ruleId": "557", "severity": 2, "message": "576", "line": 42, "column": 10, "nodeType": null, "messageId": "559", "endLine": 42, "endColumn": 26}, {"ruleId": "557", "severity": 2, "message": "577", "line": 43, "column": 10, "nodeType": null, "messageId": "559", "endLine": 43, "endColumn": 29}, {"ruleId": "557", "severity": 2, "message": "578", "line": 77, "column": 10, "nodeType": null, "messageId": "559", "endLine": 77, "endColumn": 29}, {"ruleId": "557", "severity": 2, "message": "579", "line": 77, "column": 31, "nodeType": null, "messageId": "559", "endLine": 77, "endColumn": 53}, {"ruleId": "557", "severity": 2, "message": "580", "line": 78, "column": 10, "nodeType": null, "messageId": "559", "endLine": 78, "endColumn": 22}, {"ruleId": "566", "severity": 1, "message": "581", "line": 86, "column": 6, "nodeType": "568", "endLine": 86, "endColumn": 8, "suggestions": "582"}, {"ruleId": "566", "severity": 1, "message": "583", "line": 143, "column": 6, "nodeType": "568", "endLine": 143, "endColumn": 106, "suggestions": "584"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 511, "column": 42, "nodeType": "572", "messageId": "573", "endLine": 511, "endColumn": 45, "suggestions": "585"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 27, "column": 66, "nodeType": "572", "messageId": "573", "endLine": 27, "endColumn": 69, "suggestions": "586"}, {"ruleId": "566", "severity": 1, "message": "587", "line": 89, "column": 6, "nodeType": "568", "endLine": 89, "endColumn": 8, "suggestions": "588"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 91, "column": 66, "nodeType": "572", "messageId": "573", "endLine": 91, "endColumn": 69, "suggestions": "589"}, {"ruleId": "566", "severity": 1, "message": "590", "line": 121, "column": 6, "nodeType": "568", "endLine": 121, "endColumn": 8, "suggestions": "591"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 123, "column": 47, "nodeType": "572", "messageId": "573", "endLine": 123, "endColumn": 50, "suggestions": "592"}, {"ruleId": "566", "severity": 1, "message": "593", "line": 70, "column": 6, "nodeType": "568", "endLine": 70, "endColumn": 14, "suggestions": "594"}, {"ruleId": "566", "severity": 1, "message": "595", "line": 87, "column": 6, "nodeType": "568", "endLine": 87, "endColumn": 37, "suggestions": "596"}, {"ruleId": "566", "severity": 1, "message": "597", "line": 87, "column": 7, "nodeType": "598", "endLine": 87, "endColumn": 36}, {"ruleId": "570", "severity": 2, "message": "571", "line": 322, "column": 66, "nodeType": "572", "messageId": "573", "endLine": 322, "endColumn": 69, "suggestions": "599"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 336, "column": 69, "nodeType": "572", "messageId": "573", "endLine": 336, "endColumn": 72, "suggestions": "600"}, {"ruleId": "557", "severity": 2, "message": "601", "line": 14, "column": 3, "nodeType": null, "messageId": "559", "endLine": 14, "endColumn": 9}, {"ruleId": "557", "severity": 2, "message": "602", "line": 15, "column": 3, "nodeType": null, "messageId": "559", "endLine": 15, "endColumn": 16}, {"ruleId": "557", "severity": 2, "message": "603", "line": 16, "column": 3, "nodeType": null, "messageId": "559", "endLine": 16, "endColumn": 13}, {"ruleId": "557", "severity": 2, "message": "604", "line": 17, "column": 3, "nodeType": null, "messageId": "559", "endLine": 17, "endColumn": 16}, {"ruleId": "557", "severity": 2, "message": "605", "line": 18, "column": 3, "nodeType": null, "messageId": "559", "endLine": 18, "endColumn": 14}, {"ruleId": "557", "severity": 2, "message": "606", "line": 28, "column": 3, "nodeType": null, "messageId": "559", "endLine": 28, "endColumn": 14}, {"ruleId": "557", "severity": 2, "message": "607", "line": 29, "column": 3, "nodeType": null, "messageId": "559", "endLine": 29, "endColumn": 9}, {"ruleId": "557", "severity": 2, "message": "608", "line": 30, "column": 3, "nodeType": null, "messageId": "559", "endLine": 30, "endColumn": 8}, {"ruleId": "557", "severity": 2, "message": "609", "line": 104, "column": 14, "nodeType": null, "messageId": "559", "endLine": 104, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "609", "line": 145, "column": 14, "nodeType": null, "messageId": "559", "endLine": 145, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "609", "line": 183, "column": 14, "nodeType": null, "messageId": "559", "endLine": 183, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "609", "line": 220, "column": 14, "nodeType": null, "messageId": "559", "endLine": 220, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "610", "line": 19, "column": 10, "nodeType": null, "messageId": "559", "endLine": 19, "endColumn": 29}, {"ruleId": "570", "severity": 2, "message": "571", "line": 91, "column": 16, "nodeType": "572", "messageId": "573", "endLine": 91, "endColumn": 19, "suggestions": "611"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 94, "column": 25, "nodeType": "572", "messageId": "573", "endLine": 94, "endColumn": 28, "suggestions": "612"}, {"ruleId": "566", "severity": 1, "message": "613", "line": 96, "column": 6, "nodeType": "568", "endLine": 96, "endColumn": 15, "suggestions": "614"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 212, "column": 20, "nodeType": "572", "messageId": "573", "endLine": 212, "endColumn": 23, "suggestions": "615"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 213, "column": 18, "nodeType": "572", "messageId": "573", "endLine": 213, "endColumn": 21, "suggestions": "616"}, {"ruleId": "557", "severity": 2, "message": "609", "line": 61, "column": 14, "nodeType": null, "messageId": "559", "endLine": 61, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "609", "line": 89, "column": 14, "nodeType": null, "messageId": "559", "endLine": 89, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "609", "line": 149, "column": 14, "nodeType": null, "messageId": "559", "endLine": 149, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "617", "line": 51, "column": 3, "nodeType": null, "messageId": "559", "endLine": 51, "endColumn": 12}, {"ruleId": "570", "severity": 2, "message": "571", "line": 180, "column": 44, "nodeType": "572", "messageId": "573", "endLine": 180, "endColumn": 47, "suggestions": "618"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 130, "column": 55, "nodeType": "572", "messageId": "573", "endLine": 130, "endColumn": 58, "suggestions": "619"}, {"ruleId": "566", "severity": 1, "message": "593", "line": 61, "column": 6, "nodeType": "568", "endLine": 61, "endColumn": 14, "suggestions": "620"}, {"ruleId": "566", "severity": 1, "message": "621", "line": 107, "column": 6, "nodeType": "568", "endLine": 107, "endColumn": 49, "suggestions": "622"}, {"ruleId": "557", "severity": 2, "message": "623", "line": 5, "column": 10, "nodeType": null, "messageId": "559", "endLine": 5, "endColumn": 15}, {"ruleId": "570", "severity": 2, "message": "571", "line": 22, "column": 30, "nodeType": "572", "messageId": "573", "endLine": 22, "endColumn": 33, "suggestions": "624"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 42, "column": 41, "nodeType": "572", "messageId": "573", "endLine": 42, "endColumn": 44, "suggestions": "625"}, {"ruleId": "626", "severity": 2, "message": "627", "line": 58, "column": 69, "nodeType": "628", "messageId": "629", "suggestions": "630"}, {"ruleId": "626", "severity": 2, "message": "627", "line": 59, "column": 81, "nodeType": "628", "messageId": "629", "suggestions": "631"}, {"ruleId": "557", "severity": 2, "message": "609", "line": 75, "column": 14, "nodeType": null, "messageId": "559", "endLine": 75, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "632", "line": 10, "column": 11, "nodeType": null, "messageId": "559", "endLine": 10, "endColumn": 26}, {"ruleId": "570", "severity": 2, "message": "571", "line": 65, "column": 50, "nodeType": "572", "messageId": "573", "endLine": 65, "endColumn": 53, "suggestions": "633"}, {"ruleId": "557", "severity": 2, "message": "634", "line": 18, "column": 3, "nodeType": null, "messageId": "559", "endLine": 18, "endColumn": 20}, {"ruleId": "557", "severity": 2, "message": "609", "line": 33, "column": 14, "nodeType": null, "messageId": "559", "endLine": 33, "endColumn": 19}, {"ruleId": "570", "severity": 2, "message": "571", "line": 29, "column": 34, "nodeType": "572", "messageId": "573", "endLine": 29, "endColumn": 37, "suggestions": "635"}, {"ruleId": "557", "severity": 2, "message": "636", "line": 17, "column": 3, "nodeType": null, "messageId": "559", "endLine": 17, "endColumn": 11}, {"ruleId": "557", "severity": 2, "message": "637", "line": 24, "column": 18, "nodeType": null, "messageId": "559", "endLine": 24, "endColumn": 22}, {"ruleId": "638", "severity": 2, "message": "639", "line": 5, "column": 18, "nodeType": "640", "messageId": "641", "endLine": 5, "endColumn": 31, "suggestions": "642"}, {"ruleId": "557", "severity": 2, "message": "643", "line": 184, "column": 40, "nodeType": null, "messageId": "559", "endLine": 184, "endColumn": 45}, {"ruleId": "570", "severity": 2, "message": "571", "line": 184, "column": 69, "nodeType": "572", "messageId": "573", "endLine": 184, "endColumn": 72, "suggestions": "644"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 199, "column": 46, "nodeType": "572", "messageId": "573", "endLine": 199, "endColumn": 49, "suggestions": "645"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 199, "column": 60, "nodeType": "572", "messageId": "573", "endLine": 199, "endColumn": 63, "suggestions": "646"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 13, "column": 45, "nodeType": "572", "messageId": "573", "endLine": 13, "endColumn": 48, "suggestions": "647"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 14, "column": 51, "nodeType": "572", "messageId": "573", "endLine": 14, "endColumn": 54, "suggestions": "648"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 20, "column": 45, "nodeType": "572", "messageId": "573", "endLine": 20, "endColumn": 48, "suggestions": "649"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 31, "column": 27, "nodeType": "572", "messageId": "573", "endLine": 31, "endColumn": 30, "suggestions": "650"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 31, "column": 33, "nodeType": "572", "messageId": "573", "endLine": 31, "endColumn": 36, "suggestions": "651"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 35, "column": 19, "nodeType": "572", "messageId": "573", "endLine": 35, "endColumn": 22, "suggestions": "652"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 45, "column": 70, "nodeType": "572", "messageId": "573", "endLine": 45, "endColumn": 73, "suggestions": "653"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 70, "column": 32, "nodeType": "572", "messageId": "573", "endLine": 70, "endColumn": 35, "suggestions": "654"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 112, "column": 32, "nodeType": "572", "messageId": "573", "endLine": 112, "endColumn": 35, "suggestions": "655"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 212, "column": 12, "nodeType": "572", "messageId": "573", "endLine": 212, "endColumn": 15, "suggestions": "656"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 10, "column": 12, "nodeType": "572", "messageId": "573", "endLine": 10, "endColumn": 15, "suggestions": "657"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 11, "column": 15, "nodeType": "572", "messageId": "573", "endLine": 11, "endColumn": 18, "suggestions": "658"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 12, "column": 9, "nodeType": "572", "messageId": "573", "endLine": 12, "endColumn": 12, "suggestions": "659"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 163, "column": 42, "nodeType": "572", "messageId": "573", "endLine": 163, "endColumn": 45, "suggestions": "660"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 204, "column": 15, "nodeType": "572", "messageId": "573", "endLine": 204, "endColumn": 18, "suggestions": "661"}, {"ruleId": "557", "severity": 2, "message": "609", "line": 207, "column": 14, "nodeType": null, "messageId": "559", "endLine": 207, "endColumn": 19}, {"ruleId": "570", "severity": 2, "message": "571", "line": 257, "column": 58, "nodeType": "572", "messageId": "573", "endLine": 257, "endColumn": 61, "suggestions": "662"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 103, "column": 25, "nodeType": "572", "messageId": "573", "endLine": 103, "endColumn": 28, "suggestions": "663"}, {"ruleId": "557", "severity": 2, "message": "664", "line": 8, "column": 3, "nodeType": null, "messageId": "559", "endLine": 8, "endColumn": 16}, {"ruleId": "570", "severity": 2, "message": "571", "line": 38, "column": 38, "nodeType": "572", "messageId": "573", "endLine": 38, "endColumn": 41, "suggestions": "665"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 130, "column": 25, "nodeType": "572", "messageId": "573", "endLine": 130, "endColumn": 28, "suggestions": "666"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 50, "column": 52, "nodeType": "572", "messageId": "573", "endLine": 50, "endColumn": 55, "suggestions": "667"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 50, "column": 68, "nodeType": "572", "messageId": "573", "endLine": 50, "endColumn": 71, "suggestions": "668"}, {"ruleId": "557", "severity": 2, "message": "669", "line": 41, "column": 47, "nodeType": null, "messageId": "559", "endLine": 41, "endColumn": 51}, {"ruleId": "557", "severity": 2, "message": "670", "line": 42, "column": 46, "nodeType": null, "messageId": "559", "endLine": 42, "endColumn": 48}, {"ruleId": "557", "severity": 2, "message": "669", "line": 43, "column": 42, "nodeType": null, "messageId": "559", "endLine": 43, "endColumn": 46}, {"ruleId": "557", "severity": 2, "message": "670", "line": 44, "column": 40, "nodeType": null, "messageId": "559", "endLine": 44, "endColumn": 42}, {"ruleId": "557", "severity": 2, "message": "671", "line": 46, "column": 34, "nodeType": null, "messageId": "559", "endLine": 46, "endColumn": 39}, {"ruleId": "557", "severity": 2, "message": "670", "line": 47, "column": 37, "nodeType": null, "messageId": "559", "endLine": 47, "endColumn": 39}, {"ruleId": "557", "severity": 2, "message": "672", "line": 48, "column": 38, "nodeType": null, "messageId": "559", "endLine": 48, "endColumn": 46}, {"ruleId": "557", "severity": 2, "message": "673", "line": 48, "column": 56, "nodeType": null, "messageId": "559", "endLine": 48, "endColumn": 61}, {"ruleId": "557", "severity": 2, "message": "672", "line": 49, "column": 43, "nodeType": null, "messageId": "559", "endLine": 49, "endColumn": 51}, {"ruleId": "557", "severity": 2, "message": "673", "line": 49, "column": 61, "nodeType": null, "messageId": "559", "endLine": 49, "endColumn": 66}, {"ruleId": "557", "severity": 2, "message": "674", "line": 50, "column": 37, "nodeType": null, "messageId": "559", "endLine": 50, "endColumn": 41}, {"ruleId": "570", "severity": 2, "message": "571", "line": 50, "column": 43, "nodeType": "572", "messageId": "573", "endLine": 50, "endColumn": 46, "suggestions": "675"}, {"ruleId": "557", "severity": 2, "message": "676", "line": 53, "column": 40, "nodeType": null, "messageId": "559", "endLine": 53, "endColumn": 44}, {"ruleId": "557", "severity": 2, "message": "670", "line": 55, "column": 47, "nodeType": null, "messageId": "559", "endLine": 55, "endColumn": 49}, {"ruleId": "557", "severity": 2, "message": "671", "line": 57, "column": 44, "nodeType": null, "messageId": "559", "endLine": 57, "endColumn": 49}, {"ruleId": "557", "severity": 2, "message": "671", "line": 58, "column": 37, "nodeType": null, "messageId": "559", "endLine": 58, "endColumn": 42}, {"ruleId": "557", "severity": 2, "message": "677", "line": 59, "column": 38, "nodeType": null, "messageId": "559", "endLine": 59, "endColumn": 44}, {"ruleId": "570", "severity": 2, "message": "571", "line": 59, "column": 46, "nodeType": "572", "messageId": "573", "endLine": 59, "endColumn": 49, "suggestions": "678"}, {"ruleId": "557", "severity": 2, "message": "679", "line": 4, "column": 3, "nodeType": null, "messageId": "559", "endLine": 4, "endColumn": 9}, {"ruleId": "557", "severity": 2, "message": "664", "line": 10, "column": 3, "nodeType": null, "messageId": "559", "endLine": 10, "endColumn": 16}, {"ruleId": "557", "severity": 2, "message": "680", "line": 19, "column": 5, "nodeType": null, "messageId": "559", "endLine": 19, "endColumn": 15}, {"ruleId": "557", "severity": 2, "message": "681", "line": 20, "column": 5, "nodeType": null, "messageId": "559", "endLine": 20, "endColumn": 11}, {"ruleId": "557", "severity": 2, "message": "682", "line": 21, "column": 5, "nodeType": null, "messageId": "559", "endLine": 21, "endColumn": 11}, {"ruleId": "557", "severity": 2, "message": "683", "line": 22, "column": 5, "nodeType": null, "messageId": "559", "endLine": 22, "endColumn": 14}, {"ruleId": "570", "severity": 2, "message": "571", "line": 88, "column": 24, "nodeType": "572", "messageId": "573", "endLine": 88, "endColumn": 27, "suggestions": "684"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 147, "column": 24, "nodeType": "572", "messageId": "573", "endLine": 147, "endColumn": 27, "suggestions": "685"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 179, "column": 43, "nodeType": "572", "messageId": "573", "endLine": 179, "endColumn": 46, "suggestions": "686"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 201, "column": 24, "nodeType": "572", "messageId": "573", "endLine": 201, "endColumn": 27, "suggestions": "687"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 294, "column": 28, "nodeType": "572", "messageId": "573", "endLine": 294, "endColumn": 31, "suggestions": "688"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 350, "column": 27, "nodeType": "572", "messageId": "573", "endLine": 350, "endColumn": 30, "suggestions": "689"}, {"ruleId": "557", "severity": 2, "message": "690", "line": 2, "column": 10, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 25}, {"ruleId": "557", "severity": 2, "message": "691", "line": 2, "column": 27, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 44}, {"ruleId": "557", "severity": 2, "message": "692", "line": 2, "column": 46, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 62}, {"ruleId": "557", "severity": 2, "message": "693", "line": 2, "column": 64, "nodeType": null, "messageId": "559", "endLine": 2, "endColumn": 85}, {"ruleId": "557", "severity": 2, "message": "694", "line": 5, "column": 3, "nodeType": null, "messageId": "559", "endLine": 5, "endColumn": 22}, {"ruleId": "557", "severity": 2, "message": "695", "line": 6, "column": 3, "nodeType": null, "messageId": "559", "endLine": 6, "endColumn": 22}, {"ruleId": "557", "severity": 2, "message": "664", "line": 7, "column": 3, "nodeType": null, "messageId": "559", "endLine": 7, "endColumn": 16}, {"ruleId": "557", "severity": 2, "message": "643", "line": 14, "column": 40, "nodeType": null, "messageId": "559", "endLine": 14, "endColumn": 45}, {"ruleId": "557", "severity": 2, "message": "643", "line": 69, "column": 45, "nodeType": null, "messageId": "559", "endLine": 69, "endColumn": 50}, {"ruleId": "557", "severity": 2, "message": "643", "line": 83, "column": 59, "nodeType": null, "messageId": "559", "endLine": 83, "endColumn": 64}, {"ruleId": "570", "severity": 2, "message": "571", "line": 138, "column": 19, "nodeType": "572", "messageId": "573", "endLine": 138, "endColumn": 22, "suggestions": "696"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 280, "column": 24, "nodeType": "572", "messageId": "573", "endLine": 280, "endColumn": 27, "suggestions": "697"}, {"ruleId": "557", "severity": 2, "message": "664", "line": 7, "column": 3, "nodeType": null, "messageId": "559", "endLine": 7, "endColumn": 16}, {"ruleId": "570", "severity": 2, "message": "571", "line": 24, "column": 38, "nodeType": "572", "messageId": "573", "endLine": 24, "endColumn": 41, "suggestions": "698"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 101, "column": 25, "nodeType": "572", "messageId": "573", "endLine": 101, "endColumn": 28, "suggestions": "699"}, {"ruleId": "557", "severity": 2, "message": "700", "line": 96, "column": 3, "nodeType": null, "messageId": "559", "endLine": 96, "endColumn": 12}, {"ruleId": "557", "severity": 2, "message": "701", "line": 97, "column": 3, "nodeType": null, "messageId": "559", "endLine": 97, "endColumn": 12}, {"ruleId": "557", "severity": 2, "message": "702", "line": 98, "column": 3, "nodeType": null, "messageId": "559", "endLine": 98, "endColumn": 8}, {"ruleId": "557", "severity": 2, "message": "703", "line": 99, "column": 3, "nodeType": null, "messageId": "559", "endLine": 99, "endColumn": 17}, {"ruleId": "570", "severity": 2, "message": "571", "line": 97, "column": 46, "nodeType": "572", "messageId": "573", "endLine": 97, "endColumn": 49, "suggestions": "704"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 97, "column": 56, "nodeType": "572", "messageId": "573", "endLine": 97, "endColumn": 59, "suggestions": "705"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 117, "column": 46, "nodeType": "572", "messageId": "573", "endLine": 117, "endColumn": 49, "suggestions": "706"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 117, "column": 56, "nodeType": "572", "messageId": "573", "endLine": 117, "endColumn": 59, "suggestions": "707"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 175, "column": 36, "nodeType": "572", "messageId": "573", "endLine": 175, "endColumn": 39, "suggestions": "708"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 111, "column": 38, "nodeType": "572", "messageId": "573", "endLine": 111, "endColumn": 41, "suggestions": "709"}, {"ruleId": "570", "severity": 2, "message": "571", "line": 241, "column": 34, "nodeType": "572", "messageId": "573", "endLine": 241, "endColumn": 37, "suggestions": "710"}, {"ruleId": "557", "severity": 2, "message": "609", "line": 246, "column": 12, "nodeType": null, "messageId": "559", "endLine": 246, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "unusedVar", "'updatePrompt' is defined but never used.", "'deletePrompt' is defined but never used.", "'incrementUsageCount' is defined but never used.", "'PromptUpdate' is defined but never used.", "'getCategories' is defined but never used.", "'deleteCategory' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCategories'. Either include it or remove the dependency array.", "ArrayExpression", ["711"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["712", "713"], "'PromptCard' is defined but never used.", "'isLoadingPrompts' is assigned a value but never used.", "'isLoadingCategories' is assigned a value but never used.", "'isConflictModalOpen' is assigned a value but never used.", "'setIsConflictModalOpen' is assigned a value but never used.", "'hasConflicts' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeApp'. Either include it or remove the dependency array.", ["714"], "React Hook useEffect has a missing dependency: 'loadPrompts'. Either include it or remove the dependency array.", ["715"], ["716", "717"], ["718", "719"], "React Hook useEffect has missing dependencies: 'initialQuery' and 'loadSearchHistory'. Either include them or remove the dependency array.", ["720"], ["721", "722"], "React Hook useCallback has a missing dependency: 'loadSearchHistory'. Either include it or remove the dependency array.", ["723"], ["724", "725"], "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["726"], "React Hook useEffect has a missing dependency: 'initialParams'. Either include it or remove the dependency array.", ["727"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["728", "729"], ["730", "731"], "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'CheckSquare' is defined but never used.", "'Square' is defined but never used.", "'Minus' is defined but never used.", "'error' is defined but never used.", "'generateRandomColor' is defined but never used.", ["732", "733"], ["734", "735"], "React Hook useEffect has a missing dependency: 'addCopyRecord'. Either include it or remove the dependency array.", ["736"], ["737", "738"], ["739", "740"], "'createdAt' is defined but never used.", ["741", "742"], ["743", "744"], ["745"], "React Hook useEffect has missing dependencies: 'onChange', 'onSearch', and 'value'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["746"], "'Badge' is defined but never used.", ["747", "748"], ["749", "750"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["751", "752", "753", "754"], ["755", "756", "757", "758"], "'SidebarCategory' is defined but never used.", ["759", "760"], "'CategoryWithCount' is defined but never used.", ["761", "762"], "'maxLines' is assigned a value but never used.", "'node' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["763"], "'limit' is assigned a value but never used.", ["764", "765"], ["766", "767"], ["768", "769"], ["770", "771"], ["772", "773"], ["774", "775"], ["776", "777"], ["778", "779"], ["780", "781"], ["782", "783"], ["784", "785"], ["786", "787"], ["788", "789"], ["790", "791"], ["792", "793"], ["794", "795"], ["796", "797"], ["798", "799"], ["800", "801"], ["802", "803"], "'DatabaseError' is defined but never used.", ["804", "805"], ["806", "807"], ["808", "809"], ["810", "811"], "'name' is defined but never used.", "'id' is defined but never used.", "'query' is defined but never used.", "'promptId' is defined but never used.", "'tagId' is defined but never used.", "'data' is defined but never used.", ["812", "813"], "'term' is defined but never used.", "'params' is defined but never used.", ["814", "815"], "'Prompt' is defined but never used.", "'categoryId' is assigned a value but never used.", "'tagIds' is assigned a value but never used.", "'sortBy' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", ["816", "817"], ["818", "819"], ["820", "821"], ["822", "823"], ["824", "825"], ["826", "827"], "'withClientCache' is defined but never used.", "'CLIENT_CACHE_KEYS' is defined but never used.", "'CLIENT_CACHE_TTL' is defined but never used.", "'invalidateClientCache' is defined but never used.", "'SearchHistoryInsert' is defined but never used.", "'SearchHistoryUpdate' is defined but never used.", ["828", "829"], ["830", "831"], ["832", "833"], ["834", "835"], "'faThLarge' is defined but never used.", "'faFileAlt' is defined but never used.", "'faZap' is defined but never used.", "'faMapMarkerAlt' is defined but never used.", ["836", "837"], ["838", "839"], ["840", "841"], ["842", "843"], ["844", "845"], ["846", "847"], ["848", "849"], {"desc": "850", "fix": "851"}, {"messageId": "852", "fix": "853", "desc": "854"}, {"messageId": "855", "fix": "856", "desc": "857"}, {"desc": "858", "fix": "859"}, {"desc": "860", "fix": "861"}, {"messageId": "852", "fix": "862", "desc": "854"}, {"messageId": "855", "fix": "863", "desc": "857"}, {"messageId": "852", "fix": "864", "desc": "854"}, {"messageId": "855", "fix": "865", "desc": "857"}, {"desc": "866", "fix": "867"}, {"messageId": "852", "fix": "868", "desc": "854"}, {"messageId": "855", "fix": "869", "desc": "857"}, {"desc": "870", "fix": "871"}, {"messageId": "852", "fix": "872", "desc": "854"}, {"messageId": "855", "fix": "873", "desc": "857"}, {"desc": "874", "fix": "875"}, {"desc": "876", "fix": "877"}, {"messageId": "852", "fix": "878", "desc": "854"}, {"messageId": "855", "fix": "879", "desc": "857"}, {"messageId": "852", "fix": "880", "desc": "854"}, {"messageId": "855", "fix": "881", "desc": "857"}, {"messageId": "852", "fix": "882", "desc": "854"}, {"messageId": "855", "fix": "883", "desc": "857"}, {"messageId": "852", "fix": "884", "desc": "854"}, {"messageId": "855", "fix": "885", "desc": "857"}, {"desc": "886", "fix": "887"}, {"messageId": "852", "fix": "888", "desc": "854"}, {"messageId": "855", "fix": "889", "desc": "857"}, {"messageId": "852", "fix": "890", "desc": "854"}, {"messageId": "855", "fix": "891", "desc": "857"}, {"messageId": "852", "fix": "892", "desc": "854"}, {"messageId": "855", "fix": "893", "desc": "857"}, {"messageId": "852", "fix": "894", "desc": "854"}, {"messageId": "855", "fix": "895", "desc": "857"}, {"desc": "874", "fix": "896"}, {"desc": "897", "fix": "898"}, {"messageId": "852", "fix": "899", "desc": "854"}, {"messageId": "855", "fix": "900", "desc": "857"}, {"messageId": "852", "fix": "901", "desc": "854"}, {"messageId": "855", "fix": "902", "desc": "857"}, {"messageId": "903", "data": "904", "fix": "905", "desc": "906"}, {"messageId": "903", "data": "907", "fix": "908", "desc": "909"}, {"messageId": "903", "data": "910", "fix": "911", "desc": "912"}, {"messageId": "903", "data": "913", "fix": "914", "desc": "915"}, {"messageId": "903", "data": "916", "fix": "917", "desc": "906"}, {"messageId": "903", "data": "918", "fix": "919", "desc": "909"}, {"messageId": "903", "data": "920", "fix": "921", "desc": "912"}, {"messageId": "903", "data": "922", "fix": "923", "desc": "915"}, {"messageId": "852", "fix": "924", "desc": "854"}, {"messageId": "855", "fix": "925", "desc": "857"}, {"messageId": "852", "fix": "926", "desc": "854"}, {"messageId": "855", "fix": "927", "desc": "857"}, {"messageId": "928", "fix": "929", "desc": "930"}, {"messageId": "852", "fix": "931", "desc": "854"}, {"messageId": "855", "fix": "932", "desc": "857"}, {"messageId": "852", "fix": "933", "desc": "854"}, {"messageId": "855", "fix": "934", "desc": "857"}, {"messageId": "852", "fix": "935", "desc": "854"}, {"messageId": "855", "fix": "936", "desc": "857"}, {"messageId": "852", "fix": "937", "desc": "854"}, {"messageId": "855", "fix": "938", "desc": "857"}, {"messageId": "852", "fix": "939", "desc": "854"}, {"messageId": "855", "fix": "940", "desc": "857"}, {"messageId": "852", "fix": "941", "desc": "854"}, {"messageId": "855", "fix": "942", "desc": "857"}, {"messageId": "852", "fix": "943", "desc": "854"}, {"messageId": "855", "fix": "944", "desc": "857"}, {"messageId": "852", "fix": "945", "desc": "854"}, {"messageId": "855", "fix": "946", "desc": "857"}, {"messageId": "852", "fix": "947", "desc": "854"}, {"messageId": "855", "fix": "948", "desc": "857"}, {"messageId": "852", "fix": "949", "desc": "854"}, {"messageId": "855", "fix": "950", "desc": "857"}, {"messageId": "852", "fix": "951", "desc": "854"}, {"messageId": "855", "fix": "952", "desc": "857"}, {"messageId": "852", "fix": "953", "desc": "854"}, {"messageId": "855", "fix": "954", "desc": "857"}, {"messageId": "852", "fix": "955", "desc": "854"}, {"messageId": "855", "fix": "956", "desc": "857"}, {"messageId": "852", "fix": "957", "desc": "854"}, {"messageId": "855", "fix": "958", "desc": "857"}, {"messageId": "852", "fix": "959", "desc": "854"}, {"messageId": "855", "fix": "960", "desc": "857"}, {"messageId": "852", "fix": "961", "desc": "854"}, {"messageId": "855", "fix": "962", "desc": "857"}, {"messageId": "852", "fix": "963", "desc": "854"}, {"messageId": "855", "fix": "964", "desc": "857"}, {"messageId": "852", "fix": "965", "desc": "854"}, {"messageId": "855", "fix": "966", "desc": "857"}, {"messageId": "852", "fix": "967", "desc": "854"}, {"messageId": "855", "fix": "968", "desc": "857"}, {"messageId": "852", "fix": "969", "desc": "854"}, {"messageId": "855", "fix": "970", "desc": "857"}, {"messageId": "852", "fix": "971", "desc": "854"}, {"messageId": "855", "fix": "972", "desc": "857"}, {"messageId": "852", "fix": "973", "desc": "854"}, {"messageId": "855", "fix": "974", "desc": "857"}, {"messageId": "852", "fix": "975", "desc": "854"}, {"messageId": "855", "fix": "976", "desc": "857"}, {"messageId": "852", "fix": "977", "desc": "854"}, {"messageId": "855", "fix": "978", "desc": "857"}, {"messageId": "852", "fix": "979", "desc": "854"}, {"messageId": "855", "fix": "980", "desc": "857"}, {"messageId": "852", "fix": "981", "desc": "854"}, {"messageId": "855", "fix": "982", "desc": "857"}, {"messageId": "852", "fix": "983", "desc": "854"}, {"messageId": "855", "fix": "984", "desc": "857"}, {"messageId": "852", "fix": "985", "desc": "854"}, {"messageId": "855", "fix": "986", "desc": "857"}, {"messageId": "852", "fix": "987", "desc": "854"}, {"messageId": "855", "fix": "988", "desc": "857"}, {"messageId": "852", "fix": "989", "desc": "854"}, {"messageId": "855", "fix": "990", "desc": "857"}, {"messageId": "852", "fix": "991", "desc": "854"}, {"messageId": "855", "fix": "992", "desc": "857"}, {"messageId": "852", "fix": "993", "desc": "854"}, {"messageId": "855", "fix": "994", "desc": "857"}, {"messageId": "852", "fix": "995", "desc": "854"}, {"messageId": "855", "fix": "996", "desc": "857"}, {"messageId": "852", "fix": "997", "desc": "854"}, {"messageId": "855", "fix": "998", "desc": "857"}, {"messageId": "852", "fix": "999", "desc": "854"}, {"messageId": "855", "fix": "1000", "desc": "857"}, {"messageId": "852", "fix": "1001", "desc": "854"}, {"messageId": "855", "fix": "1002", "desc": "857"}, {"messageId": "852", "fix": "1003", "desc": "854"}, {"messageId": "855", "fix": "1004", "desc": "857"}, {"messageId": "852", "fix": "1005", "desc": "854"}, {"messageId": "855", "fix": "1006", "desc": "857"}, {"messageId": "852", "fix": "1007", "desc": "854"}, {"messageId": "855", "fix": "1008", "desc": "857"}, {"messageId": "852", "fix": "1009", "desc": "854"}, {"messageId": "855", "fix": "1010", "desc": "857"}, {"messageId": "852", "fix": "1011", "desc": "854"}, {"messageId": "855", "fix": "1012", "desc": "857"}, {"messageId": "852", "fix": "1013", "desc": "854"}, {"messageId": "855", "fix": "1014", "desc": "857"}, {"messageId": "852", "fix": "1015", "desc": "854"}, {"messageId": "855", "fix": "1016", "desc": "857"}, "Update the dependencies array to be: [loadCategories]", {"range": "1017", "text": "1018"}, "suggestUnknown", {"range": "1019", "text": "1020"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1021", "text": "1022"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [initializeApp]", {"range": "1023", "text": "1024"}, "Update the dependencies array to be: [selectedCategoryId, searchQuery, currentPage, isLocalSearch, allPrompts.length, performLocalSearch, loadPrompts]", {"range": "1025", "text": "1026"}, {"range": "1027", "text": "1020"}, {"range": "1028", "text": "1022"}, {"range": "1029", "text": "1020"}, {"range": "1030", "text": "1022"}, "Update the dependencies array to be: [initialQuery, loadSearchHistory]", {"range": "1031", "text": "1032"}, {"range": "1033", "text": "1020"}, {"range": "1034", "text": "1022"}, "Update the dependencies array to be: [loadSearchHistory]", {"range": "1035", "text": "1036"}, {"range": "1037", "text": "1020"}, {"range": "1038", "text": "1022"}, "Update the dependencies array to be: [isOpen, loadData]", {"range": "1039", "text": "1040"}, "Update the dependencies array to be: [initialParams]", {"range": "1041", "text": "1042"}, {"range": "1043", "text": "1020"}, {"range": "1044", "text": "1022"}, {"range": "1045", "text": "1020"}, {"range": "1046", "text": "1022"}, {"range": "1047", "text": "1020"}, {"range": "1048", "text": "1022"}, {"range": "1049", "text": "1020"}, {"range": "1050", "text": "1022"}, "Update the dependencies array to be: [addCopyRecord, history]", {"range": "1051", "text": "1052"}, {"range": "1053", "text": "1020"}, {"range": "1054", "text": "1022"}, {"range": "1055", "text": "1020"}, {"range": "1056", "text": "1022"}, {"range": "1057", "text": "1020"}, {"range": "1058", "text": "1022"}, {"range": "1059", "text": "1020"}, {"range": "1060", "text": "1022"}, {"range": "1061", "text": "1040"}, "Update the dependencies array to be: [isOpen, highlightedIndex, filteredHistory, value, onChange, onSearch]", {"range": "1062", "text": "1063"}, {"range": "1064", "text": "1020"}, {"range": "1065", "text": "1022"}, {"range": "1066", "text": "1020"}, {"range": "1067", "text": "1022"}, "replaceWithAlt", {"alt": "1068"}, {"range": "1069", "text": "1070"}, "Replace with `&quot;`.", {"alt": "1071"}, {"range": "1072", "text": "1073"}, "Replace with `&ldquo;`.", {"alt": "1074"}, {"range": "1075", "text": "1076"}, "Replace with `&#34;`.", {"alt": "1077"}, {"range": "1078", "text": "1079"}, "Replace with `&rdquo;`.", {"alt": "1068"}, {"range": "1080", "text": "1081"}, {"alt": "1071"}, {"range": "1082", "text": "1083"}, {"alt": "1074"}, {"range": "1084", "text": "1085"}, {"alt": "1077"}, {"range": "1086", "text": "1087"}, {"range": "1088", "text": "1020"}, {"range": "1089", "text": "1022"}, {"range": "1090", "text": "1020"}, {"range": "1091", "text": "1022"}, "replaceEmptyInterfaceWithSuper", {"range": "1092", "text": "1093"}, "Replace empty interface with a type alias.", {"range": "1094", "text": "1020"}, {"range": "1095", "text": "1022"}, {"range": "1096", "text": "1020"}, {"range": "1097", "text": "1022"}, {"range": "1098", "text": "1020"}, {"range": "1099", "text": "1022"}, {"range": "1100", "text": "1020"}, {"range": "1101", "text": "1022"}, {"range": "1102", "text": "1020"}, {"range": "1103", "text": "1022"}, {"range": "1104", "text": "1020"}, {"range": "1105", "text": "1022"}, {"range": "1106", "text": "1020"}, {"range": "1107", "text": "1022"}, {"range": "1108", "text": "1020"}, {"range": "1109", "text": "1022"}, {"range": "1110", "text": "1020"}, {"range": "1111", "text": "1022"}, {"range": "1112", "text": "1020"}, {"range": "1113", "text": "1022"}, {"range": "1114", "text": "1020"}, {"range": "1115", "text": "1022"}, {"range": "1116", "text": "1020"}, {"range": "1117", "text": "1022"}, {"range": "1118", "text": "1020"}, {"range": "1119", "text": "1022"}, {"range": "1120", "text": "1020"}, {"range": "1121", "text": "1022"}, {"range": "1122", "text": "1020"}, {"range": "1123", "text": "1022"}, {"range": "1124", "text": "1020"}, {"range": "1125", "text": "1022"}, {"range": "1126", "text": "1020"}, {"range": "1127", "text": "1022"}, {"range": "1128", "text": "1020"}, {"range": "1129", "text": "1022"}, {"range": "1130", "text": "1020"}, {"range": "1131", "text": "1022"}, {"range": "1132", "text": "1020"}, {"range": "1133", "text": "1022"}, {"range": "1134", "text": "1020"}, {"range": "1135", "text": "1022"}, {"range": "1136", "text": "1020"}, {"range": "1137", "text": "1022"}, {"range": "1138", "text": "1020"}, {"range": "1139", "text": "1022"}, {"range": "1140", "text": "1020"}, {"range": "1141", "text": "1022"}, {"range": "1142", "text": "1020"}, {"range": "1143", "text": "1022"}, {"range": "1144", "text": "1020"}, {"range": "1145", "text": "1022"}, {"range": "1146", "text": "1020"}, {"range": "1147", "text": "1022"}, {"range": "1148", "text": "1020"}, {"range": "1149", "text": "1022"}, {"range": "1150", "text": "1020"}, {"range": "1151", "text": "1022"}, {"range": "1152", "text": "1020"}, {"range": "1153", "text": "1022"}, {"range": "1154", "text": "1020"}, {"range": "1155", "text": "1022"}, {"range": "1156", "text": "1020"}, {"range": "1157", "text": "1022"}, {"range": "1158", "text": "1020"}, {"range": "1159", "text": "1022"}, {"range": "1160", "text": "1020"}, {"range": "1161", "text": "1022"}, {"range": "1162", "text": "1020"}, {"range": "1163", "text": "1022"}, {"range": "1164", "text": "1020"}, {"range": "1165", "text": "1022"}, {"range": "1166", "text": "1020"}, {"range": "1167", "text": "1022"}, {"range": "1168", "text": "1020"}, {"range": "1169", "text": "1022"}, {"range": "1170", "text": "1020"}, {"range": "1171", "text": "1022"}, {"range": "1172", "text": "1020"}, {"range": "1173", "text": "1022"}, {"range": "1174", "text": "1020"}, {"range": "1175", "text": "1022"}, {"range": "1176", "text": "1020"}, {"range": "1177", "text": "1022"}, {"range": "1178", "text": "1020"}, {"range": "1179", "text": "1022"}, [1772, 1774], "[loadCategories]", [3917, 3920], "unknown", [3917, 3920], "never", [3980, 3982], "[initializeApp]", [5530, 5630], "[selectedCategoryId, searchQuery, currentPage, isLocalSearch, allPrompts.length, performLocalSearch, loadPrompts]", [14791, 14794], [14791, 14794], [1285, 1288], [1285, 1288], [3115, 3117], "[initialQuery, loadSearchHistory]", [3207, 3210], [3207, 3210], [3941, 3943], "[loadSearchHistory]", [4007, 4010], [4007, 4010], [2085, 2093], "[isOpen, loadData]", [2779, 2810], "[initialParams]", [10693, 10696], [10693, 10696], [11371, 11374], [11371, 11374], [2293, 2296], [2293, 2296], [2376, 2379], [2376, 2379], [2406, 2415], "[add<PERSON><PERSON><PERSON><PERSON><PERSON>, history]", [6388, 6391], [6388, 6391], [6427, 6430], [6427, 6430], [5114, 5117], [5114, 5117], [4002, 4005], [4002, 4005], [1634, 1642], [2957, 3000], "[is<PERSON>pen, highlightedIndex, filteredHistory, value, onChange, onSearch]", [756, 759], [756, 759], [1147, 1150], [1147, 1150], "&quot;", [1674, 1697], " 个包含 &quot;\n                ", "&ldquo;", [1674, 1697], " 个包含 &ldquo;\n                ", "&#34;", [1674, 1697], " 个包含 &#34;\n                ", "&rdquo;", [1674, 1697], " 个包含 &rdquo;\n                ", [1761, 1782], "&quot; 的提示词\n              ", [1761, 1782], "&ldquo; 的提示词\n              ", [1761, 1782], "&#34; 的提示词\n              ", [1761, 1782], "&rdquo; 的提示词\n              ", [1831, 1834], [1831, 1834], [616, 619], [616, 619], [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [5108, 5111], [5108, 5111], [5456, 5459], [5456, 5459], [5470, 5473], [5470, 5473], [192, 195], [192, 195], [223, 226], [223, 226], [354, 357], [354, 357], [587, 590], [587, 590], [593, 596], [593, 596], [756, 759], [756, 759], [996, 999], [996, 999], [1537, 1540], [1537, 1540], [2429, 2432], [2429, 2432], [4632, 4635], [4632, 4635], [143, 146], [143, 146], [163, 166], [163, 166], [177, 180], [177, 180], [3733, 3736], [3733, 3736], [4650, 4653], [4650, 4653], [6262, 6265], [6262, 6265], [2552, 2555], [2552, 2555], [1143, 1146], [1143, 1146], [3379, 3382], [3379, 3382], [986, 989], [986, 989], [1002, 1005], [1002, 1005], [1217, 1220], [1217, 1220], [1771, 1774], [1771, 1774], [2166, 2169], [2166, 2169], [3784, 3787], [3784, 3787], [4726, 4729], [4726, 4729], [5430, 5433], [5430, 5433], [8147, 8150], [8147, 8150], [9629, 9632], [9629, 9632], [3066, 3069], [3066, 3069], [6288, 6291], [6288, 6291], [564, 567], [564, 567], [2138, 2141], [2138, 2141], [1784, 1787], [1784, 1787], [1794, 1797], [1794, 1797], [2142, 2145], [2142, 2145], [2152, 2155], [2152, 2155], [3340, 3343], [3340, 3343], [2163, 2166], [2163, 2166], [4968, 4971], [4968, 4971]]