/**
 * 批量操作工具
 * 支持批量选择、删除、移动分类等操作
 */

import * as api from '@/lib/api/client'

// 批量操作结果类型
export interface BatchOperationResult {
  success: boolean
  total: number
  successful: number
  failed: number
  errors: string[]
  warnings: string[]
}

// 批量选择状态管理
export class BatchSelectionManager {
  private selectedIds = new Set<string>()
  private callbacks: Array<(selectedIds: Set<string>) => void> = []

  // 添加选择变化监听器
  onSelectionChange(callback: (selectedIds: Set<string>) => void) {
    this.callbacks.push(callback)
  }

  // 移除监听器
  removeListener(callback: (selectedIds: Set<string>) => void) {
    this.callbacks = this.callbacks.filter(cb => cb !== callback)
  }

  // 通知选择变化
  private notifyChange() {
    this.callbacks.forEach(callback => callback(new Set(this.selectedIds)))
  }

  // 选择单个项目
  select(id: string) {
    this.selectedIds.add(id)
    this.notifyChange()
  }

  // 取消选择单个项目
  deselect(id: string) {
    this.selectedIds.delete(id)
    this.notifyChange()
  }

  // 切换选择状态
  toggle(id: string) {
    if (this.selectedIds.has(id)) {
      this.deselect(id)
    } else {
      this.select(id)
    }
  }

  // 全选
  selectAll(ids: string[]) {
    ids.forEach(id => this.selectedIds.add(id))
    this.notifyChange()
  }

  // 取消全选
  deselectAll() {
    this.selectedIds.clear()
    this.notifyChange()
  }

  // 反选
  invertSelection(allIds: string[]) {
    const newSelection = new Set<string>()
    allIds.forEach(id => {
      if (!this.selectedIds.has(id)) {
        newSelection.add(id)
      }
    })
    this.selectedIds = newSelection
    this.notifyChange()
  }

  // 获取选中的项目
  getSelected(): string[] {
    return Array.from(this.selectedIds)
  }

  // 获取选中数量
  getSelectedCount(): number {
    return this.selectedIds.size
  }

  // 检查是否选中
  isSelected(id: string): boolean {
    return this.selectedIds.has(id)
  }

  // 检查是否有选中项目
  hasSelection(): boolean {
    return this.selectedIds.size > 0
  }

  // 检查是否全选
  isAllSelected(allIds: string[]): boolean {
    return allIds.length > 0 && allIds.every(id => this.selectedIds.has(id))
  }

  // 检查是否部分选中
  isPartiallySelected(allIds: string[]): boolean {
    const selectedCount = allIds.filter(id => this.selectedIds.has(id)).length
    return selectedCount > 0 && selectedCount < allIds.length
  }
}

/**
 * 批量删除提示词
 */
export async function batchDeletePrompts(promptIds: string[]): Promise<BatchOperationResult> {
  const result: BatchOperationResult = {
    success: false,
    total: promptIds.length,
    successful: 0,
    failed: 0,
    errors: [],
    warnings: []
  }

  if (promptIds.length === 0) {
    result.warnings.push('没有选择要删除的提示词')
    return result
  }

  console.log(`🚀 开始批量删除 ${promptIds.length} 个提示词...`)

  for (const promptId of promptIds) {
    try {
      await api.deletePrompt(promptId)
      result.successful++
      console.log(`✅ 删除提示词 ${promptId} 成功`)
    } catch (error) {
      result.failed++
      const errorMessage = `删除提示词 ${promptId} 失败: ${(error as Error).message}`
      result.errors.push(errorMessage)
      console.error(`❌ ${errorMessage}`)
    }
  }

  result.success = result.successful > 0
  console.log(`✅ 批量删除完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)

  return result
}

/**
 * 批量移动提示词到指定分类
 */
export async function batchMovePrompts(promptIds: string[], targetCategoryId: string | null): Promise<BatchOperationResult> {
  const result: BatchOperationResult = {
    success: false,
    total: promptIds.length,
    successful: 0,
    failed: 0,
    errors: [],
    warnings: []
  }

  if (promptIds.length === 0) {
    result.warnings.push('没有选择要移动的提示词')
    return result
  }

  const categoryName = targetCategoryId ? '指定分类' : '未分类'
  console.log(`🚀 开始批量移动 ${promptIds.length} 个提示词到 ${categoryName}...`)

  for (const promptId of promptIds) {
    try {
      // 获取提示词详情
      const prompt = await api.getPromptById(promptId)

      // 更新分类
      await api.updatePrompt(promptId, {
        title: prompt.title,
        content: prompt.content,
        categoryId: targetCategoryId,
        description: prompt.description
      })
      
      result.successful++
      console.log(`✅ 移动提示词 ${promptId} 成功`)
    } catch (error) {
      result.failed++
      const errorMessage = `移动提示词 ${promptId} 失败: ${(error as Error).message}`
      result.errors.push(errorMessage)
      console.error(`❌ ${errorMessage}`)
    }
  }

  result.success = result.successful > 0
  console.log(`✅ 批量移动完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)

  return result
}

/**
 * 批量复制提示词
 */
export async function batchCopyPrompts(promptIds: string[]): Promise<BatchOperationResult> {
  const result: BatchOperationResult = {
    success: false,
    total: promptIds.length,
    successful: 0,
    failed: 0,
    errors: [],
    warnings: []
  }

  if (promptIds.length === 0) {
    result.warnings.push('没有选择要复制的提示词')
    return result
  }

  console.log(`🚀 开始批量复制 ${promptIds.length} 个提示词...`)

  for (const promptId of promptIds) {
    try {
      // 获取提示词详情
      const prompt = await api.getPromptById(promptId)

      // 创建副本
      await api.createPrompt({
        title: `${prompt.title} (副本)`,
        content: prompt.content,
        categoryId: prompt.categoryId,
        description: prompt.description
      })
      
      result.successful++
      console.log(`✅ 复制提示词 ${promptId} 成功`)
    } catch (error) {
      result.failed++
      const errorMessage = `复制提示词 ${promptId} 失败: ${(error as Error).message}`
      result.errors.push(errorMessage)
      console.error(`❌ ${errorMessage}`)
    }
  }

  result.success = result.successful > 0
  console.log(`✅ 批量复制完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)

  return result
}

/**
 * 批量导出选中的提示词
 */
export async function batchExportPrompts(promptIds: string[]): Promise<{ success: boolean; data?: string; error?: string }> {
  if (promptIds.length === 0) {
    return { success: false, error: '没有选择要导出的提示词' }
  }

  try {
    console.log(`🚀 开始批量导出 ${promptIds.length} 个提示词...`)

    // 获取所有选中的提示词详情
    const prompts = await Promise.all(
      promptIds.map(id => api.getPromptById(id))
    )

    // 获取分类信息用于映射
    const categories = await api.getCategories()
    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]))

    // 构建导出数据
    const exportData = {
      prompts: prompts.map(prompt => ({
        ...prompt,
        categoryName: categoryMap.get(prompt.categoryId) || '未分类'
      })),
      exportTime: new Date().toISOString(),
      version: '1.0.0',
      type: 'batch_export'
    }

    const jsonData = JSON.stringify(exportData, null, 2)
    console.log(`✅ 批量导出完成: ${prompts.length} 个提示词`)

    return { success: true, data: jsonData }
  } catch (error) {
    const errorMessage = `批量导出失败: ${(error as Error).message}`
    console.error(`❌ ${errorMessage}`)
    return { success: false, error: errorMessage }
  }
}

/**
 * 获取批量操作的确认消息
 */
export function getBatchOperationConfirmMessage(operation: string, count: number, details?: string): string {
  const messages = {
    delete: `确定要删除选中的 ${count} 个提示词吗？此操作不可撤销。`,
    move: `确定要将选中的 ${count} 个提示词移动到${details || '指定分类'}吗？`,
    copy: `确定要复制选中的 ${count} 个提示词吗？将创建 ${count} 个副本。`,
    export: `确定要导出选中的 ${count} 个提示词吗？`
  }

  return messages[operation as keyof typeof messages] || `确定要对选中的 ${count} 个项目执行 ${operation} 操作吗？`
}
