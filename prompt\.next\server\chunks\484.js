"use strict";exports.id=484,exports.ids=[484],exports.modules={43:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43210);c(60687);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},363:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(a,b,c)=>{c.d(b,{Oh:()=>f});var d=c(43210),e=0;function f(){d.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??g()),document.body.insertAdjacentElement("beforeend",a[1]??g()),e++,()=>{1===e&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),e--}},[])}function g(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},2030:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},2255:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=c(19169);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},5144:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=c(51550),e=c(59656);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5334:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=c(59008),e=c(59154),f=c(75076);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d,staleTime:f}=a;return -1!==f?Date.now()<c+f?e.PrefetchCacheEntryStatus.fresh:e.PrefetchCacheEntryStatus.stale:Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6361:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"assignLocation",{enumerable:!0,get:function(){return e}});let d=c(96127);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8830:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"reducer",{enumerable:!0,get:function(){return d}}),c(59154),c(25232),c(29651),c(28627),c(78866),c(75076),c(97936),c(35429);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9707:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addSearchParamsToPageSegments:function(){return l},handleAliasedPrefetchEntry:function(){return k}});let d=c(83913),e=c(89752),f=c(86770),g=c(57391),h=c(33123),i=c(33898),j=c(59435);function k(a,b,c,k,m){let n,o=b.tree,p=b.cache,q=(0,g.createHrefFromUrl)(k);if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=l(c,Object.fromEntries(k.searchParams));let{seedData:g,isRootRender:j,pathToSegment:m}=b,r=["",...m];c=l(c,Object.fromEntries(k.searchParams));let s=(0,f.applyRouterStatePatchToTree)(r,o,c,q),t=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];t.loading=g[3],t.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,t,p,c,g)}else t.rsc=p.rsc,t.prefetchRsc=p.prefetchRsc,t.loading=p.loading,t.parallelRoutes=new Map(p.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,t,p,b);s&&(o=s,p=t,n=!0)}return!!n&&(m.patchedTree=o,m.cache=p,m.canonicalUrl=q,m.hashFragment=k.hash,(0,j.handleMutable)(b,m))}function l(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=l(c,b);return[c,g,...f]}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},10966:(a,b,c)=>{c.d(b,{H_:()=>cF,UC:()=>cB,YJ:()=>cC,q7:()=>cE,VF:()=>cI,JU:()=>cD,ZL:()=>cA,z6:()=>cG,hN:()=>cH,bL:()=>cy,wv:()=>cJ,Pb:()=>cK,G5:()=>cM,ZP:()=>cL,l9:()=>cz});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(65551),i=c(14163),j=c(9510),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963);let p=["top","right","bottom","left"],q=Math.min,r=Math.max,s=Math.round,t=Math.floor,u=a=>({x:a,y:a}),v={left:"right",right:"left",bottom:"top",top:"bottom"},w={start:"end",end:"start"};function x(a,b){return"function"==typeof a?a(b):a}function y(a){return a.split("-")[0]}function z(a){return a.split("-")[1]}function A(a){return"x"===a?"y":"x"}function B(a){return"y"===a?"height":"width"}let C=new Set(["top","bottom"]);function D(a){return C.has(y(a))?"y":"x"}function E(a){return a.replace(/start|end/g,a=>w[a])}let F=["left","right"],G=["right","left"],H=["top","bottom"],I=["bottom","top"];function J(a){return a.replace(/left|right|bottom|top/g,a=>v[a])}function K(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function L(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function M(a,b,c){let d,{reference:e,floating:f}=a,g=D(b),h=A(D(b)),i=B(h),j=y(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(z(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let N=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=M(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=M(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function O(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=x(b,a),o=K(n),p=h[m?"floating"===l?"reference":"floating":l],q=L(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=L(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function P(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function Q(a){return p.some(b=>a[b]>=0)}let R=new Set(["left","top"]);async function S(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=y(c),h=z(c),i="y"===D(c),j=R.has(g)?-1:1,k=f&&i?-1:1,l=x(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function T(){return"undefined"!=typeof window}function U(a){return X(a)?(a.nodeName||"").toLowerCase():"#document"}function V(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function W(a){var b;return null==(b=(X(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function X(a){return!!T()&&(a instanceof Node||a instanceof V(a).Node)}function Y(a){return!!T()&&(a instanceof Element||a instanceof V(a).Element)}function Z(a){return!!T()&&(a instanceof HTMLElement||a instanceof V(a).HTMLElement)}function $(a){return!!T()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof V(a).ShadowRoot)}let _=new Set(["inline","contents"]);function aa(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=al(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!_.has(e)}let ab=new Set(["table","td","th"]),ac=[":popover-open",":modal"];function ad(a){return ac.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ae=["transform","translate","scale","rotate","perspective"],af=["transform","translate","scale","rotate","perspective","filter"],ag=["paint","layout","strict","content"];function ah(a){let b=ai(),c=Y(a)?al(a):a;return ae.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||af.some(a=>(c.willChange||"").includes(a))||ag.some(a=>(c.contain||"").includes(a))}function ai(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aj=new Set(["html","body","#document"]);function ak(a){return aj.has(U(a))}function al(a){return V(a).getComputedStyle(a)}function am(a){return Y(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function an(a){if("html"===U(a))return a;let b=a.assignedSlot||a.parentNode||$(a)&&a.host||W(a);return $(b)?b.host:b}function ao(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=an(b);return ak(c)?b.ownerDocument?b.ownerDocument.body:b.body:Z(c)&&aa(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=V(e);if(f){let a=ap(g);return b.concat(g,g.visualViewport||[],aa(e)?e:[],a&&c?ao(a):[])}return b.concat(e,ao(e,[],c))}function ap(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aq(a){let b=al(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=Z(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=s(c)!==f||s(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function ar(a){return Y(a)?a:a.contextElement}function as(a){let b=ar(a);if(!Z(b))return u(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aq(b),g=(f?s(c.width):c.width)/d,h=(f?s(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let at=u(0);function au(a){let b=V(a);return ai()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:at}function av(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ar(a),h=u(1);b&&(d?Y(d)&&(h=as(d)):h=as(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===V(g))&&e)?au(g):u(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=V(g),b=d&&Y(d)?V(d):d,c=a,e=ap(c);for(;e&&d&&b!==c;){let a=as(e),b=e.getBoundingClientRect(),d=al(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=ap(c=V(e))}}return L({width:l,height:m,x:j,y:k})}function aw(a,b){let c=am(a).scrollLeft;return b?b.left+c:av(W(a)).left+c}function ax(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aw(a,d)),y:d.top+b.scrollTop}}let ay=new Set(["absolute","fixed"]);function az(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=V(a),d=W(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=ai();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=W(a),c=am(a),d=a.ownerDocument.body,e=r(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=r(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aw(a),h=-c.scrollTop;return"rtl"===al(d).direction&&(g+=r(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(W(a));else if(Y(b))d=function(a,b){let c=av(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=Z(a)?as(a):u(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=au(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return L(d)}function aA(a){return"static"===al(a).position}function aB(a,b){if(!Z(a)||"fixed"===al(a).position)return null;if(b)return b(a);let c=a.offsetParent;return W(a)===c&&(c=c.ownerDocument.body),c}function aC(a,b){var c;let d=V(a);if(ad(a))return d;if(!Z(a)){let b=an(a);for(;b&&!ak(b);){if(Y(b)&&!aA(b))return b;b=an(b)}return d}let e=aB(a,b);for(;e&&(c=e,ab.has(U(c)))&&aA(e);)e=aB(e,b);return e&&ak(e)&&aA(e)&&!ah(e)?d:e||function(a){let b=an(a);for(;Z(b)&&!ak(b);){if(ah(b))return b;if(ad(b))break;b=an(b)}return null}(a)||d}let aD=async function(a){let b=this.getOffsetParent||aC,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=Z(b),e=W(b),f="fixed"===c,g=av(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=u(0);if(d||!d&&!f)if(("body"!==U(b)||aa(e))&&(h=am(b)),d){let a=av(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aw(e));f&&!d&&e&&(i.x=aw(e));let j=!e||d||f?u(0):ax(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},aE={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=W(d),h=!!b&&ad(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=u(1),k=u(0),l=Z(d);if((l||!l&&!f)&&(("body"!==U(d)||aa(g))&&(i=am(d)),Z(d))){let a=av(d);j=as(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?u(0):ax(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:W,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?ad(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ao(a,[],!1).filter(a=>Y(a)&&"body"!==U(a)),e=null,f="fixed"===al(a).position,g=f?an(a):a;for(;Y(g)&&!ak(g);){let b=al(g),c=ah(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&ay.has(e.position)||aa(g)&&!c&&function a(b,c){let d=an(b);return!(d===c||!Y(d)||ak(d))&&("fixed"===al(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=an(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=az(b,c,e);return a.top=r(d.top,a.top),a.right=q(d.right,a.right),a.bottom=q(d.bottom,a.bottom),a.left=r(d.left,a.left),a},az(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:aC,getElementRects:aD,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aq(a);return{width:b,height:c}},getScale:as,isElement:Y,isRTL:function(a){return"rtl"===al(a).direction}};function aF(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let aG=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=x(a,b)||{};if(null==j)return{};let l=K(k),m={x:c,y:d},n=A(D(e)),o=B(n),p=await g.getDimensions(j),s="y"===n,t=s?"clientHeight":"clientWidth",u=f.reference[o]+f.reference[n]-m[n]-f.floating[o],v=m[n]-f.reference[n],w=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),y=w?w[t]:0;y&&await (null==g.isElement?void 0:g.isElement(w))||(y=h.floating[t]||f.floating[o]);let C=y/2-p[o]/2-1,E=q(l[s?"top":"left"],C),F=q(l[s?"bottom":"right"],C),G=y-p[o]-F,H=y/2-p[o]/2+(u/2-v/2),I=r(E,q(H,G)),J=!i.arrow&&null!=z(e)&&H!==I&&f.reference[o]/2-(H<E?E:F)-p[o]/2<0,L=J?H<E?H-E:H-G:0;return{[n]:m[n]+L,data:{[n]:I,centerOffset:H-I-L,...J&&{alignmentOffset:L}},reset:J}}});var aH=c(51215),aI="undefined"!=typeof document?d.useLayoutEffect:function(){};function aJ(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!aJ(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!aJ(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function aK(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aL(a,b){let c=aK(a);return Math.round(b*c)/c}function aM(a){let b=d.useRef(a);return aI(()=>{b.current=a}),b}var aN=c(60687),aO=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aN.jsx)(i.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aN.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aO.displayName="Arrow";var aP=c(13495),aQ=c(66156),aR=c(18853),aS="Popper",[aT,aU]=(0,g.A)(aS),[aV,aW]=aT(aS),aX=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aN.jsx)(aV,{scope:b,anchor:e,onAnchorChange:f,children:c})};aX.displayName=aS;var aY="PopperAnchor",aZ=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...g}=a,h=aW(aY,c),j=d.useRef(null),k=(0,f.s)(b,j);return d.useEffect(()=>{h.onAnchorChange(e?.current||j.current)}),e?null:(0,aN.jsx)(i.sG.div,{...g,ref:k})});aZ.displayName=aY;var a$="PopperContent",[a_,a0]=aT(a$),a1=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:g=0,align:h="center",alignOffset:j=0,arrowPadding:k=0,avoidCollisions:l=!0,collisionBoundary:m=[],collisionPadding:n=0,sticky:o="partial",hideWhenDetached:p=!1,updatePositionStrategy:s="optimized",onPlaced:u,...v}=a,w=aW(a$,c),[C,K]=d.useState(null),L=(0,f.s)(b,a=>K(a)),[M,T]=d.useState(null),U=(0,aR.X)(M),V=U?.width??0,X=U?.height??0,Y="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},Z=Array.isArray(m)?m:[m],$=Z.length>0,_={padding:Y,boundary:Z.filter(a5),altBoundary:$},{refs:aa,floatingStyles:ab,placement:ac,isPositioned:ad,middlewareData:ae}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);aJ(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aM(j),C=aM(f),D=aM(k),E=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:aE,...c},f={...e.platform,_c:d};return N(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!aJ(z.current,b)&&(z.current=b,aH.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);aI(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=d.useRef(!1);aI(()=>(F.current=!0,()=>{F.current=!1}),[]),aI(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=d.useMemo(()=>({reference:v,floating:w}),[v,w]),I=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=aL(H.floating,l.x),d=aL(H.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...aK(H.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,H.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:e+("center"!==h?"-"+h:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=ar(a),l=f||g?[...k?ao(k):[],...ao(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=W(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=t(l),p=t(e.clientWidth-(k+m)),s={rootMargin:-o+"px "+-p+"px "+-t(e.clientHeight-(l+n))+"px "+-t(k)+"px",threshold:r(0,q(1,i))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==i){if(!u)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||aF(j,a.getBoundingClientRect())||g(),u=!1}try{d=new IntersectionObserver(v,{...s,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,s)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?av(a):null;return j&&function b(){let d=av(a);p&&!aF(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===s}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await S(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:g+X,alignmentAxis:j}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=x(a,b),j={x:c,y:d},k=await O(b,i),l=D(y(e)),m=A(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=r(c,q(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=r(c,q(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===o?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=x(a,b),k={x:c,y:d},l=D(e),m=A(l),n=k[m],o=k[l],p=x(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=R.has(y(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,..._}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=x(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=y(h),v=D(k),w=y(k)===k,C=await (null==l.isRTL?void 0:l.isRTL(m.floating)),K=p||(w||!s?[J(k)]:function(a){let b=J(a);return[E(a),b,E(b)]}(k)),L="none"!==r;!p&&L&&K.push(...function(a,b,c,d){let e=z(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?G:F;return b?F:G;case"left":case"right":return b?H:I;default:return[]}}(y(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(E)))),f}(k,s,r,C));let M=[k,...K],N=await O(b,t),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&P.push(N[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=z(a),e=A(D(a)),f=B(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=J(g)),[g,J(g)]}(h,j,C);P.push(N[a[0]],N[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=M[a];if(b&&("alignment"!==o||v===D(b)||Q.every(a=>a.overflows[0]>0&&D(a.placement)===v)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=Q.filter(a=>{if(L){let b=D(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({..._}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=x(a,b),m=await O(b,l),n=y(g),o=z(g),p="y"===D(g),{width:s,height:t}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let u=t-m.top-m.bottom,v=s-m.left-m.right,w=q(t-m[e],u),A=q(s-m[f],v),B=!b.middlewareData.shift,C=w,E=A;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(E=v),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(C=u),B&&!o){let a=r(m.left,0),b=r(m.right,0),c=r(m.top,0),d=r(m.bottom,0);p?E=s-2*(0!==a||0!==b?a+b:r(m.left,m.right)):C=t-2*(0!==c||0!==d?c+d:r(m.top,m.bottom))}await k({...b,availableWidth:E,availableHeight:C});let F=await i.getDimensions(j.floating);return s!==F.width||t!==F.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({..._,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),M&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?aG({element:c.current,padding:d}).fn(b):{}:c?aG({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:M,padding:k}),a6({arrowWidth:V,arrowHeight:X}),p&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=x(a,b);switch(d){case"referenceHidden":{let a=P(await O(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Q(a)}}}case"escaped":{let a=P(await O(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:Q(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",..._})]}),[af,ag]=a7(ac),ah=(0,aP.c)(u);(0,aQ.N)(()=>{ad&&ah?.()},[ad,ah]);let ai=ae.arrow?.x,aj=ae.arrow?.y,ak=ae.arrow?.centerOffset!==0,[al,am]=d.useState();return(0,aQ.N)(()=>{C&&am(window.getComputedStyle(C).zIndex)},[C]),(0,aN.jsx)("div",{ref:aa.setFloating,"data-radix-popper-content-wrapper":"",style:{...ab,transform:ad?ab.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:al,"--radix-popper-transform-origin":[ae.transformOrigin?.x,ae.transformOrigin?.y].join(" "),...ae.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aN.jsx)(a_,{scope:c,placedSide:af,onArrowChange:T,arrowX:ai,arrowY:aj,shouldHideArrow:ak,children:(0,aN.jsx)(i.sG.div,{"data-side":af,"data-align":ag,...v,ref:L,style:{...v.style,animation:ad?void 0:"none"}})})})});a1.displayName=a$;var a2="PopperArrow",a3={top:"bottom",right:"left",bottom:"top",left:"right"},a4=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=a0(a2,c),f=a3[e.placedSide];return(0,aN.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aN.jsx)(aO,{...d,ref:b,style:{...d.style,display:"block"}})})});function a5(a){return null!==a}a4.displayName=a2;var a6=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a7(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a7(a){let[b,c="center"]=a.split("-");return[b,c]}var a8=c(25028),a9=c(46059),ba=c(72942),bb=c(8730),bc=c(63376),bd=c(42247),be=["Enter"," "],bf=["ArrowUp","PageDown","End"],bg=["ArrowDown","PageUp","Home",...bf],bh={ltr:[...be,"ArrowRight"],rtl:[...be,"ArrowLeft"]},bi={ltr:["ArrowLeft"],rtl:["ArrowRight"]},bj="Menu",[bk,bl,bm]=(0,j.N)(bj),[bn,bo]=(0,g.A)(bj,[bm,aU,ba.RG]),bp=aU(),bq=(0,ba.RG)(),[br,bs]=bn(bj),[bt,bu]=bn(bj),bv=a=>{let{__scopeMenu:b,open:c=!1,children:e,dir:f,onOpenChange:g,modal:h=!0}=a,i=bp(b),[j,l]=d.useState(null),m=d.useRef(!1),n=(0,aP.c)(g),o=(0,k.jH)(f);return d.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,aN.jsx)(aX,{...i,children:(0,aN.jsx)(br,{scope:b,open:c,onOpenChange:n,content:j,onContentChange:l,children:(0,aN.jsx)(bt,{scope:b,onClose:d.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:h,children:e})})})};bv.displayName=bj;var bw=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bp(c);return(0,aN.jsx)(aZ,{...e,...d,ref:b})});bw.displayName="MenuAnchor";var bx="MenuPortal",[by,bz]=bn(bx,{forceMount:void 0}),bA=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=bs(bx,b);return(0,aN.jsx)(by,{scope:b,forceMount:c,children:(0,aN.jsx)(a9.C,{present:c||f.open,children:(0,aN.jsx)(a8.Z,{asChild:!0,container:e,children:d})})})};bA.displayName=bx;var bB="MenuContent",[bC,bD]=bn(bB),bE=d.forwardRef((a,b)=>{let c=bz(bB,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=bs(bB,a.__scopeMenu),g=bu(bB,a.__scopeMenu);return(0,aN.jsx)(bk.Provider,{scope:a.__scopeMenu,children:(0,aN.jsx)(a9.C,{present:d||f.open,children:(0,aN.jsx)(bk.Slot,{scope:a.__scopeMenu,children:g.modal?(0,aN.jsx)(bF,{...e,ref:b}):(0,aN.jsx)(bG,{...e,ref:b})})})})}),bF=d.forwardRef((a,b)=>{let c=bs(bB,a.__scopeMenu),g=d.useRef(null),h=(0,f.s)(b,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,bc.Eq)(a)},[]),(0,aN.jsx)(bI,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),bG=d.forwardRef((a,b)=>{let c=bs(bB,a.__scopeMenu);return(0,aN.jsx)(bI,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),bH=(0,bb.TL)("MenuContent.ScrollLock"),bI=d.forwardRef((a,b)=>{let{__scopeMenu:c,loop:g=!1,trapFocus:h,onOpenAutoFocus:i,onCloseAutoFocus:j,disableOutsidePointerEvents:k,onEntryFocus:o,onEscapeKeyDown:p,onPointerDownOutside:q,onFocusOutside:r,onInteractOutside:s,onDismiss:t,disableOutsideScroll:u,...v}=a,w=bs(bB,c),x=bu(bB,c),y=bp(c),z=bq(c),A=bl(c),[B,C]=d.useState(null),D=d.useRef(null),E=(0,f.s)(b,D,w.onContentChange),F=d.useRef(0),G=d.useRef(""),H=d.useRef(0),I=d.useRef(null),J=d.useRef("right"),K=d.useRef(0),L=u?bd.A:d.Fragment;d.useEffect(()=>()=>window.clearTimeout(F.current),[]),(0,m.Oh)();let M=d.useCallback(a=>J.current===I.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,I.current?.area),[]);return(0,aN.jsx)(bC,{scope:c,searchRef:G,onItemEnter:d.useCallback(a=>{M(a)&&a.preventDefault()},[M]),onItemLeave:d.useCallback(a=>{M(a)||(D.current?.focus(),C(null))},[M]),onTriggerLeave:d.useCallback(a=>{M(a)&&a.preventDefault()},[M]),pointerGraceTimerRef:H,onPointerGraceIntentChange:d.useCallback(a=>{I.current=a},[]),children:(0,aN.jsx)(L,{...u?{as:bH,allowPinchZoom:!0}:void 0,children:(0,aN.jsx)(n.n,{asChild:!0,trapped:h,onMountAutoFocus:(0,e.m)(i,a=>{a.preventDefault(),D.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:j,children:(0,aN.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:k,onEscapeKeyDown:p,onPointerDownOutside:q,onFocusOutside:r,onInteractOutside:s,onDismiss:t,children:(0,aN.jsx)(ba.bL,{asChild:!0,...z,dir:x.dir,orientation:"vertical",loop:g,currentTabStopId:B,onCurrentTabStopIdChange:C,onEntryFocus:(0,e.m)(o,a=>{x.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,aN.jsx)(a1,{role:"menu","aria-orientation":"vertical","data-state":b8(w.open),"data-radix-menu-content":"",dir:x.dir,...y,...v,ref:E,style:{outline:"none",...v.style},onKeyDown:(0,e.m)(v.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=G.current+a,c=A().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){G.current=b,window.clearTimeout(F.current),""!==b&&(F.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=D.current;if(a.target!==e||!bg.includes(a.key))return;a.preventDefault();let f=A().filter(a=>!a.disabled).map(a=>a.ref.current);bf.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,e.m)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(F.current),G.current="")}),onPointerMove:(0,e.m)(a.onPointerMove,cb(a=>{let b=a.target,c=K.current!==a.clientX;a.currentTarget.contains(b)&&c&&(J.current=a.clientX>K.current?"right":"left",K.current=a.clientX)}))})})})})})})});bE.displayName=bB;var bJ=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{role:"group",...d,ref:b})});bJ.displayName="MenuGroup";var bK=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{...d,ref:b})});bK.displayName="MenuLabel";var bL="MenuItem",bM="menu.itemSelect",bN=d.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:g,...h}=a,j=d.useRef(null),k=bu(bL,a.__scopeMenu),l=bD(bL,a.__scopeMenu),m=(0,f.s)(b,j),n=d.useRef(!1);return(0,aN.jsx)(bO,{...h,ref:m,disabled:c,onClick:(0,e.m)(a.onClick,()=>{let a=j.current;if(!c&&a){let b=new CustomEvent(bM,{bubbles:!0,cancelable:!0});a.addEventListener(bM,a=>g?.(a),{once:!0}),(0,i.hO)(a,b),b.defaultPrevented?n.current=!1:k.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),n.current=!0},onPointerUp:(0,e.m)(a.onPointerUp,a=>{n.current||a.currentTarget?.click()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=""!==l.searchRef.current;c||b&&" "===a.key||be.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});bN.displayName=bL;var bO=d.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:g=!1,textValue:h,...j}=a,k=bD(bL,c),l=bq(c),m=d.useRef(null),n=(0,f.s)(b,m),[o,p]=d.useState(!1),[q,r]=d.useState("");return d.useEffect(()=>{let a=m.current;a&&r((a.textContent??"").trim())},[j.children]),(0,aN.jsx)(bk.ItemSlot,{scope:c,disabled:g,textValue:h??q,children:(0,aN.jsx)(ba.q7,{asChild:!0,...l,focusable:!g,children:(0,aN.jsx)(i.sG.div,{role:"menuitem","data-highlighted":o?"":void 0,"aria-disabled":g||void 0,"data-disabled":g?"":void 0,...j,ref:n,onPointerMove:(0,e.m)(a.onPointerMove,cb(a=>{g?k.onItemLeave(a):(k.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,e.m)(a.onPointerLeave,cb(a=>k.onItemLeave(a))),onFocus:(0,e.m)(a.onFocus,()=>p(!0)),onBlur:(0,e.m)(a.onBlur,()=>p(!1))})})})}),bP=d.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,aN.jsx)(bX,{scope:a.__scopeMenu,checked:c,children:(0,aN.jsx)(bN,{role:"menuitemcheckbox","aria-checked":b9(c)?"mixed":c,...f,ref:b,"data-state":ca(c),onSelect:(0,e.m)(f.onSelect,()=>d?.(!!b9(c)||!c),{checkForDefaultPrevented:!1})})})});bP.displayName="MenuCheckboxItem";var bQ="MenuRadioGroup",[bR,bS]=bn(bQ,{value:void 0,onValueChange:()=>{}}),bT=d.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=(0,aP.c)(d);return(0,aN.jsx)(bR,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,aN.jsx)(bJ,{...e,ref:b})})});bT.displayName=bQ;var bU="MenuRadioItem",bV=d.forwardRef((a,b)=>{let{value:c,...d}=a,f=bS(bU,a.__scopeMenu),g=c===f.value;return(0,aN.jsx)(bX,{scope:a.__scopeMenu,checked:g,children:(0,aN.jsx)(bN,{role:"menuitemradio","aria-checked":g,...d,ref:b,"data-state":ca(g),onSelect:(0,e.m)(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});bV.displayName=bU;var bW="MenuItemIndicator",[bX,bY]=bn(bW,{checked:!1}),bZ=d.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=bY(bW,c);return(0,aN.jsx)(a9.C,{present:d||b9(f.checked)||!0===f.checked,children:(0,aN.jsx)(i.sG.span,{...e,ref:b,"data-state":ca(f.checked)})})});bZ.displayName=bW;var b$=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});b$.displayName="MenuSeparator";var b_=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bp(c);return(0,aN.jsx)(a4,{...e,...d,ref:b})});b_.displayName="MenuArrow";var b0="MenuSub",[b1,b2]=bn(b0),b3=a=>{let{__scopeMenu:b,children:c,open:e=!1,onOpenChange:f}=a,g=bs(b0,b),h=bp(b),[i,j]=d.useState(null),[k,l]=d.useState(null),m=(0,aP.c)(f);return d.useEffect(()=>(!1===g.open&&m(!1),()=>m(!1)),[g.open,m]),(0,aN.jsx)(aX,{...h,children:(0,aN.jsx)(br,{scope:b,open:e,onOpenChange:m,content:k,onContentChange:l,children:(0,aN.jsx)(b1,{scope:b,contentId:(0,o.B)(),triggerId:(0,o.B)(),trigger:i,onTriggerChange:j,children:c})})})};b3.displayName=b0;var b4="MenuSubTrigger",b5=d.forwardRef((a,b)=>{let c=bs(b4,a.__scopeMenu),g=bu(b4,a.__scopeMenu),h=b2(b4,a.__scopeMenu),i=bD(b4,a.__scopeMenu),j=d.useRef(null),{pointerGraceTimerRef:k,onPointerGraceIntentChange:l}=i,m={__scopeMenu:a.__scopeMenu},n=d.useCallback(()=>{j.current&&window.clearTimeout(j.current),j.current=null},[]);return d.useEffect(()=>n,[n]),d.useEffect(()=>{let a=k.current;return()=>{window.clearTimeout(a),l(null)}},[k,l]),(0,aN.jsx)(bw,{asChild:!0,...m,children:(0,aN.jsx)(bO,{id:h.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":h.contentId,"data-state":b8(c.open),...a,ref:(0,f.t)(b,h.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,e.m)(a.onPointerMove,cb(b=>{i.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||j.current||(i.onPointerGraceIntentChange(null),j.current=window.setTimeout(()=>{c.onOpenChange(!0),n()},100)))})),onPointerLeave:(0,e.m)(a.onPointerLeave,cb(a=>{n();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];i.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(k.current),k.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(a),a.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,e.m)(a.onKeyDown,b=>{let d=""!==i.searchRef.current;a.disabled||d&&" "===b.key||bh[g.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});b5.displayName=b4;var b6="MenuSubContent",b7=d.forwardRef((a,b)=>{let c=bz(bB,a.__scopeMenu),{forceMount:g=c.forceMount,...h}=a,i=bs(bB,a.__scopeMenu),j=bu(bB,a.__scopeMenu),k=b2(b6,a.__scopeMenu),l=d.useRef(null),m=(0,f.s)(b,l);return(0,aN.jsx)(bk.Provider,{scope:a.__scopeMenu,children:(0,aN.jsx)(a9.C,{present:g||i.open,children:(0,aN.jsx)(bk.Slot,{scope:a.__scopeMenu,children:(0,aN.jsx)(bI,{id:k.contentId,"aria-labelledby":k.triggerId,...h,ref:m,align:"start",side:"rtl"===j.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{j.isUsingKeyboardRef.current&&l.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>{a.target!==k.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,e.m)(a.onEscapeKeyDown,a=>{j.onClose(),a.preventDefault()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=bi[j.dir].includes(a.key);b&&c&&(i.onOpenChange(!1),k.trigger?.focus(),a.preventDefault())})})})})})});function b8(a){return a?"open":"closed"}function b9(a){return"indeterminate"===a}function ca(a){return b9(a)?"indeterminate":a?"checked":"unchecked"}function cb(a){return b=>"mouse"===b.pointerType?a(b):void 0}b7.displayName=b6;var cc="DropdownMenu",[cd,ce]=(0,g.A)(cc,[bo]),cf=bo(),[cg,ch]=cd(cc),ci=a=>{let{__scopeDropdownMenu:b,children:c,dir:e,open:f,defaultOpen:g,onOpenChange:i,modal:j=!0}=a,k=cf(b),l=d.useRef(null),[m,n]=(0,h.i)({prop:f,defaultProp:g??!1,onChange:i,caller:cc});return(0,aN.jsx)(cg,{scope:b,triggerId:(0,o.B)(),triggerRef:l,contentId:(0,o.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,aN.jsx)(bv,{...k,open:m,onOpenChange:n,dir:e,modal:j,children:c})})};ci.displayName=cc;var cj="DropdownMenuTrigger",ck=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...g}=a,h=ch(cj,c),j=cf(c);return(0,aN.jsx)(bw,{asChild:!0,...j,children:(0,aN.jsx)(i.sG.button,{type:"button",id:h.triggerId,"aria-haspopup":"menu","aria-expanded":h.open,"aria-controls":h.open?h.contentId:void 0,"data-state":h.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...g,ref:(0,f.t)(b,h.triggerRef),onPointerDown:(0,e.m)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(h.onOpenToggle(),h.open||a.preventDefault())}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&h.onOpenToggle(),"ArrowDown"===a.key&&h.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});ck.displayName=cj;var cl=a=>{let{__scopeDropdownMenu:b,...c}=a,d=cf(b);return(0,aN.jsx)(bA,{...d,...c})};cl.displayName="DropdownMenuPortal";var cm="DropdownMenuContent",cn=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...f}=a,g=ch(cm,c),h=cf(c),i=d.useRef(!1);return(0,aN.jsx)(bE,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...f,ref:b,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:(0,e.m)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});cn.displayName=cm;var co=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bJ,{...e,...d,ref:b})});co.displayName="DropdownMenuGroup";var cp=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bK,{...e,...d,ref:b})});cp.displayName="DropdownMenuLabel";var cq=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bN,{...e,...d,ref:b})});cq.displayName="DropdownMenuItem";var cr=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bP,{...e,...d,ref:b})});cr.displayName="DropdownMenuCheckboxItem";var cs=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bT,{...e,...d,ref:b})});cs.displayName="DropdownMenuRadioGroup";var ct=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bV,{...e,...d,ref:b})});ct.displayName="DropdownMenuRadioItem";var cu=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(bZ,{...e,...d,ref:b})});cu.displayName="DropdownMenuItemIndicator";var cv=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(b$,{...e,...d,ref:b})});cv.displayName="DropdownMenuSeparator",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(b_,{...e,...d,ref:b})}).displayName="DropdownMenuArrow";var cw=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(b5,{...e,...d,ref:b})});cw.displayName="DropdownMenuSubTrigger";var cx=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cf(c);return(0,aN.jsx)(b7,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});cx.displayName="DropdownMenuSubContent";var cy=ci,cz=ck,cA=cl,cB=cn,cC=co,cD=cp,cE=cq,cF=cr,cG=cs,cH=ct,cI=cu,cJ=cv,cK=a=>{let{__scopeDropdownMenu:b,children:c,open:d,onOpenChange:e,defaultOpen:f}=a,g=cf(b),[i,j]=(0,h.i)({prop:d,defaultProp:f??!1,onChange:e,caller:"DropdownMenuSub"});return(0,aN.jsx)(b3,{...g,open:i,onOpenChange:j,children:c})},cL=cw,cM=cx},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18468:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=c(33123),e=c(74007);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},19169:(a,b)=>{function c(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"parsePath",{enumerable:!0,get:function(){return c}})},21134:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},22308:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=c(56928),e=c(59008),f=c(83913);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},24642:(a,b)=>{function c(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function d(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{extractInfoFromServerReferenceId:function(){return c},omitUnusedArgs:function(){return d}})},25232:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=c(59008),e=c(57391),f=c(18468),g=c(86770),h=c(65951),i=c(2030),j=c(59154),k=c(59435),l=c(56928),m=c(75076),n=c(89752),o=c(83913),p=c(65956),q=c(5334),r=c(97464),s=c(9707);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}c(50593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},25942:(a,b,c)=>{function d(a){return a}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeBasePath",{enumerable:!0,get:function(){return d}}),c(26736),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},26736:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=c(2255);function e(a){return(0,d.pathHasPrefix)(a,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},28627:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=c(57391),e=c(70642);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}c(65956),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},29651:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=c(57391),e=c(86770),f=c(2030),g=c(25232),h=c(56928),i=c(59435),j=c(89752);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},30195:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=c(40740)._(c(76715)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},32547:(a,b,c)=>{c.d(b,{n:()=>l});var d=c(43210),e=c(98599),f=c(14163),g=c(13495),h=c(60687),i="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},l=d.forwardRef((a,b)=>{let{loop:c=!1,trapped:l=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=d.useState(null),v=(0,g.c)(q),w=(0,g.c)(r),x=d.useRef(null),y=(0,e.s)(b,a=>u(a)),z=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(l){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:o(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||o(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&o(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[l,t,z.paused]),d.useEffect(()=>{if(t){p.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(i,k);t.addEventListener(i,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(o(d,{select:b}),document.activeElement!==c)return}(m(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&o(t))}return()=>{t.removeEventListener(i,v),setTimeout(()=>{let b=new CustomEvent(j,k);t.addEventListener(j,w),t.dispatchEvent(b),b.defaultPrevented||o(a??document.body,{select:!0}),t.removeEventListener(j,w),p.remove(z)},0)}}},[t,v,w,z]);let A=d.useCallback(a=>{if(!c&&!l||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,d=document.activeElement;if(b&&d){let b=a.currentTarget,[e,f]=function(a){let b=m(a);return[n(b,a),n(b.reverse(),a)]}(b);e&&f?a.shiftKey||d!==f?a.shiftKey&&d===e&&(a.preventDefault(),c&&o(f,{select:!0})):(a.preventDefault(),c&&o(e,{select:!0})):d===b&&a.preventDefault()}},[c,l,z.paused]);return(0,h.jsx)(f.sG.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function m(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function n(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function o(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}l.displayName="FocusScope";var p=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=q(a,b)).unshift(b)},remove(b){a=q(a,b),a[0]?.resume()}}}();function q(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},32708:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"errorOnce",{enumerable:!0,get:function(){return c}});let c=a=>{}},33898:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=c(34400),e=c(41500),f=c(33123),g=c(83913);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},34400:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=c(33123);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},34410:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]])},35416:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=c(95796),e=/google/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},35429:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverActionReducer",{enumerable:!0,get:function(){return D}});let d=c(11264),e=c(11448),f=c(91563),g=c(7379),h=c(59154),i=c(6361),j=c(57391),k=c(25232),l=c(86770),m=c(2030),n=c(59435),o=c(41500),p=c(89752),q=c(68214),r=c(96493),s=c(22308),t=c(74007),u=c(36875),v=c(97860),w=c(5334),x=c(25942),y=c(26736),z=c(24642);c(50593);let A=g.createFromFetch;async function B(a,b,c){let h,j,k,l,{actionId:m,actionArgs:n}=c,o=(0,g.createTemporaryReferenceSet)(),p=(0,z.extractInfoFromServerReferenceId)(m),q="use-cache"===p.type?(0,z.omitUnusedArgs)(n,p):n,r=await (0,g.encodeReply)(q,{temporaryReferences:o}),s=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:m,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,t.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:r});if("1"===s.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(Error('Server Action "'+m+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let u=s.headers.get("x-action-redirect"),[w,x]=(null==u?void 0:u.split(";"))||[];switch(x){case"push":h=v.RedirectType.push;break;case"replace":h=v.RedirectType.replace;break;default:h=void 0}let y=!!s.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(s.headers.get("x-action-revalidated")||"[[],0,0]");j={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){j=C}let B=w?(0,i.assignLocation)(w,new URL(a.canonicalUrl,window.location.href)):void 0,D=s.headers.get("content-type"),E=!!(D&&D.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!E&&!B)throw Object.defineProperty(Error(s.status>=400&&"text/plain"===D?await s.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(E){let a=await A(Promise.resolve(s),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:o});k=B?void 0:a.a,l=(0,t.normalizeFlightData)(a.f)}else k=void 0,l=void 0;return{actionResult:k,actionFlightData:l,redirectLocation:B,redirectType:h,revalidatedParts:j,isPrerender:y}}let C={paths:[],tag:!1,cookie:!1};function D(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,q.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,i=Date.now();return B(a,g,b).then(async q=>{let t,{actionResult:z,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=q;if(B&&(C===v.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=t=(0,j.createHrefFromUrl)(B,!1)),!A)return(c(z),B)?(0,k.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(z),(0,k.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:h,seedData:j,head:n,isRootRender:q}=d;if(!q)return console.log("SERVER ACTION APPLY FAILED"),c(z),a;let u=(0,l.applyRouterStatePatchToTree)([""],f,h,t||a.canonicalUrl);if(null===u)return c(z),(0,r.handleSegmentMismatch)(a,b,h);if((0,m.isNavigatingToNewRootLayout)(f,u))return c(z),(0,k.handleExternalUrl)(a,e,t||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,p.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,o.fillLazyItemsTillLeafWithHead)(i,c,void 0,h,j,n,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,s.refreshInactiveParallelSegments)({navigatedAt:i,state:a,updatedTree:u,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=u,f=u}return B&&t?(F||((0,w.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?h.PrefetchKind.FULL:h.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,u.getRedirectError)((0,y.hasBasePath)(t)?(0,x.removeBasePath)(t):t,C||v.RedirectType.push))):c(z),(0,n.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},41500:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=c(33123),e=c(59154);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},42247:(a,b,c)=>{c.d(b,{A:()=>U});var d,e,f=function(){return(f=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function g(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var h=("function"==typeof SuppressedError&&SuppressedError,c(43210)),i="right-scroll-bar-position",j="width-before-scroll-bar";function k(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var l="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,m=new WeakMap;function n(a){return a}var o=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=n),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=f({async:!0,ssr:!1},a),e}(),p=function(){},q=h.forwardRef(function(a,b){var c,d,e,i,j=h.useRef(null),n=h.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),q=n[0],r=n[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=g(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[j,b],d=function(a){return c.forEach(function(b){return k(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,i=e.facade,l(function(){var a=m.get(i);if(a){var b=new Set(a),d=new Set(c),e=i.current;b.forEach(function(a){d.has(a)||k(a,null)}),d.forEach(function(a){b.has(a)||k(a,e)})}m.set(i,c)},[c]),i),H=f(f({},F),q);return h.createElement(h.Fragment,null,w&&h.createElement(y,{sideCar:o,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:j,gapMode:E}),s?h.cloneElement(h.Children.only(t),f(f({},H),{ref:G})):h.createElement(void 0===D?"div":D,f({},H,{className:u,ref:G}),t))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:j,zeroRight:i};var r=function(a){var b=a.sideCar,c=g(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,f({},c))};r.isSideCarExport=!0;var s=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=e||c.nc;return b&&a.setAttribute("nonce",b),a}())){var f,g;(f=b).styleSheet?f.styleSheet.cssText=d:f.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},t=function(){var a=s();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},u=function(){var a=t();return function(b){return a(b.styles,b.dynamic),null}},v={left:0,top:0,right:0,gap:0},w=function(a){return parseInt(a||"",10)||0},x=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[w(c),w(d),w(e)]},y=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return v;var b=x(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},z=u(),A="data-scroll-locked",B=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},C=function(){var a=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(a)?a:0},D=function(){h.useEffect(function(){return document.body.setAttribute(A,(C()+1).toString()),function(){var a=C()-1;a<=0?document.body.removeAttribute(A):document.body.setAttribute(A,a.toString())}},[])},E=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;D();var f=h.useMemo(function(){return y(e)},[e]);return h.createElement(z,{styles:B(f,!b,e,c?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var G=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch(a){F=!1}var H=!!F&&{passive:!1},I=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},J=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),K(a,d)){var e=L(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},K=function(a,b){return"v"===a?I(b,"overflowY"):I(b,"overflowX")},L=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},M=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=L(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&K(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},N=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},O=function(a){return[a.deltaX,a.deltaY]},P=function(a){return a&&"current"in a?a.current:a},Q=0,R=[];let S=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(Q++)[0],f=h.useState(u)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(P),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=N(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=J(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=J(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return M(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(R.length&&R[R.length-1]===f){var c="deltaY"in a?O(a):N(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(P).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=N(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,O(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,N(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return R.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,H),document.addEventListener("touchmove",j,H),document.addEventListener("touchstart",l,H),function(){R=R.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,H),document.removeEventListener("touchmove",j,H),document.removeEventListener("touchstart",l,H)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(E,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},o.useMedium(d),r);var T=h.forwardRef(function(a,b){return h.createElement(q,f({},a,{ref:b,sideCar:S}))});T.classNames=q.classNames;let U=T},44397:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=c(83913),e=c(33123);function f(a,b){return function a(b,c,f){if(0===Object.keys(c).length)return[b,f];let g=Object.keys(c).filter(a=>"children"!==a);for(let h of("children"in c&&g.unshift("children"),g)){let[g,i]=c[h];if(g===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(h);if(!j)continue;let k=(0,e.createRouterCacheKey)(g),l=j.get(k);if(!l)continue;let m=a(l,i,f+"/"+k);if(m)return m}return null}(a,b,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},50593:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{NavigationResultTag:function(){return m},PrefetchPriority:function(){return n},cancelPrefetchTask:function(){return i},createCacheKey:function(){return l},getCurrentCacheVersion:function(){return g},isPrefetchTaskDirty:function(){return k},navigate:function(){return e},prefetch:function(){return d},reschedulePrefetchTask:function(){return j},revalidateEntireCache:function(){return f},schedulePrefetchTask:function(){return h}});let c=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},d=c,e=c,f=c,g=c,h=c,i=c,j=c,k=c,l=c;var m=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),n=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},51550:(a,b,c)=>{function d(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}c.r(b),c.d(b,{_:()=>d})},53038:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(43210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},54674:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=c(84949),e=c(19169),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return""+(0,d.removeTrailingSlash)(b)+c+f};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},56928:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=c(41500),e=c(33898);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},59435:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleMutable",{enumerable:!0,get:function(){return f}});let d=c(70642);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},59656:(a,b,c)=>{c.r(b),c.d(b,{_:()=>e});var d=0;function e(a){return"__private_"+d+++"_"+a}},61794:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=c(79289),e=c(26736);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},63376:(a,b,c)=>{c.d(b,{Eq:()=>j});var d=new WeakMap,e=new WeakMap,f={},g=0,h=function(a){return a&&(a.host||h(a.parentNode))},i=function(a,b,c,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=h(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});f[c]||(f[c]=new WeakMap);var k=f[c],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var b=a.getAttribute(i),f=null!==b&&"false"!==b,g=(d.get(a)||0)+1,h=(k.get(a)||0)+1;d.set(a,g),k.set(a,h),l.push(a),1===g&&f&&e.set(a,!0),1===h&&a.setAttribute(c,"true"),f||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(b),m.clear(),g++,function(){l.forEach(function(a){var b=d.get(a)-1,f=k.get(a)-1;d.set(a,b),k.set(a,f),b||(e.has(a)||a.removeAttribute(i),e.delete(a)),f||a.removeAttribute(c)}),--g||(d=new WeakMap,d=new WeakMap,e=new WeakMap,f={})}},j=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),i(d,e,c,"aria-hidden")):function(){return null}}},63690:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=c(59154),e=c(8830),f=c(43210),g=c(91992);c(50593);let h=c(19129),i=c(96127),j=c(89752),k=c(75076),l=c(73406);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},65822:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},65951:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=c(74007),e=c(14077);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},65956:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=c(83913),e=c(14077),f=c(33123),g=c(2030),h=c(5334),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},69018:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=c(60687),e=c(43210);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},70642:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=c(72859),e=c(83913),f=c(14077),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},72942:(a,b,c)=>{c.d(b,{RG:()=>v,bL:()=>E,q7:()=>F});var d=c(43210),e=c(70569),f=c(9510),g=c(98599),h=c(11273),i=c(96963),j=c(14163),k=c(13495),l=c(65551),m=c(43),n=c(60687),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,f.N)(q),[u,v]=(0,h.A)(q,[t]),[w,x]=u(q),y=d.forwardRef((a,b)=>(0,n.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=d.useRef(null),A=(0,g.s)(b,z),B=(0,m.jH)(i),[C,E]=(0,l.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=d.useState(!1),H=(0,k.c)(v),I=s(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,n.jsx)(w,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>E(a),[E]),onItemShiftTab:d.useCallback(()=>G(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,n.jsx)(j.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":f,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:k,...l}=a,m=(0,i.B)(),o=h||m,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return d.useEffect(()=>{if(f)return u(),()=>v()},[f,u,v]),(0,n.jsx)(r.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,n.jsx)(j.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof k?k({isCurrentTabStop:q,hasTabStop:null!=w}):k})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=y,F=B},73406:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{IDLE_LINK_STATUS:function(){return j},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return s},mountLinkInstance:function(){return r},onLinkVisibilityChanged:function(){return u},onNavigationIntent:function(){return v},pingVisibleLinks:function(){return x},setLinkForCurrentNavigation:function(){return k},unmountLinkForCurrentNavigation:function(){return l},unmountPrefetchableInstance:function(){return t}}),c(63690);let d=c(89752),e=c(59154),f=c(50593),g=c(43210),h=null,i={pending:!0},j={pending:!1};function k(a){(0,g.startTransition)(()=>{null==h||h.setOptimisticLinkStatus(j),null==a||a.setOptimisticLinkStatus(i),h=a})}function l(a){h===a&&(h=null)}let m="function"==typeof WeakMap?new WeakMap:new Map,n=new Set,o="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;u(b.target,a)}},{rootMargin:"200px"}):null;function p(a,b){void 0!==m.get(a)&&t(a),m.set(a,b),null!==o&&o.observe(a)}function q(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function r(a,b,c,d,e,f){if(e){let e=q(b);if(null!==e){let b={router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return p(a,b),b}}return{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function s(a,b,c,d){let e=q(b);null!==e&&p(a,{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function t(a){let b=m.get(a);if(void 0!==b){m.delete(a),n.delete(b);let c=b.prefetchTask;null!==c&&(0,f.cancelPrefetchTask)(c)}null!==o&&o.unobserve(a)}function u(a,b){let c=m.get(a);void 0!==c&&(c.isVisible=b,b?n.add(c):n.delete(c),w(c,f.PrefetchPriority.Default))}function v(a,b){let c=m.get(a);void 0!==c&&void 0!==c&&w(c,f.PrefetchPriority.Intent)}function w(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,f.cancelPrefetchTask)(c);return}}function x(a,b){for(let c of n){let d=c.prefetchTask;if(null!==d&&!(0,f.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,f.cancelPrefetchTask)(d);let g=(0,f.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,f.schedulePrefetchTask)(g,b,c.kind===e.PrefetchKind.FULL,f.PrefetchPriority.Default,null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},75076:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=c(5144),e=c(5334),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},76715:(a,b)=>{function c(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function d(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function e(a){let b=new URLSearchParams;for(let[c,e]of Object.entries(a))if(Array.isArray(e))for(let a of e)b.append(c,d(a));else b.set(c,d(e));return b}function f(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{assign:function(){return f},searchParamsToUrlQuery:function(){return c},urlQueryToSearchParams:function(){return e}})},77022:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=c(43210),e=c(51215),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},78866:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=c(59008),e=c(57391),f=c(86770),g=c(2030),h=c(25232),i=c(59435),j=c(41500),k=c(89752),l=c(96493),m=c(68214),n=c(22308);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}c(50593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},79289:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DecodeError:function(){return o},MiddlewareNotFoundError:function(){return s},MissingStaticPage:function(){return r},NormalizeError:function(){return p},PageNotFoundError:function(){return q},SP:function(){return m},ST:function(){return n},WEB_VITALS:function(){return c},execOnce:function(){return d},getDisplayName:function(){return i},getLocationOrigin:function(){return g},getURL:function(){return h},isAbsoluteUrl:function(){return f},isResSent:function(){return j},loadGetInitialProps:function(){return l},normalizeRepeatedSlashes:function(){return k},stringifyError:function(){return t}});let c=["CLS","FCP","FID","INP","LCP","TTFB"];function d(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let e=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,f=a=>e.test(a);function g(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function h(){let{href:a}=window.location,b=g();return a.substring(b.length)}function i(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function j(a){return a.finished||a.headersSent}function k(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function l(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await l(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&j(c))return d;if(!d)throw Object.defineProperty(Error('"'+i(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let m="undefined"!=typeof performance,n=m&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class o extends Error{}class p extends Error{}class q extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class r extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class s extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function t(a){return JSON.stringify({message:a.message,stack:a.stack})}},84949:(a,b)=>{function c(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeTrailingSlash",{enumerable:!0,get:function(){return c}})},85814:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return q},useLinkStatus:function(){return s}});let d=c(40740),e=c(60687),f=d._(c(43210)),g=c(30195),h=c(22142),i=c(59154),j=c(53038),k=c(79289),l=c(96127);c(50148);let m=c(73406),n=c(61794),o=c(63690);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){let b,c,d,[g,q]=(0,f.useOptimistic)(m.IDLE_LINK_STATUS),s=(0,f.useRef)(null),{href:t,as:u,children:v,prefetch:w=null,passHref:x,replace:y,shallow:z,scroll:A,onClick:B,onMouseEnter:C,onTouchStart:D,legacyBehavior:E=!1,onNavigate:F,ref:G,unstable_dynamicOnHover:H,...I}=a;b=v,E&&("string"==typeof b||"number"==typeof b)&&(b=(0,e.jsx)("a",{children:b}));let J=f.default.useContext(h.AppRouterContext),K=!1!==w,L=null===w||"auto"===w?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:M,as:N}=f.default.useMemo(()=>{let a=p(t);return{href:a,as:u?p(u):a}},[t,u]);E&&(c=f.default.Children.only(b));let O=E?c&&"object"==typeof c&&c.ref:G,P=f.default.useCallback(a=>(null!==J&&(s.current=(0,m.mountLinkInstance)(a,M,J,L,K,q)),()=>{s.current&&((0,m.unmountLinkForCurrentNavigation)(s.current),s.current=null),(0,m.unmountPrefetchableInstance)(a)}),[K,M,J,L,q]),Q={ref:(0,j.useMergedRef)(P,O),onClick(a){E||"function"!=typeof B||B(a),E&&c.props&&"function"==typeof c.props.onClick&&c.props.onClick(a),J&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,n.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,o.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,M,N,s,y,A,F))},onMouseEnter(a){E||"function"!=typeof C||C(a),E&&c.props&&"function"==typeof c.props.onMouseEnter&&c.props.onMouseEnter(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)},onTouchStart:function(a){E||"function"!=typeof D||D(a),E&&c.props&&"function"==typeof c.props.onTouchStart&&c.props.onTouchStart(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)}};return(0,k.isAbsoluteUrl)(N)?Q.href=N:E&&!x&&("a"!==c.type||"href"in c.props)||(Q.href=(0,l.addBasePath)(N)),d=E?f.default.cloneElement(c,Q):(0,e.jsx)("a",{...I,...Q,children:b}),(0,e.jsx)(r.Provider,{value:g,children:d})}c(32708);let r=(0,f.createContext)(m.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},86770:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=c(83913),e=c(74007),f=c(14077),g=c(22308);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},89752:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=c(14985),e=c(40740),f=c(60687),g=e._(c(43210)),h=c(22142),i=c(59154),j=c(57391),k=c(10449),l=c(19129),m=c(35656),n=d._(c(25227)),o=c(35416),p=c(96127),q=c(77022),r=c(67086),s=c(44397),t=c(89330),u=c(25942),v=c(26736),w=c(70642),x=c(12776),y=c(63690),z=c(36875),A=c(97860);c(73406);let B=d._(c(69018)),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,o.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,p.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e,gracefullyDegrade:j}=a,n=(0,l.useActionQueue)(c),{canonicalUrl:o}=n,{searchParams:p,pathname:x}=(0,g.useMemo)(()=>{let a=new URL(o,"http://n");return{searchParams:a.searchParams,pathname:(0,v.hasBasePath)(a.pathname)?(0,u.removeBasePath)(a.pathname):a.pathname}},[o]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,A.isRedirectError)(b)){a.preventDefault();let c=(0,z.getURLFromRedirectError)(b);(0,z.getRedirectTypeFromError)(b)===A.RedirectType.push?y.publicAppRouterInstance.push(c,{}):y.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:D}=n;if(D.mpaNavigation){if(C.pendingMpaPath!==o){let a=window.location;D.pendingPush?a.assign(o):a.replace(o),C.pendingMpaPath=o}throw t.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,y.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:E,tree:G,nextUrl:J,focusAndScrollRef:K}=n,L=(0,g.useMemo)(()=>(0,s.findHeadInCache)(E,G[1]),[E,G]),M=(0,g.useMemo)(()=>(0,w.getSelectedParams)(G),[G]),O=(0,g.useMemo)(()=>({parentTree:G,parentCacheNode:E,parentSegmentPath:null,url:o}),[G,E,o]),P=(0,g.useMemo)(()=>({tree:G,focusAndScrollRef:K,nextUrl:J}),[G,K,J]);if(null!==L){let[a,c]=L;b=(0,f.jsx)(I,{headCacheNode:a},c)}else b=null;let Q=(0,f.jsxs)(r.RedirectBoundary,{children:[b,E.rsc,(0,f.jsx)(q.AppRouterAnnouncer,{tree:G})]});return Q=j?(0,f.jsx)(B.default,{children:Q}):(0,f.jsx)(m.ErrorBoundary,{errorComponent:e[0],errorStyles:e[1],children:Q}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:n}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:M,children:(0,f.jsx)(k.PathnameContext.Provider,{value:x,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:p,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:P,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:y.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:O,children:Q})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d,gracefullyDegrade:e}=a;(0,x.useNavFailureHandler)();let g=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c,gracefullyDegrade:e});return e?g:(0,f.jsx)(m.ErrorBoundary,{errorComponent:n.default,children:g})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},95796:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return c}});let c=/Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addBasePath",{enumerable:!0,get:function(){return f}});let d=c(98834),e=c(54674);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,""))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},96493:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=c(25232);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}},97464:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=c(74007),e=c(33123);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},97936:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),c(59008),c(57391),c(86770),c(2030),c(25232),c(59435),c(56928),c(89752),c(96493),c(68214);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},98834:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=c(19169);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}}};