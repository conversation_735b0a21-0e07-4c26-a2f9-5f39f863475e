import { query } from '@/lib/database/client'
import { withClientCache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'
import type {
  SearchHistory,
  SearchHistoryInsert,
  SearchHistoryUpdate,
  DatabaseError
} from '@/types/database'

/**
 * 获取搜索历史（简化版本，不依赖用户认证）
 * 注意：当前版本不支持搜索历史功能，返回空数组
 */
export async function getSearchHistory(limit: number = 10): Promise<SearchHistory[]> {
  try {
    // 当前版本不支持搜索历史功能，因为没有用户认证
    // 可以考虑使用本地存储或其他方式实现
    console.log('搜索历史功能暂未实现（无用户认证）')
    return []
  } catch (error) {
    console.error('获取搜索历史失败:', error)
    return []
  }
}

/**
 * 添加搜索历史（简化版本，不依赖用户认证）
 * 注意：当前版本不支持搜索历史功能，仅记录日志
 */
export async function addSearchHistory(searchTerm: string): Promise<void> {
  try {
    // 当前版本不支持搜索历史功能，因为没有用户认证
    // 可以考虑使用本地存储或其他方式实现
    console.log('搜索词:', searchTerm, '（搜索历史功能暂未实现）')
    // 搜索历史失败不应该影响搜索功能，所以只记录日志
  } catch (error) {
    console.error('添加搜索历史失败:', error)
    // 搜索历史失败不应该影响搜索功能，所以只记录日志
  }
}

/**
 * 清除搜索历史（简化版本）
 */
export async function clearSearchHistory(): Promise<void> {
  try {
    console.log('清除搜索历史（搜索历史功能暂未实现）')
    // 当前版本不支持搜索历史功能
  } catch (error) {
    console.error('清除搜索历史失败:', error)
  }
}

/**
 * 删除单个搜索历史记录（简化版本）
 */
export async function deleteSearchHistoryItem(id: string): Promise<void> {
  try {
    console.log('删除搜索历史记录:', id, '（搜索历史功能暂未实现）')
    // 当前版本不支持搜索历史功能
  } catch (error) {
    console.error('删除搜索历史记录失败:', error)
  }
}

/**
 * 获取热门搜索词（简化版本）
 */
export async function getPopularSearchTerms(limit: number = 10): Promise<SearchHistory[]> {
  try {
    console.log('获取热门搜索词（搜索历史功能暂未实现）')
    // 当前版本不支持搜索历史功能
    return []
  } catch (error) {
    console.error('获取热门搜索词失败:', error)
    return []
  }
}

/**
 * 搜索建议（简化版本）
 */
export async function getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
  try {
    console.log('获取搜索建议:', query, '（搜索历史功能暂未实现）')
    // 当前版本不支持搜索历史功能
    return []
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    return []
  }
}

/**
 * 全文搜索提示词（使用PostgreSQL直连）
 */
export async function searchPrompts(
  searchQuery: string,
  options: {
    categoryId?: string
    tagIds?: string[]
    limit?: number
    offset?: number
  } = {}
) {
  try {
    const { categoryId, tagIds = [], limit = 20, offset = 0 } = options

    // 记录搜索历史（当前版本仅记录日志）
    if (searchQuery.trim()) {
      await addSearchHistory(searchQuery.trim())
    }

    // 构建基础查询
    let sql = `
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          json_agg(
            json_build_object(
              'id', t.id,
              'name', t.name,
              'color', t.color
            )
          ) FILTER (WHERE t.id IS NOT NULL),
          '[]'::json
        ) as tags
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `

    const params: any[] = []
    let paramIndex = 1

    // 全文搜索
    if (searchQuery.trim()) {
      sql += ` AND (
        p.title ILIKE $${paramIndex} OR
        p.description ILIKE $${paramIndex} OR
        p.content ILIKE $${paramIndex}
      )`
      params.push(`%${searchQuery.trim()}%`)
      paramIndex++
    }

    // 分类筛选
    if (categoryId) {
      sql += ` AND p.category_id = $${paramIndex}`
      params.push(categoryId)
      paramIndex++
    }

    // 标签筛选
    if (tagIds.length > 0) {
      sql += ` AND p.id IN (
        SELECT DISTINCT prompt_id
        FROM prompt_tags
        WHERE tag_id = ANY($${paramIndex})
      )`
      params.push(tagIds)
      paramIndex++
    }

    // 分组、排序和分页
    sql += `
      GROUP BY p.id, c.name, c.color, c.icon
      ORDER BY p.updated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    params.push(limit, offset)

    const result = await query(sql, params)

    // 处理数据，确保tags是数组格式
    const processedData = result.rows.map(item => ({
      ...item,
      category: item.category_name ? {
        name: item.category_name,
        color: item.category_color,
        icon: item.category_icon
      } : null,
      tags: Array.isArray(item.tags) ? item.tags.filter(tag => tag.id) : []
    }))

    return processedData
  } catch (error) {
    console.error('搜索提示词失败:', error)
    throw new Error('搜索提示词失败')
  }
}

/**
 * 高级搜索（简化版本，使用PostgreSQL直连）
 */
export async function advancedSearch(params: {
  query?: string
  title?: string
  content?: string
  categoryId?: string
  tagIds?: string[]
  dateFrom?: string
  dateTo?: string
  usageCountMin?: number
  usageCountMax?: number
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}) {
  try {
    return await executeAdvancedSearch(params)
  } catch (error) {
    console.error('高级搜索失败:', error)
    throw new Error('高级搜索失败')
  }
}

async function executeAdvancedSearch(params: {
  query?: string
  title?: string
  content?: string
  categoryId?: string
  tagIds?: string[]
  dateFrom?: string
  dateTo?: string
  usageCountMin?: number
  usageCountMax?: number
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}) {
  try {
    const {
      query: searchQuery,
      title,
      content,
      categoryId,
      tagIds = [],
      dateFrom,
      dateTo,
      usageCountMin,
      usageCountMax,
      sortBy = 'updated_at',
      sortOrder = 'desc',
      limit = 20,
      offset = 0
    } = params

    // 构建基础查询
    let sql = `
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          json_agg(
            json_build_object(
              'id', t.id,
              'name', t.name,
              'color', t.color
            )
          ) FILTER (WHERE t.id IS NOT NULL),
          '[]'::json
        ) as tags
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `

    const queryParams: any[] = []
    let paramIndex = 1

    // 通用搜索
    if (searchQuery) {
      sql += ` AND (
        p.title ILIKE $${paramIndex} OR
        p.description ILIKE $${paramIndex} OR
        p.content ILIKE $${paramIndex}
      )`
      queryParams.push(`%${searchQuery}%`)
      paramIndex++
    }

    // 标题搜索
    if (title) {
      sql += ` AND p.title ILIKE $${paramIndex}`
      queryParams.push(`%${title}%`)
      paramIndex++
    }

    // 内容搜索
    if (content) {
      sql += ` AND p.content ILIKE $${paramIndex}`
      queryParams.push(`%${content}%`)
      paramIndex++
    }

    // 分类筛选
    if (categoryId) {
      sql += ` AND p.category_id = $${paramIndex}`
      queryParams.push(categoryId)
      paramIndex++
    }

    // 标签筛选
    if (tagIds.length > 0) {
      sql += ` AND p.id IN (
        SELECT DISTINCT prompt_id
        FROM prompt_tags
        WHERE tag_id = ANY($${paramIndex})
      )`
      queryParams.push(tagIds)
      paramIndex++
    }

    // 日期范围筛选
    if (dateFrom) {
      sql += ` AND p.created_at >= $${paramIndex}`
      queryParams.push(dateFrom)
      paramIndex++
    }
    if (dateTo) {
      sql += ` AND p.created_at <= $${paramIndex}`
      queryParams.push(dateTo)
      paramIndex++
    }

    // 使用次数筛选
    if (usageCountMin !== undefined) {
      sql += ` AND p.usage_count >= $${paramIndex}`
      queryParams.push(usageCountMin)
      paramIndex++
    }
    if (usageCountMax !== undefined) {
      sql += ` AND p.usage_count <= $${paramIndex}`
      queryParams.push(usageCountMax)
      paramIndex++
    }

    // 分组、排序和分页
    sql += `
      GROUP BY p.id, c.name, c.color, c.icon
      ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    queryParams.push(limit, offset)

    const result = await query(sql, queryParams)

    // 处理数据，确保tags是数组格式
    const processedData = result.rows.map(item => ({
      ...item,
      category: item.category_name ? {
        name: item.category_name,
        color: item.category_color,
        icon: item.category_icon
      } : null,
      tags: Array.isArray(item.tags) ? item.tags.filter(tag => tag.id) : []
    }))

    return processedData
  } catch (error) {
    console.error('高级搜索执行失败:', error)
    throw new Error('高级搜索执行失败')
  }
}
