{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/fontawesome.ts"], "sourcesContent": ["// Font Awesome 配置\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport {\n  faFolder,\n  faFolderOpen,\n  faCode,\n  faPenToSquare,\n  faBullhorn,\n  faRocket,\n  faGraduationCap,\n  faSearch,\n  faPlus,\n  faCopy,\n  faEdit,\n  faTrash,\n  faEye,\n  faTags,\n  faHeart,\n  faShare,\n  faDownload,\n  faUpload,\n  faStar,\n  faBookmark,\n  faFilter,\n  faSortAmountDown,\n  faSortAmountUp,\n  faThLarge as faGrid,\n  faList,\n  faCog,\n  faUser,\n  faSignOutAlt,\n  faHome,\n  faChevronDown,\n  faChevronUp,\n  faChevronLeft,\n  faChevronRight,\n  faTimes,\n  faCheck,\n  faExclamationTriangle,\n  faInfoCircle,\n  faQuestionCircle,\n  faBars,\n  faEllipsisV,\n  faSpinner,\n  faRefresh,\n  faSave,\n  faUndo,\n  faRedo,\n  faExpand,\n  faCompress,\n  faExternalLinkAlt,\n  faClipboard,\n  faClipboardCheck,\n  faFileAlt as faMarkdown,\n  faFileCode,\n  faFileText,\n  faImage,\n  faVideo,\n  faMusic,\n  faFile,\n  faCalendar,\n  faClock,\n  faHashtag,\n  faAt,\n  faLink,\n  faGlobe,\n  faLock,\n  faUnlock,\n  faShield,\n  faDatabase,\n  faServer,\n  faCloud,\n  faDesktop,\n  faMobile,\n  faTablet,\n  faLaptop,\n  faPalette,\n  faPaintBrush,\n  faMagic,\n  faLightbulb,\n  faZap as faFlash,\n  faBolt,\n  faFire,\n  faGem,\n  faCrown,\n  faTrophy,\n  faMedal,\n  faAward,\n  faBullseye,\n  faFlag,\n  faMapMarkerAlt as faMapMarker,\n  faCompass,\n  faRoute,\n  faMap,\n  faChartBar,\n  faThLarge,\n  faFileAlt,\n  faZap,\n  faMapMarkerAlt\n} from '@fortawesome/free-solid-svg-icons'\n\n// 添加图标到库中\nlibrary.add(\n  faFolder,\n  faFolderOpen,\n  faCode,\n  faPenToSquare,\n  faBullhorn,\n  faRocket,\n  faGraduationCap,\n  faSearch,\n  faPlus,\n  faCopy,\n  faEdit,\n  faTrash,\n  faEye,\n  faTags,\n  faHeart,\n  faShare,\n  faDownload,\n  faUpload,\n  faStar,\n  faBookmark,\n  faFilter,\n  faSortAmountDown,\n  faSortAmountUp,\n  faGrid,\n  faList,\n  faCog,\n  faUser,\n  faSignOutAlt,\n  faHome,\n  faChevronDown,\n  faChevronUp,\n  faChevronLeft,\n  faChevronRight,\n  faTimes,\n  faCheck,\n  faExclamationTriangle,\n  faInfoCircle,\n  faQuestionCircle,\n  faBars,\n  faEllipsisV,\n  faSpinner,\n  faRefresh,\n  faSave,\n  faUndo,\n  faRedo,\n  faExpand,\n  faCompress,\n  faExternalLinkAlt,\n  faClipboard,\n  faClipboardCheck,\n  faMarkdown,\n  faFileCode,\n  faFileText,\n  faImage,\n  faVideo,\n  faMusic,\n  faFile,\n  faCalendar,\n  faClock,\n  faHashtag,\n  faAt,\n  faLink,\n  faGlobe,\n  faLock,\n  faUnlock,\n  faShield,\n  faDatabase,\n  faServer,\n  faCloud,\n  faDesktop,\n  faMobile,\n  faTablet,\n  faLaptop,\n  faPalette,\n  faPaintBrush,\n  faMagic,\n  faLightbulb,\n  faFlash,\n  faBolt,\n  faFire,\n  faGem,\n  faCrown,\n  faTrophy,\n  faMedal,\n  faAward,\n  faBullseye,\n  faFlag,\n  faMapMarker,\n  faCompass,\n  faRoute,\n  faMap,\n  faChartBar\n)\n\n// 导出常用图标名称映射\nexport const iconMap = {\n  // 分类图标\n  folder: 'folder',\n  'folder-open': 'folder-open',\n  code: 'code',\n  'pen-to-square': 'pen-to-square',\n  bullhorn: 'bullhorn',\n  rocket: 'rocket',\n  'graduation-cap': 'graduation-cap',\n  \n  // 操作图标\n  search: 'search',\n  plus: 'plus',\n  copy: 'copy',\n  edit: 'edit',\n  trash: 'trash',\n  eye: 'eye',\n  tags: 'tags',\n  heart: 'heart',\n  share: 'share',\n  download: 'download',\n  upload: 'upload',\n  star: 'star',\n  bookmark: 'bookmark',\n  \n  // 界面图标\n  filter: 'filter',\n  'sort-amount-down': 'sort-amount-down',\n  'sort-amount-up': 'sort-amount-up',\n  grid: 'grid',\n  list: 'list',\n  cog: 'cog',\n  user: 'user',\n  'sign-out-alt': 'sign-out-alt',\n  home: 'home',\n  \n  // 导航图标\n  'chevron-down': 'chevron-down',\n  'chevron-up': 'chevron-up',\n  'chevron-left': 'chevron-left',\n  'chevron-right': 'chevron-right',\n  times: 'times',\n  check: 'check',\n  \n  // 状态图标\n  'exclamation-triangle': 'exclamation-triangle',\n  'info-circle': 'info-circle',\n  'question-circle': 'question-circle',\n  bars: 'bars',\n  'ellipsis-v': 'ellipsis-v',\n  spinner: 'spinner',\n  refresh: 'refresh',\n  \n  // 编辑图标\n  save: 'save',\n  undo: 'undo',\n  redo: 'redo',\n  expand: 'expand',\n  compress: 'compress',\n  'external-link-alt': 'external-link-alt',\n  clipboard: 'clipboard',\n  'clipboard-check': 'clipboard-check',\n  \n  // 文件类型图标\n  markdown: 'markdown',\n  'file-code': 'file-code',\n  'file-text': 'file-text',\n  image: 'image',\n  video: 'video',\n  music: 'music',\n  file: 'file',\n  \n  // 时间图标\n  calendar: 'calendar',\n  clock: 'clock',\n  \n  // 社交图标\n  hashtag: 'hashtag',\n  at: 'at',\n  link: 'link',\n  globe: 'globe',\n  \n  // 安全图标\n  lock: 'lock',\n  unlock: 'unlock',\n  shield: 'shield',\n  \n  // 技术图标\n  database: 'database',\n  server: 'server',\n  cloud: 'cloud',\n  desktop: 'desktop',\n  mobile: 'mobile',\n  tablet: 'tablet',\n  laptop: 'laptop',\n  \n  // 设计图标\n  palette: 'palette',\n  'paint-brush': 'paint-brush',\n  magic: 'magic',\n  \n  // 创意图标\n  lightbulb: 'lightbulb',\n  flash: 'flash',\n  bolt: 'bolt',\n  fire: 'fire',\n  gem: 'gem',\n  \n  // 成就图标\n  crown: 'crown',\n  trophy: 'trophy',\n  medal: 'medal',\n  award: 'award',\n  bullseye: 'bullseye',\n  \n  // 位置图标\n  flag: 'flag',\n  'map-marker': 'map-marker',\n  compass: 'compass',\n  route: 'route',\n  map: 'map',\n\n  // 图表图标\n  chart: 'chart-bar'\n} as const\n\nexport type IconName = keyof typeof iconMap\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;AAClB;AACA;;;AAmGA,UAAU;AACV,wKAAA,CAAA,UAAO,CAAC,GAAG,CACT,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,kBAAe,EACf,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,iBAAc,EACd,2KAAA,CAAA,YAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,iBAAc,EACd,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,wBAAqB,EACrB,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,oBAAiB,EACjB,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,YAAU,EACV,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,OAAI,EACJ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,QAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,iBAAW,EACX,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,aAAU;AAIL,MAAM,UAAU;IACrB,OAAO;IACP,QAAQ;IACR,eAAe;IACf,MAAM;IACN,iBAAiB;IACjB,UAAU;IACV,QAAQ;IACR,kBAAkB;IAElB,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;IACR,MAAM;IACN,UAAU;IAEV,OAAO;IACP,QAAQ;IACR,oBAAoB;IACpB,kBAAkB;IAClB,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,gBAAgB;IAChB,MAAM;IAEN,OAAO;IACP,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,OAAO;IACP,OAAO;IAEP,OAAO;IACP,wBAAwB;IACxB,eAAe;IACf,mBAAmB;IACnB,MAAM;IACN,cAAc;IACd,SAAS;IACT,SAAS;IAET,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,qBAAqB;IACrB,WAAW;IACX,mBAAmB;IAEnB,SAAS;IACT,UAAU;IACV,aAAa;IACb,aAAa;IACb,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IAEN,OAAO;IACP,UAAU;IACV,OAAO;IAEP,OAAO;IACP,SAAS;IACT,IAAI;IACJ,MAAM;IACN,OAAO;IAEP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IAER,OAAO;IACP,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IAER,OAAO;IACP,SAAS;IACT,eAAe;IACf,OAAO;IAEP,OAAO;IACP,WAAW;IACX,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IAEL,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IAEV,OAAO;IACP,MAAM;IACN,cAAc;IACd,SAAS;IACT,OAAO;IACP,KAAK;IAEL,OAAO;IACP,OAAO;AACT", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/icon.tsx"], "sourcesContent": ["\"use client\"\n\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome'\nimport { IconName, iconMap } from '@/lib/fontawesome'\nimport { cn } from '@/lib/utils'\n\ninterface IconProps {\n  name: IconName\n  className?: string\n  size?: 'xs' | 'sm' | 'lg' | 'xl' | '2xl'\n  spin?: boolean\n  pulse?: boolean\n  color?: string\n}\n\nexport function Icon({\n  name,\n  className,\n  size,\n  spin = false,\n  pulse = false,\n  color\n}: IconProps) {\n  // 如果 name 为 undefined 或不存在于 iconMap 中，使用默认图标\n  const iconName = name && iconMap[name] ? name : 'question-circle'\n\n  return (\n    <FontAwesomeIcon\n      icon={iconMap[iconName] as any}\n      className={cn(className)}\n      size={size}\n      spin={spin}\n      pulse={pulse}\n      color={color}\n    />\n  )\n}\n\nexport type { IconName }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeO,SAAS,KAAK,KAOT;QAPS,EACnB,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,OAAO,KAAK,EACZ,QAAQ,KAAK,EACb,KAAK,EACK,GAPS;IAQnB,6CAA6C;IAC7C,MAAM,WAAW,QAAQ,qHAAA,CAAA,UAAO,CAAC,KAAK,GAAG,OAAO;IAEhD,qBACE,6LAAC,uKAAA,CAAA,kBAAe;QACd,MAAM,qHAAA,CAAA,UAAO,CAAC,SAAS;QACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;QACd,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;;;;;;AAGb;KArBgB", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst DropdownMenu = DropdownMenuPrimitive.Root;\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n));\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName;\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName;\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className,\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n));\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n));\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName;\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n));\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n));\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  );\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,6JAAA,CAAA,aAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sLACA,4YACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,6JAAA,CAAA,aAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/theme-switcher.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { <PERSON>pt<PERSON>, <PERSON>, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\nimport { useEffect, useState } from \"react\";\n\nconst ThemeSwitcher = () => {\n  const [mounted, setMounted] = useState(false);\n  const { theme, setTheme } = useTheme();\n\n  // useEffect only runs on the client, so now we can safely show the UI\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  const ICON_SIZE = 16;\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size={\"sm\"}>\n          {theme === \"light\" ? (\n            <Sun\n              key=\"light\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          ) : theme === \"dark\" ? (\n            <Moon\n              key=\"dark\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          ) : (\n            <Laptop\n              key=\"system\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-content\" align=\"start\">\n        <DropdownMenuRadioGroup\n          value={theme}\n          onValueChange={(e) => setTheme(e)}\n        >\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"light\">\n            <Sun size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>Light</span>\n          </DropdownMenuRadioItem>\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"dark\">\n            <Moon size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>Dark</span>\n          </DropdownMenuRadioItem>\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"system\">\n            <Laptop size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>System</span>\n          </DropdownMenuRadioItem>\n        </DropdownMenuRadioGroup>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n};\n\nexport { ThemeSwitcher };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;AAcA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;QACb;kCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,YAAY;IAElB,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAM;8BAC3B,UAAU,wBACT,6LAAC,mMAAA,CAAA,MAAG;wBAEF,MAAM;wBACN,WAAW;uBAFP;;;;mEAIJ,UAAU,uBACZ,6LAAC,qMAAA,CAAA,OAAI;wBAEH,MAAM;wBACN,WAAW;uBAFP;;;;iFAKN,6LAAC,yMAAA,CAAA,SAAM;wBAEL,MAAM;wBACN,WAAW;uBAFP;;;;;;;;;;;;;;;0BAOZ,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAY,OAAM;0BAC/C,cAAA,6LAAC,wIAAA,CAAA,yBAAsB;oBACrB,OAAO;oBACP,eAAe,CAAC,IAAM,SAAS;;sCAE/B,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,mMAAA,CAAA,MAAG;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC3D,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC5D,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC9D,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GA7DM;;QAEwB,mJAAA,CAAA,WAAQ;;;KAFhC", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/dashboard-header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { usePathname } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\n\nimport { ThemeSwitcher } from '@/components/theme-switcher'\n\ninterface DashboardHeaderProps {\n  onCreatePrompt?: () => void\n  children?: React.ReactNode\n}\n\nexport function DashboardHeader({ onCreatePrompt, children }: DashboardHeaderProps) {\n  const pathname = usePathname()\n\n  const navItems = [\n    {\n      href: '/dashboard',\n      label: '提示词',\n      icon: 'home' as const,\n      active: pathname === '/dashboard'\n    },\n    {\n      href: '/dashboard/search',\n      label: '搜索',\n      icon: 'search' as const,\n      active: pathname === '/dashboard/search'\n    },\n    {\n      href: '/dashboard/categories',\n      label: '分类管理',\n      icon: 'folder' as const,\n      active: pathname === '/dashboard/categories'\n    },\n    {\n      href: '/dashboard/stats',\n      label: '数据统计',\n      icon: 'chart' as const,\n      active: pathname === '/dashboard/stats'\n    }\n  ]\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 左侧：Logo 和导航 */}\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/dashboard\" className=\"flex items-center gap-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg\">\n                <Icon name=\"lightbulb\" className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                提示词管理\n              </span>\n            </Link>\n\n            <nav className=\"hidden md:flex items-center gap-1\">\n              {navItems.map((item) => (\n                <Link key={item.href} href={item.href}>\n                  <Button\n                    variant={item.active ? \"secondary\" : \"ghost\"}\n                    size=\"sm\"\n                    className=\"gap-2\"\n                  >\n                    <Icon name={item.icon} className=\"h-4 w-4\" />\n                    {item.label}\n                  </Button>\n                </Link>\n              ))}\n            </nav>\n          </div>\n\n          {/* 右侧：操作按钮 */}\n          <div className=\"flex items-center gap-2\">\n            {onCreatePrompt && (\n              <Button\n                variant=\"default\"\n                size=\"sm\"\n                onClick={onCreatePrompt}\n                className=\"hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200\"\n              >\n                <Icon name=\"plus\" className=\"h-4 w-4 mr-2\" />\n                新建提示词\n              </Button>\n            )}\n            \n            {children}\n            <ThemeSwitcher />\n          </div>\n        </div>\n\n        {/* 移动端导航 */}\n        <div className=\"md:hidden border-t border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex items-center gap-1 py-2\">\n            {navItems.map((item) => (\n              <Link key={item.href} href={item.href} className=\"flex-1\">\n                <Button\n                  variant={item.active ? \"secondary\" : \"ghost\"}\n                  size=\"sm\"\n                  className=\"w-full gap-2\"\n                >\n                  <Icon name={item.icon} className=\"h-4 w-4\" />\n                  {item.label}\n                </Button>\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;AAcO,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,cAAc,EAAE,QAAQ,EAAwB,GAAlD;;IAC9B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAY,WAAU;;;;;;;;;;;sDAEnC,6LAAC;4CAAK,WAAU;sDAAkD;;;;;;;;;;;;8CAKpE,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAAiB,MAAM,KAAK,IAAI;sDACnC,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,KAAK,MAAM,GAAG,cAAc;gDACrC,MAAK;gDACL,WAAU;;kEAEV,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;;;;;;oDAChC,KAAK,KAAK;;;;;;;2CAPJ,KAAK,IAAI;;;;;;;;;;;;;;;;sCAe1B,6LAAC;4BAAI,WAAU;;gCACZ,gCACC,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;wCAAiB;;;;;;;gCAKhD;8CACD,6LAAC,mIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;8BAKlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;gCAAE,WAAU;0CAC/C,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,KAAK,MAAM,GAAG,cAAc;oCACrC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAM,KAAK,IAAI;4CAAE,WAAU;;;;;;wCAChC,KAAK,KAAK;;;;;;;+BAPJ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBlC;GApGgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/search-bar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef, useCallback } from 'react'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { Badge } from '@/components/ui/badge'\nimport { cn } from '@/lib/utils'\n\ninterface SearchHistory {\n  id: string\n  term: string\n  count: number\n  lastSearched: string\n}\n\ninterface SearchBarProps {\n  value: string\n  onChange: (value: string) => void\n  onSearch: (term: string) => void\n  onRemoteSearch?: (term: string) => void // 远程搜索回调\n  placeholder?: string\n  searchHistory?: SearchHistory[]\n  onClearHistory?: () => void\n  className?: string\n  showRemoteSearch?: boolean // 是否显示远程搜索选项\n}\n\nexport function SearchBar({\n  value,\n  onChange,\n  onSearch,\n  onRemoteSearch,\n  placeholder = \"搜索提示词...\",\n  searchHistory = [],\n  onClearHistory,\n  className,\n  showRemoteSearch = false\n}: SearchBarProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [highlightedIndex, setHighlightedIndex] = useState(-1)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)\n\n  // 防抖搜索函数\n  const debouncedSearch = useCallback((searchTerm: string) => {\n    if (debounceTimerRef.current) {\n      clearTimeout(debounceTimerRef.current)\n    }\n\n    debounceTimerRef.current = setTimeout(() => {\n      onSearch(searchTerm)\n    }, 300) // 300ms 防抖延迟\n  }, [onSearch])\n\n  // 过滤搜索历史\n  const filteredHistory = searchHistory\n    .filter(item =>\n      item?.term &&\n      typeof item.term === 'string' &&\n      item.term.toLowerCase().includes(value.toLowerCase()) &&\n      item.term !== value\n    )\n    .slice(0, 5)\n\n  // 处理键盘事件\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!isOpen) return\n\n      switch (e.key) {\n        case 'ArrowDown':\n          e.preventDefault()\n          setHighlightedIndex(prev => \n            prev < filteredHistory.length - 1 ? prev + 1 : prev\n          )\n          break\n        case 'ArrowUp':\n          e.preventDefault()\n          setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1)\n          break\n        case 'Enter':\n          e.preventDefault()\n          if (highlightedIndex >= 0 && filteredHistory[highlightedIndex]) {\n            const selectedTerm = filteredHistory[highlightedIndex].term\n            onChange(selectedTerm)\n            onSearch(selectedTerm)\n            setIsOpen(false)\n            setHighlightedIndex(-1)\n          } else if (value.trim()) {\n            onSearch(value.trim())\n            setIsOpen(false)\n            setHighlightedIndex(-1)\n          }\n          break\n        case 'Escape':\n          setIsOpen(false)\n          setHighlightedIndex(-1)\n          inputRef.current?.blur()\n          break\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isOpen, highlightedIndex, filteredHistory])\n\n  // 点击外部关闭下拉框\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current && \n        !dropdownRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setIsOpen(false)\n        setHighlightedIndex(-1)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      // 清理防抖定时器\n      if (debounceTimerRef.current) {\n        clearTimeout(debounceTimerRef.current)\n      }\n    }\n  }, [])\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value\n    onChange(newValue)\n    setIsOpen(newValue.length > 0 || filteredHistory.length > 0)\n    setHighlightedIndex(-1)\n\n    // 实时搜索：输入时触发防抖搜索\n    debouncedSearch(newValue)\n  }\n\n  const handleInputFocus = () => {\n    setIsOpen(value.length > 0 || filteredHistory.length > 0)\n  }\n\n  const handleSearch = () => {\n    if (value.trim()) {\n      onSearch(value.trim())\n      setIsOpen(false)\n      setHighlightedIndex(-1)\n    }\n  }\n\n  const handleHistoryItemClick = (term: string) => {\n    onChange(term)\n    onSearch(term)\n    setIsOpen(false)\n    setHighlightedIndex(-1)\n  }\n\n  const handleClear = () => {\n    onChange('')\n    setIsOpen(false)\n    setHighlightedIndex(-1)\n    inputRef.current?.focus()\n  }\n\n  return (\n    <div className={cn(\"relative w-full\", className)}>\n      <div className=\"relative\">\n        <Icon \n          name=\"search\" \n          className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" \n        />\n        <Input\n          ref={inputRef}\n          type=\"text\"\n          placeholder={placeholder}\n          value={value}\n          onChange={handleInputChange}\n          onFocus={handleInputFocus}\n          className=\"pl-10 pr-20\"\n        />\n        <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\">\n          {value && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-6 w-6 hover:bg-gray-100\"\n              onClick={handleClear}\n            >\n              <Icon name=\"times\" className=\"h-3 w-3\" />\n            </Button>\n          )}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600\"\n            onClick={handleSearch}\n            disabled={!value.trim()}\n          >\n            搜索\n          </Button>\n        </div>\n      </div>\n\n      {/* 搜索历史下拉框 */}\n      {isOpen && (filteredHistory.length > 0 || searchHistory.length > 0) && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto\"\n        >\n          {filteredHistory.length > 0 && (\n            <div className=\"p-2\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-xs font-medium text-muted-foreground\">搜索历史</span>\n                {onClearHistory && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-5 px-1 text-xs text-muted-foreground hover:text-red-600\"\n                    onClick={onClearHistory}\n                  >\n                    清除\n                  </Button>\n                )}\n              </div>\n              {filteredHistory.map((item, index) => (\n                <div\n                  key={item.id}\n                  className={cn(\n                    \"flex items-center justify-between px-2 py-1.5 rounded cursor-pointer transition-colors\",\n                    highlightedIndex === index \n                      ? \"bg-blue-50 text-blue-600\" \n                      : \"hover:bg-gray-50\"\n                  )}\n                  onClick={() => handleHistoryItemClick(item.term)}\n                >\n                  <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n                    <Icon name=\"clock\" className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                    <span className=\"text-sm truncate\">{item.term}</span>\n                  </div>\n                  <Badge variant=\"secondary\" className=\"text-xs ml-2\">\n                    {item.count}\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {filteredHistory.length === 0 && value && (\n            <div className=\"p-2\">\n              <div className=\"p-2 text-center text-sm text-muted-foreground\">\n                正在搜索 &quot;{value}&quot;...\n              </div>\n              {showRemoteSearch && onRemoteSearch && (\n                <button\n                  className=\"w-full p-2 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors\"\n                  onClick={() => {\n                    onRemoteSearch(value)\n                    setIsOpen(false)\n                  }}\n                >\n                  <Icon name=\"search\" className=\"h-3 w-3 mr-2 inline\" />\n                  在线搜索更多结果\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4BO,SAAS,UAAU,KAUT;QAVS,EACxB,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,cAAc,UAAU,EACxB,gBAAgB,EAAE,EAClB,cAAc,EACd,SAAS,EACT,mBAAmB,KAAK,EACT,GAVS;;IAWxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACnC,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,iBAAiB,OAAO,GAAG;0DAAW;oBACpC,SAAS;gBACX;yDAAG,MAAK,aAAa;QACvB;iDAAG;QAAC;KAAS;IAEb,SAAS;IACT,MAAM,kBAAkB,cACrB,MAAM,CAAC,CAAA,OACN,CAAA,iBAAA,2BAAA,KAAM,IAAI,KACV,OAAO,KAAK,IAAI,KAAK,YACrB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAClD,KAAK,IAAI,KAAK,OAEf,KAAK,CAAC,GAAG;IAEZ,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;qDAAgB,CAAC;oBACrB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,EAAE,GAAG;wBACX,KAAK;4BACH,EAAE,cAAc;4BAChB;qEAAoB,CAAA,OAClB,OAAO,gBAAgB,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAEjD;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB;qEAAoB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;;4BACnD;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB,IAAI,oBAAoB,KAAK,eAAe,CAAC,iBAAiB,EAAE;gCAC9D,MAAM,eAAe,eAAe,CAAC,iBAAiB,CAAC,IAAI;gCAC3D,SAAS;gCACT,SAAS;gCACT,UAAU;gCACV,oBAAoB,CAAC;4BACvB,OAAO,IAAI,MAAM,IAAI,IAAI;gCACvB,SAAS,MAAM,IAAI;gCACnB,UAAU;gCACV,oBAAoB,CAAC;4BACvB;4BACA;wBACF,KAAK;gCAGH;4BAFA,UAAU;4BACV,oBAAoB,CAAC;6BACrB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,IAAI;4BACtB;oBACJ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;uCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;8BAAG;QAAC;QAAQ;QAAkB;KAAgB;IAE9C,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;0DAAqB,CAAC;wBAIvB;oBAHH,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,GAAC,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,MAAM,MAAM,IACxC;wBACA,UAAU;wBACV,oBAAoB,CAAC;oBACvB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;uCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,UAAU;oBACV,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,UAAU,SAAS,MAAM,GAAG,KAAK,gBAAgB,MAAM,GAAG;QAC1D,oBAAoB,CAAC;QAErB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,UAAU,MAAM,MAAM,GAAG,KAAK,gBAAgB,MAAM,GAAG;IACzD;IAEA,MAAM,eAAe;QACnB,IAAI,MAAM,IAAI,IAAI;YAChB,SAAS,MAAM,IAAI;YACnB,UAAU;YACV,oBAAoB,CAAC;QACvB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,SAAS;QACT,SAAS;QACT,UAAU;QACV,oBAAoB,CAAC;IACvB;IAEA,MAAM,cAAc;YAIlB;QAHA,SAAS;QACT,UAAU;QACV,oBAAoB,CAAC;SACrB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;wBACH,MAAK;wBACL,WAAU;;;;;;kCAEZ,6LAAC,6HAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAQ,WAAU;;;;;;;;;;;0CAGjC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,UAAU,CAAC,MAAM,IAAI;0CACtB;;;;;;;;;;;;;;;;;;YAOJ,UAAU,CAAC,gBAAgB,MAAM,GAAG,KAAK,cAAc,MAAM,GAAG,CAAC,mBAChE,6LAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;oCAC3D,gCACC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;;;;;;;4BAKJ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;oCAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0FACA,qBAAqB,QACjB,6BACA;oCAEN,SAAS,IAAM,uBAAuB,KAAK,IAAI;;sDAE/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAQ,WAAU;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,IAAI;;;;;;;;;;;;sDAE/C,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,KAAK,KAAK;;;;;;;mCAdR,KAAK,EAAE;;;;;;;;;;;oBAqBnB,gBAAgB,MAAM,KAAK,KAAK,uBAC/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAgD;oCACjD;oCAAM;;;;;;;4BAEnB,oBAAoB,gCACnB,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP,eAAe;oCACf,UAAU;gCACZ;;kDAEA,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAS,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GArPgB;KAAA", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/code-block.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON> as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter'\nimport { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { cn } from '@/lib/utils'\n\ninterface CodeBlockProps {\n  children: string\n  className?: string\n  inline?: boolean\n}\n\nexport function CodeBlock({ children, className, inline }: CodeBlockProps) {\n  const [copied, setCopied] = useState(false)\n  const { toast } = useToast()\n  \n  const match = /language-(\\w+)/.exec(className || '')\n  const language = match ? match[1] : ''\n  \n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(children)\n      setCopied(true)\n      toast({\n        title: \"复制成功\",\n        description: \"代码已复制到剪贴板\",\n      })\n      setTimeout(() => setCopied(false), 2000)\n    } catch (error) {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  if (inline) {\n    return (\n      <code className=\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold\">\n        {children}\n      </code>\n    )\n  }\n\n  return (\n    <div className=\"relative group\">\n      <div className=\"absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleCopy}\n          className={cn(\n            \"h-8 px-2 bg-background/80 backdrop-blur-sm border-border/50\",\n            copied && \"bg-green-100 border-green-300 text-green-700\"\n          )}\n        >\n          <Icon \n            name={copied ? \"check\" : \"copy\"} \n            className=\"h-3 w-3 mr-1\" \n          />\n          {copied ? \"已复制\" : \"复制\"}\n        </Button>\n      </div>\n      \n      <SyntaxHighlighter\n        style={tomorrow}\n        language={language}\n        PreTag=\"div\"\n        className=\"rounded-md !mt-0 !mb-0\"\n        customStyle={{\n          margin: 0,\n          borderRadius: '0.375rem',\n          fontSize: '0.875rem',\n          lineHeight: '1.25rem',\n        }}\n      >\n        {children}\n      </SyntaxHighlighter>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAgBO,SAAS,UAAU,KAA+C;QAA/C,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAkB,GAA/C;;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;IACjD,MAAM,WAAW,QAAQ,KAAK,CAAC,EAAE,GAAG;IAEpC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,IAAI,QAAQ;QACV,qBACE,6LAAC;YAAK,WAAU;sBACb;;;;;;IAGP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA,UAAU;;sCAGZ,6LAAC,4HAAA,CAAA,OAAI;4BACH,MAAM,SAAS,UAAU;4BACzB,WAAU;;;;;;wBAEX,SAAS,QAAQ;;;;;;;;;;;;0BAItB,6LAAC,6MAAA,CAAA,QAAiB;gBAChB,OAAO,sOAAA,CAAA,WAAQ;gBACf,UAAU;gBACV,QAAO;gBACP,WAAU;gBACV,aAAa;oBACX,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;GArEgB;;QAEI,wHAAA,CAAA,WAAQ;;;KAFZ", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/markdown-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport ReactMarkdown from 'react-markdown'\nimport { CodeBlock } from './code-block'\n\ninterface MarkdownPreviewProps {\n  content: string\n  className?: string\n  truncate?: boolean\n  maxLines?: number\n}\n\nexport function MarkdownPreview({ \n  content, \n  className = \"\", \n  truncate = false, \n  maxLines = 3 \n}: MarkdownPreviewProps) {\n  return (\n    <div className={`prose prose-sm max-w-none dark:prose-invert ${className}`}>\n      <ReactMarkdown\n        components={{\n          // 简化的代码块渲染（卡片预览中不显示复制按钮）\n          code({ node, inline, className, children, ...props }) {\n            if (inline) {\n              return (\n                <code className=\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold\">\n                  {children}\n                </code>\n              )\n            }\n            \n            // 在卡片预览中，代码块使用简化样式\n            if (truncate) {\n              return (\n                <pre className=\"bg-muted rounded p-2 text-xs overflow-hidden\">\n                  <code>{String(children).replace(/\\n$/, '')}</code>\n                </pre>\n              )\n            }\n            \n            // 完整视图中使用带复制功能的代码块\n            return (\n              <CodeBlock\n                inline={inline}\n                className={className}\n                {...props}\n              >\n                {String(children).replace(/\\n$/, '')}\n              </CodeBlock>\n            )\n          },\n          \n          // 简化其他元素的渲染\n          h1: ({ children }) => truncate ? \n            <span className=\"font-bold text-base\">{children}</span> : \n            <h1 className=\"text-xl font-bold mb-2\">{children}</h1>,\n          h2: ({ children }) => truncate ? \n            <span className=\"font-semibold text-sm\">{children}</span> : \n            <h2 className=\"text-lg font-semibold mb-2\">{children}</h2>,\n          h3: ({ children }) => truncate ? \n            <span className=\"font-medium text-sm\">{children}</span> : \n            <h3 className=\"text-base font-medium mb-1\">{children}</h3>,\n          \n          p: ({ children }) => truncate ? \n            <span className=\"inline\">{children} </span> : \n            <p className=\"mb-2\">{children}</p>,\n          \n          ul: ({ children }) => truncate ? \n            <span className=\"inline\">{children}</span> : \n            <ul className=\"list-disc list-inside mb-2\">{children}</ul>,\n          \n          ol: ({ children }) => truncate ? \n            <span className=\"inline\">{children}</span> : \n            <ol className=\"list-decimal list-inside mb-2\">{children}</ol>,\n          \n          li: ({ children }) => truncate ? \n            <span className=\"inline\">• {children} </span> : \n            <li className=\"mb-1\">{children}</li>,\n          \n          strong: ({ children }) => <strong className=\"font-semibold\">{children}</strong>,\n          em: ({ children }) => <em className=\"italic\">{children}</em>,\n          \n          blockquote: ({ children }) => truncate ? \n            <span className=\"inline italic\">{children}</span> : \n            <blockquote className=\"border-l-4 border-muted pl-4 italic mb-2\">{children}</blockquote>,\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,gBAAgB,KAKT;QALS,EAC9B,OAAO,EACP,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,CAAC,EACS,GALS;IAM9B,qBACE,6LAAC;QAAI,WAAW,AAAC,+CAAwD,OAAV;kBAC7D,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,YAAY;gBACV,yBAAyB;gBACzB,MAAK,KAA+C;wBAA/C,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,GAA/C;oBACH,IAAI,QAAQ;wBACV,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBAEA,mBAAmB;oBACnB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAM,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;oBAG7C;oBAEA,mBAAmB;oBACnB,qBACE,6LAAC,qIAAA,CAAA,YAAS;wBACR,QAAQ;wBACR,WAAW;wBACV,GAAG,KAAK;kCAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;gBAGvC;gBAEA,YAAY;gBACZ,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAuB;;;;;+CACvC,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;;gBAC1C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAyB;;;;;+CACzC,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAC9C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAuB;;;;;+CACvC,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAE9C,GAAG;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACnB,6LAAC;wBAAK,WAAU;;4BAAU;4BAAS;;;;;;+CACnC,6LAAC;wBAAE,WAAU;kCAAQ;;;;;;;gBAEvB,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;+CAC1B,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAE9C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;+CAC1B,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;;gBAEjD,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;;4BAAS;4BAAG;4BAAS;;;;;;+CACrC,6LAAC;wBAAG,WAAU;kCAAQ;;;;;;;gBAExB,QAAQ;wBAAC,EAAE,QAAQ,EAAE;yCAAK,6LAAC;wBAAO,WAAU;kCAAiB;;;;;;;gBAC7D,IAAI;wBAAC,EAAE,QAAQ,EAAE;yCAAK,6LAAC;wBAAG,WAAU;kCAAU;;;;;;;gBAE9C,YAAY;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBAC5B,6LAAC;wBAAK,WAAU;kCAAiB;;;;;+CACjC,6LAAC;wBAAW,WAAU;kCAA4C;;;;;;;YACtE;sBAEC;;;;;;;;;;;AAIT;KAhFgB", "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/prompt-card.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { MarkdownPreview } from '@/components/ui/markdown-preview'\nimport { cn } from '@/lib/utils'\n\ninterface Tag {\n  id: string\n  name: string\n  color: string\n}\n\ninterface Category {\n  id: string\n  name: string\n  color: string\n  icon: string\n}\n\ninterface PromptCardProps {\n  id: string\n  title: string\n  description?: string\n  content: string\n  category?: Category\n  isLocal?: boolean\n  tags?: Tag[]\n  usageCount: number\n  createdAt: string\n  updatedAt: string\n  onView: (id: string) => void\n  onEdit: (id: string) => void\n  onDelete: (id: string) => void\n  onCopy: (content: string, id: string) => void\n  className?: string\n}\n\nexport function PromptCard({\n  id,\n  title,\n  description,\n  content,\n  category,\n  tags = [],\n  usageCount,\n  createdAt,\n  updatedAt,\n  onView,\n  onEdit,\n  onDelete,\n  onCopy,\n  isLocal = false,\n  className\n}: PromptCardProps) {\n  const [isHovered, setIsHovered] = useState(false)\n  const { toast } = useToast()\n\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(content)\n      onCopy(content, id)\n      toast({\n        title: \"复制成功\",\n        description: \"提示词已复制到剪贴板\",\n      })\n    } catch {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板，请手动复制\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  return (\n    <Card\n      data-prompt-id={id}\n      data-testid=\"prompt-card\"\n      className={cn(\n        \"group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] cursor-pointer\",\n        \"border-l-4\",\n        category?.color ? `border-l-[${category.color}]` : \"border-l-blue-500\",\n        className\n      )}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      onClick={() => onView(id)}\n    >\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <CardTitle className=\"text-lg font-semibold mb-1 overflow-hidden\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"line-clamp-2 flex-1\">\n                  {title}\n                </div>\n                {isLocal && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shrink-0\">\n                    <Icon name=\"smartphone\" className=\"h-3 w-3 mr-1\" />\n                    本地\n                  </span>\n                )}\n              </div>\n            </CardTitle>\n            {description && (\n              <CardDescription className=\"text-sm text-muted-foreground overflow-hidden\">\n                <div className=\"line-clamp-2\">\n                  {description}\n                </div>\n              </CardDescription>\n            )}\n          </div>\n          \n          {/* 操作按钮 */}\n          <div className={cn(\n            \"flex items-center gap-1 transition-opacity duration-200\",\n            isHovered ? \"opacity-100\" : \"opacity-0 md:opacity-0\",\n            \"sm:opacity-100\" // 在小屏幕上始终显示\n          )}>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-8 w-8 hover:bg-blue-100 hover:text-blue-600\"\n              onClick={(e) => {\n                e.stopPropagation()\n                handleCopy()\n              }}\n            >\n              <Icon name=\"copy\" className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-8 w-8 hover:bg-green-100 hover:text-green-600\"\n              onClick={(e) => {\n                e.stopPropagation()\n                onEdit(id)\n              }}\n            >\n              <Icon name=\"edit\" className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-8 w-8 hover:bg-red-100 hover:text-red-600\"\n              onClick={(e) => {\n                e.stopPropagation()\n                onDelete(id)\n              }}\n            >\n              <Icon name=\"trash\" className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* 分类信息 - 上半部分 */}\n        {category && (\n          <div className=\"flex items-center gap-2 mt-1.5\">\n            <div\n              className=\"flex items-center gap-1 px-2 py-1 rounded text-xs font-medium\"\n              style={{\n                backgroundColor: `${category.color}12`,\n                color: category.color,\n                border: `1px solid ${category.color}25`\n              }}\n            >\n              <Icon name={category.icon as any} className=\"h-3 w-3\" />\n              {category.name}\n            </div>\n          </div>\n        )}\n      </CardHeader>\n\n      <CardContent className=\"pt-0\">\n        {/* 内容预览 */}\n        <div className=\"relative text-sm text-muted-foreground mb-4 overflow-hidden\">\n          {/* Markdown 指示器 */}\n          <div className=\"absolute top-0 right-0 z-10 bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400 text-xs px-1.5 py-0.5 rounded-sm opacity-70\">\n            <Icon name=\"eye\" className=\"h-2.5 w-2.5 inline\" />\n          </div>\n\n          <div className=\"line-clamp-3 pr-8\">\n            <MarkdownPreview\n              content={content}\n              truncate={true}\n              maxLines={3}\n              className=\"text-sm text-muted-foreground prose-p:inline prose-headings:inline prose-strong:font-medium prose-em:italic\"\n            />\n          </div>\n        </div>\n\n        {/* 底部信息 */}\n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-1\">\n              <Icon name=\"eye\" className=\"h-3 w-3\" />\n              <span>{usageCount} 次使用</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Icon name=\"clock\" className=\"h-3 w-3\" />\n              <span>{formatDate(updatedAt)}</span>\n            </div>\n          </div>\n\n          {/* 快速复制按钮 */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600\"\n            onClick={(e) => {\n              e.stopPropagation()\n              handleCopy()\n            }}\n          >\n            <Icon name=\"copy\" className=\"h-3 w-3 mr-1\" />\n            复制\n          </Button>\n        </div>\n\n        {/* 标签 - 最底部 */}\n        {tags.length > 0 && (\n          <div className=\"mt-2 pt-2 border-t border-gray-100\">\n            <div className=\"flex flex-wrap gap-1\">\n              {tags.slice(0, 5).map((tag) => (\n                <Badge\n                  key={tag.id}\n                  variant=\"outline\"\n                  className=\"text-xs px-1.5 py-0.5 border-dashed h-5\"\n                  style={{\n                    backgroundColor: `${tag.color}06`,\n                    color: tag.color,\n                    borderColor: `${tag.color}30`\n                  }}\n                >\n                  {tag.name}\n                </Badge>\n              ))}\n              {tags.length > 5 && (\n                <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5 border-dashed text-muted-foreground h-5\">\n                  +{tags.length - 5}\n                </Badge>\n              )}\n            </div>\n          </div>\n        )}\n      </CardContent>\n\n      {/* 悬停时的边框效果 */}\n      <div className={cn(\n        \"absolute inset-0 border-2 border-transparent transition-colors duration-300 rounded-lg pointer-events-none\",\n        isHovered && \"border-blue-200\"\n      )} />\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AA0CO,SAAS,WAAW,KAgBT;QAhBS,EACzB,EAAE,EACF,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,OAAO,EAAE,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,UAAU,KAAK,EACf,SAAS,EACO,GAhBS;;IAiBzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO,SAAS;YAChB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,UAAM;YACN,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QACH,kBAAgB;QAChB,eAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gHACA,cACA,CAAA,qBAAA,+BAAA,SAAU,KAAK,IAAG,AAAC,aAA2B,OAAf,SAAS,KAAK,EAAC,OAAK,qBACnD;QAEF,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;QACjC,SAAS,IAAM,OAAO;;0BAEtB,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ;;;;;;gDAEF,yBACC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAa,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;oCAM1D,6BACC,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDACzB,cAAA,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,2DACA,YAAY,gBAAgB,0BAC5B,iBAAiB,YAAY;;;kDAE7B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB;wCACF;kDAEA,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;;;;;;kDAE9B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,OAAO;wCACT;kDAEA,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;;;;;;kDAE9B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,SAAS;wCACX;kDAEA,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMlC,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;gCACnC,OAAO,SAAS,KAAK;gCACrB,QAAQ,AAAC,aAA2B,OAAf,SAAS,KAAK,EAAC;4BACtC;;8CAEA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAM,SAAS,IAAI;oCAAS,WAAU;;;;;;gCAC3C,SAAS,IAAI;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAM,WAAU;;;;;;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2IAAA,CAAA,kBAAe;oCACd,SAAS;oCACT,UAAU;oCACV,UAAU;oCACV,WAAU;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAM,WAAU;;;;;;0DAC3B,6LAAC;;oDAAM;oDAAW;;;;;;;;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAQ,WAAU;;;;;;0DAC7B,6LAAC;0DAAM,WAAW;;;;;;;;;;;;;;;;;;0CAKtB,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;;kDAEA,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAO,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;oBAMhD,KAAK,MAAM,GAAG,mBACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrB,6LAAC,6HAAA,CAAA,QAAK;wCAEJ,SAAQ;wCACR,WAAU;wCACV,OAAO;4CACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;4CAC9B,OAAO,IAAI,KAAK;4CAChB,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wCAC5B;kDAEC,IAAI,IAAI;uCATJ,IAAI,EAAE;;;;;gCAYd,KAAK,MAAM,GAAG,mBACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAgE;wCAC/F,KAAK,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAS5B,6LAAC;gBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,8GACA,aAAa;;;;;;;;;;;;AAIrB;GAjOgB;;QAkBI,wHAAA,CAAA,WAAQ;;;KAlBZ", "debugId": null}}, {"offset": {"line": 2365, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">关闭</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2534, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2576, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/api/client.ts"], "sourcesContent": ["import type {\n  PromptWithDetails,\n  PromptInsert,\n  PromptUpdate,\n  SearchParams,\n  PaginatedResponse,\n  Category,\n  CategoryWithCount,\n  CategoryInsert,\n  CategoryUpdate,\n  Tag,\n  TagInsert,\n  TagUpdate,\n  AppPreferences,\n  AppPreferencesUpdate\n} from '@/types/database'\n\nconst API_BASE = process.env.NEXT_PUBLIC_APP_URL || (typeof window !== 'undefined' ? window.location.origin : '')\n\n// 通用 API 调用函数\nasync function apiCall<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE}/api${endpoint}`\n  \n  const response = await fetch(url, {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  })\n\n  if (!response.ok) {\n    const error = await response.json().catch(() => ({ error: 'Network error' }))\n    throw new Error(error.error || `HTTP ${response.status}`)\n  }\n\n  return response.json()\n}\n\n// ===== 提示词 API =====\n\nexport async function getPrompts(params: SearchParams = {}): Promise<PaginatedResponse<PromptWithDetails>> {\n  const searchParams = new URLSearchParams()\n  \n  if (params.query) searchParams.set('query', params.query)\n  if (params.categoryId) searchParams.set('categoryId', params.categoryId)\n  if (params.tagIds?.length) searchParams.set('tagIds', params.tagIds.join(','))\n  if (params.sortBy) searchParams.set('sortBy', params.sortBy)\n  if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder)\n  if (params.limit) searchParams.set('limit', params.limit.toString())\n  if (params.offset) searchParams.set('offset', params.offset.toString())\n\n  const queryString = searchParams.toString()\n  return apiCall<PaginatedResponse<PromptWithDetails>>(\n    `/prompts${queryString ? `?${queryString}` : ''}`\n  )\n}\n\nexport async function getPromptById(id: string): Promise<PromptWithDetails> {\n  return apiCall<PromptWithDetails>(`/prompts/${id}`)\n}\n\nexport async function createPrompt(data: PromptInsert, tagIds?: string[]): Promise<PromptWithDetails> {\n  const payload = tagIds ? { ...data, tagIds } : data\n  return apiCall<PromptWithDetails>('/prompts', {\n    method: 'POST',\n    body: JSON.stringify(payload),\n  })\n}\n\nexport async function updatePrompt(id: string, data: PromptUpdate, tagIds?: string[]): Promise<PromptWithDetails> {\n  const payload = tagIds ? { ...data, tagIds } : data\n  return apiCall<PromptWithDetails>(`/prompts/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(payload),\n  })\n}\n\nexport async function deletePrompt(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/prompts/${id}`, {\n    method: 'DELETE',\n  })\n}\n\nexport async function incrementUsageCount(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/prompts/${id}/usage`, {\n    method: 'POST',\n  })\n}\n\n// ===== 分类 API =====\n\nexport async function getCategories(): Promise<CategoryWithCount[]> {\n  return apiCall<CategoryWithCount[]>('/categories')\n}\n\nexport async function getCategoryById(id: string): Promise<Category> {\n  return apiCall<Category>(`/categories/${id}`)\n}\n\nexport async function createCategory(data: CategoryInsert): Promise<Category> {\n  return apiCall<Category>('/categories', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function updateCategory(id: string, data: CategoryUpdate): Promise<Category> {\n  return apiCall<Category>(`/categories/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function deleteCategory(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/categories/${id}`, {\n    method: 'DELETE',\n  })\n}\n\nexport async function updateCategoriesOrder(data: { id: string; sortOrder: number }[]): Promise<void> {\n  await apiCall<{ success: boolean }>('/categories', {\n    method: 'PATCH',\n    body: JSON.stringify(data),\n  })\n}\n\n// ===== 标签 API =====\n\nexport async function getTags(): Promise<Tag[]> {\n  return apiCall<Tag[]>('/tags')\n}\n\nexport async function getTagById(id: string): Promise<Tag> {\n  return apiCall<Tag>(`/tags/${id}`)\n}\n\nexport async function createTag(data: TagInsert): Promise<Tag> {\n  return apiCall<Tag>('/tags', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function createTags(data: TagInsert[]): Promise<Tag[]> {\n  return apiCall<Tag[]>('/tags', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function updateTag(id: string, data: TagUpdate): Promise<Tag> {\n  return apiCall<Tag>(`/tags/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function deleteTag(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/tags/${id}`, {\n    method: 'DELETE',\n  })\n}\n\n// ===== 应用偏好设置 API =====\n\nexport async function getAppPreferences(): Promise<AppPreferences> {\n  return apiCall<AppPreferences>('/preferences')\n}\n\nexport async function updateAppPreferences(data: AppPreferencesUpdate): Promise<AppPreferences> {\n  return apiCall<AppPreferences>('/preferences', {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\n// ===== 搜索历史 API =====\n// 简化版本：由于移除了用户认证，搜索历史功能暂时返回空数据\n\nexport async function getSearchHistory(limit: number = 10): Promise<any[]> {\n  // 返回空数组，搜索历史功能暂时禁用\n  return []\n}\n\nexport async function addSearchHistory(searchTerm: string): Promise<void> {\n  // 搜索历史功能暂时禁用，不执行任何操作\n  console.log('搜索历史功能已禁用:', searchTerm)\n}\n\nexport async function clearSearchHistory(): Promise<void> {\n  // 搜索历史功能暂时禁用，不执行任何操作\n  console.log('搜索历史功能已禁用')\n}\n\nexport async function advancedSearch(params: any): Promise<any[]> {\n  // 使用基本的提示词搜索功能\n  const searchParams = {\n    query: params.query,\n    categoryId: params.categoryId,\n    tagIds: params.tagIds,\n    sortBy: params.sortBy || 'updated_at',\n    sortOrder: params.sortOrder || 'desc',\n    limit: params.limit || 20,\n    offset: params.offset || 0\n  }\n\n  const result = await getPrompts(searchParams)\n  return result.data\n}\n\n// ===== 分类名称检查 API =====\n\nexport async function checkCategoryNameExists(name: string, excludeId?: string): Promise<boolean> {\n  try {\n    const categories = await getCategories()\n    return categories.some(cat =>\n      cat.name.toLowerCase() === name.toLowerCase() &&\n      (!excludeId || cat.id !== excludeId)\n    )\n  } catch (error) {\n    console.error('检查分类名称失败:', error)\n    return false\n  }\n}\n\n// ===== 兼容性别名 =====\n\n// 为了保持与现有代码的兼容性，提供一些别名\nexport const incrementPromptUsage = incrementUsageCount\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBiB;AAAjB,MAAM,WAAW,6DAAmC,CAAC,uCAAgC,OAAO,QAAQ,CAAC,MAAM,GAAG,uBAAE;AAEhH,cAAc;AACd,eAAe,QACb,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,UAAS,QAAe,OAAT;IAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;gBAAE,OAAO;YAAgB,CAAC;QAC3E,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;IACxD;IAEA,OAAO,SAAS,IAAI;AACtB;AAIO,eAAe;QAAW,SAAA,iEAAuB,CAAC;QAKnD;IAJJ,MAAM,eAAe,IAAI;IAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK;IACxD,IAAI,OAAO,UAAU,EAAE,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;IACvE,KAAI,iBAAA,OAAO,MAAM,cAAb,qCAAA,eAAe,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC;IACzE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,OAAO,SAAS,EAAE,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;IAEpE,MAAM,cAAc,aAAa,QAAQ;IACzC,OAAO,QACL,AAAC,WAA+C,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;AAEjD;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,QAA2B,AAAC,YAAc,OAAH;AAChD;AAEO,eAAe,aAAa,IAAkB,EAAE,MAAiB;IACtE,MAAM,UAAU,SAAS;QAAE,GAAG,IAAI;QAAE;IAAO,IAAI;IAC/C,OAAO,QAA2B,YAAY;QAC5C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,aAAa,EAAU,EAAE,IAAkB,EAAE,MAAiB;IAClF,MAAM,UAAU,SAAS;QAAE,GAAG,IAAI;QAAE;IAAO,IAAI;IAC/C,OAAO,QAA2B,AAAC,YAAc,OAAH,KAAM;QAClD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,QAA8B,AAAC,YAAc,OAAH,KAAM;QACpD,QAAQ;IACV;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,QAA8B,AAAC,YAAc,OAAH,IAAG,WAAS;QAC1D,QAAQ;IACV;AACF;AAIO,eAAe;IACpB,OAAO,QAA6B;AACtC;AAEO,eAAe,gBAAgB,EAAU;IAC9C,OAAO,QAAkB,AAAC,eAAiB,OAAH;AAC1C;AAEO,eAAe,eAAe,IAAoB;IACvD,OAAO,QAAkB,eAAe;QACtC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAU,EAAE,IAAoB;IACnE,OAAO,QAAkB,AAAC,eAAiB,OAAH,KAAM;QAC5C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,QAA8B,AAAC,eAAiB,OAAH,KAAM;QACvD,QAAQ;IACV;AACF;AAEO,eAAe,sBAAsB,IAAyC;IACnF,MAAM,QAA8B,eAAe;QACjD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAIO,eAAe;IACpB,OAAO,QAAe;AACxB;AAEO,eAAe,WAAW,EAAU;IACzC,OAAO,QAAa,AAAC,SAAW,OAAH;AAC/B;AAEO,eAAe,UAAU,IAAe;IAC7C,OAAO,QAAa,SAAS;QAC3B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,WAAW,IAAiB;IAChD,OAAO,QAAe,SAAS;QAC7B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,UAAU,EAAU,EAAE,IAAe;IACzD,OAAO,QAAa,AAAC,SAAW,OAAH,KAAM;QACjC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,UAAU,EAAU;IACxC,MAAM,QAA8B,AAAC,SAAW,OAAH,KAAM;QACjD,QAAQ;IACV;AACF;AAIO,eAAe;IACpB,OAAO,QAAwB;AACjC;AAEO,eAAe,qBAAqB,IAA0B;IACnE,OAAO,QAAwB,gBAAgB;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe;QAAiB,QAAA,iEAAgB;IACrD,mBAAmB;IACnB,OAAO,EAAE;AACX;AAEO,eAAe,iBAAiB,UAAkB;IACvD,qBAAqB;IACrB,QAAQ,GAAG,CAAC,cAAc;AAC5B;AAEO,eAAe;IACpB,qBAAqB;IACrB,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe,eAAe,MAAW;IAC9C,eAAe;IACf,MAAM,eAAe;QACnB,OAAO,OAAO,KAAK;QACnB,YAAY,OAAO,UAAU;QAC7B,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM,IAAI;QACzB,WAAW,OAAO,SAAS,IAAI;QAC/B,OAAO,OAAO,KAAK,IAAI;QACvB,QAAQ,OAAO,MAAM,IAAI;IAC3B;IAEA,MAAM,SAAS,MAAM,WAAW;IAChC,OAAO,OAAO,IAAI;AACpB;AAIO,eAAe,wBAAwB,IAAY,EAAE,SAAkB;IAC5E,IAAI;QACF,MAAM,aAAa,MAAM;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,MACrB,IAAI,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MAC3C,CAAC,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS;IAEvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 2785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/index.ts"], "sourcesContent": ["// 数据库操作统一入口\n// 为了保持兼容性，重新导出 API 客户端函数\n\n// 分类相关\nexport {\n  getCategories,\n  getCategoryById,\n  createCategory,\n  updateCategory,\n  deleteCategory,\n  updateCategoriesOrder\n} from '@/lib/api/client'\n\n// 提示词相关\nexport {\n  getPrompts,\n  getPromptById,\n  createPrompt,\n  updatePrompt,\n  deletePrompt,\n  incrementUsageCount as incrementPromptUsage\n} from '@/lib/api/client'\n\n// 标签相关\nexport {\n  getTags,\n  getTagById,\n  createTag,\n  updateTag,\n  deleteTag,\n  createTags\n} from '@/lib/api/client'\n\n// 应用偏好设置相关\nexport {\n  getAppPreferences,\n  updateAppPreferences\n} from '@/lib/api/client'\n\n// 兼容性函数 - 这些函数暂时返回空值或默认值，避免编译错误\nexport const checkCategoryNameExists = async (name: string) => false\nexport const getCategoryPromptCount = async (id: string) => 0\nexport const checkTagNameExists = async (name: string) => false\nexport const getTagUsageCount = async (id: string) => 0\nexport const getPopularTags = async () => []\nexport const searchTags = async (query: string) => []\nexport const getPromptTags = async (id: string) => []\nexport const addTagToPrompt = async (promptId: string, tagId: string) => {}\nexport const removeTagFromPrompt = async (promptId: string, tagId: string) => {}\nexport const importPrompts = async (data: any[]) => []\nexport const getPopularPrompts = async () => []\nexport const getSearchHistory = async () => []\nexport const addSearchHistory = async (term: string) => {}\nexport const clearSearchHistory = async () => {}\nexport const deleteSearchHistoryItem = async (id: string) => {}\nexport const getPopularSearchTerms = async () => []\nexport const getSearchSuggestions = async (query: string) => []\nexport const searchPrompts = async (query: string) => ({ data: [], total: 0, hasMore: false, nextOffset: null })\nexport const advancedSearch = async (params: any) => ({ data: [], total: 0, hasMore: false, nextOffset: null })\n\n// 导出类型\nexport type {\n  Database,\n  Category,\n  CategoryInsert,\n  CategoryUpdate,\n  CategoryWithCount,\n  Tag,\n  TagInsert,\n  TagUpdate,\n  Prompt,\n  PromptInsert,\n  PromptUpdate,\n  PromptWithDetails,\n  PromptTag,\n  PromptTagInsert,\n  PromptTagUpdate,\n  SearchHistory,\n  SearchHistoryInsert,\n  SearchHistoryUpdate,\n  AppPreferences,\n  AppPreferencesInsert,\n  AppPreferencesUpdate,\n  SearchParams,\n  PaginatedResponse,\n  DatabaseError\n} from '@/types/database'\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,yBAAyB;AAEzB,OAAO;;;;;;;;;;;;;;;;;;;;;;AACP;;;;;AAoCO,MAAM,0BAA0B,OAAO,OAAiB;AACxD,MAAM,yBAAyB,OAAO,KAAe;AACrD,MAAM,qBAAqB,OAAO,OAAiB;AACnD,MAAM,mBAAmB,OAAO,KAAe;AAC/C,MAAM,iBAAiB,UAAY,EAAE;AACrC,MAAM,aAAa,OAAO,QAAkB,EAAE;AAC9C,MAAM,gBAAgB,OAAO,KAAe,EAAE;AAC9C,MAAM,iBAAiB,OAAO,UAAkB,SAAmB;AACnE,MAAM,sBAAsB,OAAO,UAAkB,SAAmB;AACxE,MAAM,gBAAgB,OAAO,OAAgB,EAAE;AAC/C,MAAM,oBAAoB,UAAY,EAAE;AACxC,MAAM,mBAAmB,UAAY,EAAE;AACvC,MAAM,mBAAmB,OAAO,QAAkB;AAClD,MAAM,qBAAqB,WAAa;AACxC,MAAM,0BAA0B,OAAO,MAAgB;AACvD,MAAM,wBAAwB,UAAY,EAAE;AAC5C,MAAM,uBAAuB,OAAO,QAAkB,EAAE;AACxD,MAAM,gBAAgB,OAAO,QAAkB,CAAC;QAAE,MAAM,EAAE;QAAE,OAAO;QAAG,SAAS;QAAO,YAAY;IAAK,CAAC;AACxG,MAAM,iBAAiB,OAAO,SAAgB,CAAC;QAAE,MAAM,EAAE;QAAE,OAAO;QAAG,SAAS;QAAO,YAAY;IAAK,CAAC", "debugId": null}}, {"offset": {"line": 2859, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/advanced-search-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { getCategories, getTags } from '@/lib/database'\nimport type { Category, Tag } from '@/types/database'\n\ninterface AdvancedSearchParams {\n  query?: string\n  title?: string\n  content?: string\n  categoryId?: string\n  tagIds?: string[]\n  dateFrom?: string\n  dateTo?: string\n  usageCountMin?: number\n  usageCountMax?: number\n  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'\n  sortOrder?: 'asc' | 'desc'\n}\n\ninterface AdvancedSearchModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSearch: (params: AdvancedSearchParams) => void\n  initialParams?: AdvancedSearchParams\n}\n\nexport function AdvancedSearchModal({\n  isOpen,\n  onClose,\n  onSearch,\n  initialParams = {}\n}: AdvancedSearchModalProps) {\n  const [query, setQuery] = useState('')\n  const [title, setTitle] = useState('')\n  const [content, setContent] = useState('')\n  const [selectedCategoryId, setSelectedCategoryId] = useState('')\n  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])\n  const [dateFrom, setDateFrom] = useState('')\n  const [dateTo, setDateTo] = useState('')\n  const [usageCountMin, setUsageCountMin] = useState('')\n  const [usageCountMax, setUsageCountMax] = useState('')\n  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'usage_count' | 'title'>('updated_at')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n\n  const [categories, setCategories] = useState<Category[]>([])\n  const [tags, setTags] = useState<Tag[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n\n  const { toast } = useToast()\n\n  // 加载数据\n  useEffect(() => {\n    if (isOpen) {\n      loadData()\n    }\n  }, [isOpen])\n\n  // 填充初始参数 - 使用 JSON.stringify 来比较对象内容而不是引用\n  useEffect(() => {\n    if (initialParams) {\n      setQuery(initialParams.query || '')\n      setTitle(initialParams.title || '')\n      setContent(initialParams.content || '')\n      setSelectedCategoryId(initialParams.categoryId || '')\n      setSelectedTagIds(initialParams.tagIds || [])\n      setDateFrom(initialParams.dateFrom || '')\n      setDateTo(initialParams.dateTo || '')\n      setUsageCountMin(initialParams.usageCountMin?.toString() || '')\n      setUsageCountMax(initialParams.usageCountMax?.toString() || '')\n      setSortBy(initialParams.sortBy || 'updated_at')\n      setSortOrder(initialParams.sortOrder || 'desc')\n    }\n  }, [JSON.stringify(initialParams)]) // 使用 JSON.stringify 避免对象引用问题\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      const [categoriesData, tagsData] = await Promise.all([\n        getCategories(),\n        getTags()\n      ])\n      setCategories(categoriesData)\n      setTags(tagsData)\n    } catch (error) {\n      console.error('加载数据失败:', error)\n      toast({\n        title: \"加载失败\",\n        description: \"无法加载分类和标签数据\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    const params: AdvancedSearchParams = {\n      query: query.trim() || undefined,\n      title: title.trim() || undefined,\n      content: content.trim() || undefined,\n      categoryId: selectedCategoryId || undefined,\n      tagIds: selectedTagIds.length > 0 ? selectedTagIds : undefined,\n      dateFrom: dateFrom || undefined,\n      dateTo: dateTo || undefined,\n      usageCountMin: usageCountMin ? parseInt(usageCountMin) : undefined,\n      usageCountMax: usageCountMax ? parseInt(usageCountMax) : undefined,\n      sortBy,\n      sortOrder\n    }\n\n    onSearch(params)\n    onClose()\n  }\n\n  const handleReset = () => {\n    setQuery('')\n    setTitle('')\n    setContent('')\n    setSelectedCategoryId('')\n    setSelectedTagIds([])\n    setDateFrom('')\n    setDateTo('')\n    setUsageCountMin('')\n    setUsageCountMax('')\n    setSortBy('updated_at')\n    setSortOrder('desc')\n  }\n\n  const toggleTag = (tagId: string) => {\n    setSelectedTagIds(prev => \n      prev.includes(tagId) \n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    )\n  }\n\n  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id))\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <DialogTitle>高级搜索</DialogTitle>\n          <DialogDescription>\n            使用多个条件精确搜索提示词\n          </DialogDescription>\n        </DialogHeader>\n\n        {isLoading ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <Icon name=\"spinner\" className=\"h-6 w-6 animate-spin mr-2\" />\n            <span>加载中...</span>\n          </div>\n        ) : (\n          <form onSubmit={handleSubmit} className=\"flex-1 overflow-hidden flex flex-col\">\n            <div className=\"flex-1 overflow-y-auto space-y-4\">\n              {/* 通用搜索 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"query\">关键词搜索</Label>\n                <Input\n                  id=\"query\"\n                  value={query}\n                  onChange={(e) => setQuery(e.target.value)}\n                  placeholder=\"在标题、描述、内容中搜索\"\n                />\n              </div>\n\n              {/* 标题搜索 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"title\">标题</Label>\n                <Input\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"搜索标题\"\n                />\n              </div>\n\n              {/* 内容搜索 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"content\">内容</Label>\n                <Input\n                  id=\"content\"\n                  value={content}\n                  onChange={(e) => setContent(e.target.value)}\n                  placeholder=\"搜索内容\"\n                />\n              </div>\n\n              {/* 分类筛选 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"category\">分类</Label>\n                <select\n                  id=\"category\"\n                  value={selectedCategoryId}\n                  onChange={(e) => setSelectedCategoryId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\n                >\n                  <option value=\"\">所有分类</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 标签筛选 */}\n              <div className=\"space-y-2\">\n                <Label>标签</Label>\n                \n                {/* 已选标签 */}\n                {selectedTags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mb-2\">\n                    {selectedTags.map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"secondary\"\n                        className=\"cursor-pointer\"\n                        style={{ \n                          backgroundColor: `${tag.color}20`,\n                          color: tag.color \n                        }}\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        {tag.name}\n                        <Icon name=\"times\" className=\"h-3 w-3 ml-1\" />\n                      </Badge>\n                    ))}\n                  </div>\n                )}\n\n                {/* 可选标签 */}\n                <div className=\"flex flex-wrap gap-2 max-h-32 overflow-y-auto\">\n                  {tags\n                    .filter(tag => !selectedTagIds.includes(tag.id))\n                    .map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"outline\"\n                        className=\"cursor-pointer hover:bg-gray-100\"\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        <Icon name=\"plus\" className=\"h-3 w-3 mr-1\" />\n                        {tag.name}\n                      </Badge>\n                    ))}\n                </div>\n              </div>\n\n              {/* 日期范围 */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"dateFrom\">创建日期从</Label>\n                  <Input\n                    id=\"dateFrom\"\n                    type=\"date\"\n                    value={dateFrom}\n                    onChange={(e) => setDateFrom(e.target.value)}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"dateTo\">创建日期到</Label>\n                  <Input\n                    id=\"dateTo\"\n                    type=\"date\"\n                    value={dateTo}\n                    onChange={(e) => setDateTo(e.target.value)}\n                  />\n                </div>\n              </div>\n\n              {/* 使用次数范围 */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"usageCountMin\">最少使用次数</Label>\n                  <Input\n                    id=\"usageCountMin\"\n                    type=\"number\"\n                    min=\"0\"\n                    value={usageCountMin}\n                    onChange={(e) => setUsageCountMin(e.target.value)}\n                    placeholder=\"0\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"usageCountMax\">最多使用次数</Label>\n                  <Input\n                    id=\"usageCountMax\"\n                    type=\"number\"\n                    min=\"0\"\n                    value={usageCountMax}\n                    onChange={(e) => setUsageCountMax(e.target.value)}\n                    placeholder=\"无限制\"\n                  />\n                </div>\n              </div>\n\n              {/* 排序 */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"sortBy\">排序字段</Label>\n                  <select\n                    id=\"sortBy\"\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as any)}\n                    className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\n                  >\n                    <option value=\"updated_at\">更新时间</option>\n                    <option value=\"created_at\">创建时间</option>\n                    <option value=\"usage_count\">使用次数</option>\n                    <option value=\"title\">标题</option>\n                  </select>\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"sortOrder\">排序方向</Label>\n                  <select\n                    id=\"sortOrder\"\n                    value={sortOrder}\n                    onChange={(e) => setSortOrder(e.target.value as any)}\n                    className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\n                  >\n                    <option value=\"desc\">降序</option>\n                    <option value=\"asc\">升序</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* 底部按钮 */}\n            <div className=\"flex items-center justify-between pt-4 border-t\">\n              <Button type=\"button\" variant=\"outline\" onClick={handleReset}>\n                <Icon name=\"refresh\" className=\"h-4 w-4 mr-2\" />\n                重置\n              </Button>\n              \n              <div className=\"flex items-center gap-2\">\n                <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                  取消\n                </Button>\n                <Button type=\"submit\">\n                  <Icon name=\"search\" className=\"h-4 w-4 mr-2\" />\n                  搜索\n                </Button>\n              </div>\n            </div>\n          </form>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAhBA;;;;;;;;;;AAwCO,SAAS,oBAAoB,KAKT;QALS,EAClC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,gBAAgB,CAAC,CAAC,EACO,GALS;;IAMlC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyD;IAC5F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;wCAAG;QAAC;KAAO;IAEX,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,eAAe;oBAQA,8BACA;gBARjB,SAAS,cAAc,KAAK,IAAI;gBAChC,SAAS,cAAc,KAAK,IAAI;gBAChC,WAAW,cAAc,OAAO,IAAI;gBACpC,sBAAsB,cAAc,UAAU,IAAI;gBAClD,kBAAkB,cAAc,MAAM,IAAI,EAAE;gBAC5C,YAAY,cAAc,QAAQ,IAAI;gBACtC,UAAU,cAAc,MAAM,IAAI;gBAClC,iBAAiB,EAAA,+BAAA,cAAc,aAAa,cAA3B,mDAAA,6BAA6B,QAAQ,OAAM;gBAC5D,iBAAiB,EAAA,+BAAA,cAAc,aAAa,cAA3B,mDAAA,6BAA6B,QAAQ,OAAM;gBAC5D,UAAU,cAAc,MAAM,IAAI;gBAClC,aAAa,cAAc,SAAS,IAAI;YAC1C;QACF;wCAAG;QAAC,KAAK,SAAS,CAAC;KAAe,GAAE,6BAA6B;IAEjE,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,MAAM,CAAC,gBAAgB,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD;gBACZ,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;aACP;YACD,cAAc;YACd,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,MAAM,SAA+B;YACnC,OAAO,MAAM,IAAI,MAAM;YACvB,OAAO,MAAM,IAAI,MAAM;YACvB,SAAS,QAAQ,IAAI,MAAM;YAC3B,YAAY,sBAAsB;YAClC,QAAQ,eAAe,MAAM,GAAG,IAAI,iBAAiB;YACrD,UAAU,YAAY;YACtB,QAAQ,UAAU;YAClB,eAAe,gBAAgB,SAAS,iBAAiB;YACzD,eAAe,gBAAgB,SAAS,iBAAiB;YACzD;YACA;QACF;QAEA,SAAS;QACT;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,SAAS;QACT,WAAW;QACX,sBAAsB;QACtB,kBAAkB,EAAE;QACpB,YAAY;QACZ,UAAU;QACV,iBAAiB;QACjB,iBAAiB;QACjB,UAAU;QACV,aAAa;IACf;IAEA,MAAM,YAAY,CAAC;QACjB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAAO,eAAe,QAAQ,CAAC,IAAI,EAAE;IAEtE,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;gBAKpB,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAU,WAAU;;;;;;sCAC/B,6LAAC;sCAAK;;;;;;;;;;;yCAGR,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACrD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAQ9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;sDAAC;;;;;;wCAGN,aAAa,MAAM,GAAG,mBACrB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wDAC9B,OAAO,IAAI,KAAK;oDAClB;oDACA,SAAS,IAAM,UAAU,IAAI,EAAE;;wDAE9B,IAAI,IAAI;sEACT,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAQ,WAAU;;;;;;;mDAVxB,IAAI,EAAE;;;;;;;;;;sDAiBnB,6LAAC;4CAAI,WAAU;sDACZ,KACE,MAAM,CAAC,CAAA,MAAO,CAAC,eAAe,QAAQ,CAAC,IAAI,EAAE,GAC7C,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,UAAU,IAAI,EAAE;;sEAE/B,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAO,WAAU;;;;;;wDAC3B,IAAI,IAAI;;mDANJ,IAAI,EAAE;;;;;;;;;;;;;;;;8CAarB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAG/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAM/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,aAAY;;;;;;;;;;;;;;;;;;8CAMlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC;oDACC,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAG1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC;oDACC,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAU,WAAU;;;;;;wCAAiB;;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;4CAAU,SAAS;sDAAS;;;;;;sDAG1D,6LAAC,8HAAA,CAAA,SAAM;4CAAC,MAAK;;8DACX,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAS,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE;GAvUgB;;QAsBI,wHAAA,CAAA,WAAQ;;;KAtBZ", "debugId": null}}, {"offset": {"line": 3602, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/search-results.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { PromptCard } from '@/components/prompt-card'\nimport { AdvancedSearchModal } from '@/components/advanced-search-modal'\nimport type { PromptWithDetails } from '@/types/database'\n\ninterface SearchResultsProps {\n  results: PromptWithDetails[]\n  searchQuery: string\n  total: number\n  isLoading: boolean\n  hasMore: boolean\n  onLoadMore: () => void\n  onPromptView: (promptId: string) => void\n  onPromptEdit: (promptId: string) => void\n  onPromptDelete: (promptId: string) => void\n  onPromptCopy: (content: string, promptId: string) => void\n  onAdvancedSearch: (params: any) => void\n  onClearSearch: () => void\n}\n\nexport function SearchResults({\n  results,\n  searchQuery,\n  total,\n  isLoading,\n  hasMore,\n  onLoadMore,\n  onPromptView,\n  onPromptEdit,\n  onPromptDelete,\n  onPromptCopy,\n  onAdvancedSearch,\n  onClearSearch\n}: SearchResultsProps) {\n  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false)\n\n  const handleAdvancedSearch = (params: any) => {\n    onAdvancedSearch(params)\n    setIsAdvancedSearchOpen(false)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索结果头部 */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-4\">\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            搜索结果\n          </h2>\n          <p className=\"text-sm text-muted-foreground mt-1\">\n            {searchQuery ? (\n              <>\n                找到 <span className=\"font-medium\">{total}</span> 个包含 \"\n                <span className=\"font-medium text-blue-600\">{searchQuery}</span>\" 的提示词\n              </>\n            ) : (\n              <>共 <span className=\"font-medium\">{total}</span> 个提示词</>\n            )}\n          </p>\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setIsAdvancedSearchOpen(true)}\n          >\n            <Icon name=\"filter\" className=\"h-4 w-4 sm:mr-2\" />\n            <span className=\"hidden sm:inline\">高级搜索</span>\n          </Button>\n\n          {searchQuery && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={onClearSearch}\n            >\n              <Icon name=\"times\" className=\"h-4 w-4 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">清除搜索</span>\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* 搜索建议 */}\n      {searchQuery && results.length === 0 && !isLoading && (\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Icon name=\"lightbulb\" className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n            <div>\n              <h3 className=\"font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                搜索建议\n              </h3>\n              <ul className=\"text-sm text-yellow-700 dark:text-yellow-300 space-y-1\">\n                <li>• 检查拼写是否正确</li>\n                <li>• 尝试使用更通用的关键词</li>\n                <li>• 使用高级搜索进行更精确的筛选</li>\n                <li>• 检查是否选择了正确的分类</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 搜索结果列表 */}\n      {results.length > 0 ? (\n        <>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6\">\n            {results.map((prompt) => (\n              <PromptCard\n                key={prompt.id}\n                id={prompt.id}\n                title={prompt.title}\n                description={prompt.description || undefined}\n                content={prompt.content}\n                category={prompt.category}\n                tags={prompt.tags}\n                usageCount={prompt.usageCount}\n                createdAt={prompt.createdAt}\n                updatedAt={prompt.updatedAt}\n                onView={onPromptView}\n                onEdit={onPromptEdit}\n                onDelete={onPromptDelete}\n                onCopy={onPromptCopy}\n                className=\"search-result-card\"\n              />\n            ))}\n          </div>\n\n          {/* 加载更多 */}\n          {hasMore && (\n            <div className=\"text-center\">\n              <Button\n                variant=\"outline\"\n                onClick={onLoadMore}\n                disabled={isLoading}\n              >\n                {isLoading ? (\n                  <>\n                    <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                    加载中...\n                  </>\n                ) : (\n                  <>\n                    <Icon name=\"refresh\" className=\"h-4 w-4 mr-2\" />\n                    加载更多\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </>\n      ) : !isLoading ? (\n        <div className=\"text-center py-12\">\n          <Icon name=\"search\" className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n            {searchQuery ? '未找到匹配的提示词' : '暂无提示词'}\n          </h3>\n          <p className=\"text-muted-foreground mb-4\">\n            {searchQuery \n              ? '尝试调整搜索关键词或使用高级搜索'\n              : '开始创建您的第一个提示词吧'\n            }\n          </p>\n          <div className=\"flex items-center justify-center gap-2\">\n            {searchQuery ? (\n              <>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setIsAdvancedSearchOpen(true)}\n                >\n                  <Icon name=\"filter\" className=\"h-4 w-4 mr-2\" />\n                  高级搜索\n                </Button>\n                <Button onClick={onClearSearch}>\n                  <Icon name=\"times\" className=\"h-4 w-4 mr-2\" />\n                  清除搜索\n                </Button>\n              </>\n            ) : (\n              <Button onClick={() => {/* TODO: 新建提示词 */}}>\n                <Icon name=\"plus\" className=\"h-4 w-4 mr-2\" />\n                新建提示词\n              </Button>\n            )}\n          </div>\n        </div>\n      ) : null}\n\n      {/* 加载状态 */}\n      {isLoading && results.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Icon name=\"spinner\" className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-muted-foreground\">搜索中...</p>\n        </div>\n      )}\n\n      {/* 高级搜索模态框 */}\n      <AdvancedSearchModal\n        isOpen={isAdvancedSearchOpen}\n        onClose={() => setIsAdvancedSearchOpen(false)}\n        onSearch={handleAdvancedSearch}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AAyBO,SAAS,cAAc,KAaT;QAbS,EAC5B,OAAO,EACP,WAAW,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACM,GAbS;;IAc5B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;QACjB,wBAAwB;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;0CACV,4BACC;;wCAAE;sDACG,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAa;sDAC/C,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAAmB;;iEAGlE;;wCAAE;sDAAE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAa;;;;;;;;;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,wBAAwB;;kDAEvC,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAS,WAAU;;;;;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;4BAGpC,6BACC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;;kDAET,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAQ,WAAU;;;;;;kDAC7B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;YAO1C,eAAe,QAAQ,MAAM,KAAK,KAAK,CAAC,2BACvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAY,WAAU;;;;;;sCACjC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQb,QAAQ,MAAM,GAAG,kBAChB;;kCACE,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,gIAAA,CAAA,aAAU;gCAET,IAAI,OAAO,EAAE;gCACb,OAAO,OAAO,KAAK;gCACnB,aAAa,OAAO,WAAW,IAAI;gCACnC,SAAS,OAAO,OAAO;gCACvB,UAAU,OAAO,QAAQ;gCACzB,MAAM,OAAO,IAAI;gCACjB,YAAY,OAAO,UAAU;gCAC7B,WAAW,OAAO,SAAS;gCAC3B,WAAW,OAAO,SAAS;gCAC3B,QAAQ;gCACR,QAAQ;gCACR,UAAU;gCACV,QAAQ;gCACR,WAAU;+BAdL,OAAO,EAAE;;;;;;;;;;oBAoBnB,yBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCAET,0BACC;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAU,WAAU;;;;;;oCAA8B;;6DAI/D;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAU,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;+BAQ1D,CAAC,0BACH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;wBAAC,MAAK;wBAAS,WAAU;;;;;;kCAC9B,6LAAC;wBAAG,WAAU;kCACX,cAAc,cAAc;;;;;;kCAE/B,6LAAC;wBAAE,WAAU;kCACV,cACG,qBACA;;;;;;kCAGN,6LAAC;wBAAI,WAAU;kCACZ,4BACC;;8CACE,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,wBAAwB;;sDAEvC,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAS,WAAU;;;;;;wCAAiB;;;;;;;8CAGjD,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;wCAAiB;;;;;;;;yDAKlD,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS,KAAwB;;8CACvC,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAO,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;uBAMnD;YAGH,aAAa,QAAQ,MAAM,KAAK,mBAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;wBAAC,MAAK;wBAAU,WAAU;;;;;;kCAC/B,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKzC,6LAAC,6IAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,UAAU;;;;;;;;;;;;AAIlB;GAzLgB;KAAA", "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/utils/clipboard.ts"], "sourcesContent": ["/**\n * 剪贴板操作工具函数\n */\n\n/**\n * 复制文本到剪贴板\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    // 优先使用现代 Clipboard API\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text)\n      return true\n    }\n\n    // 降级到传统方法\n    return fallbackCopyToClipboard(text)\n  } catch (error) {\n    console.error('复制到剪贴板失败:', error)\n    return fallbackCopyToClipboard(text)\n  }\n}\n\n/**\n * 降级复制方法（兼容旧浏览器）\n */\nfunction fallbackCopyToClipboard(text: string): boolean {\n  try {\n    // 创建临时文本区域\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    textArea.style.position = 'fixed'\n    textArea.style.left = '-999999px'\n    textArea.style.top = '-999999px'\n    document.body.appendChild(textArea)\n    \n    // 选择并复制\n    textArea.focus()\n    textArea.select()\n    const successful = document.execCommand('copy')\n    \n    // 清理\n    document.body.removeChild(textArea)\n    \n    return successful\n  } catch (error) {\n    console.error('降级复制方法失败:', error)\n    return false\n  }\n}\n\n/**\n * 检查是否支持剪贴板操作\n */\nexport function isClipboardSupported(): boolean {\n  return !!(navigator.clipboard || document.execCommand)\n}\n\n/**\n * 从剪贴板读取文本\n */\nexport async function readFromClipboard(): Promise<string | null> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      const text = await navigator.clipboard.readText()\n      return text\n    }\n    \n    // 传统方法无法读取剪贴板内容\n    console.warn('当前环境不支持读取剪贴板内容')\n    return null\n  } catch (error) {\n    console.error('读取剪贴板失败:', error)\n    return null\n  }\n}\n\n/**\n * 复制文本并显示反馈\n */\nexport async function copyWithFeedback(\n  text: string,\n  onSuccess?: () => void,\n  onError?: (error: string) => void\n): Promise<boolean> {\n  const success = await copyToClipboard(text)\n  \n  if (success) {\n    onSuccess?.()\n  } else {\n    onError?.('复制失败，请手动复制')\n  }\n  \n  return success\n}\n\n/**\n * 格式化文本用于复制\n */\nexport function formatTextForCopy(text: string): string {\n  // 移除多余的空白字符\n  return text\n    .replace(/\\r\\n/g, '\\n') // 统一换行符\n    .replace(/\\t/g, '  ') // 制表符转换为空格\n    .trim() // 移除首尾空白\n}\n\n/**\n * 复制 JSON 数据\n */\nexport async function copyJSON(data: any, pretty: boolean = true): Promise<boolean> {\n  try {\n    const jsonString = pretty \n      ? JSON.stringify(data, null, 2)\n      : JSON.stringify(data)\n    \n    return await copyToClipboard(jsonString)\n  } catch (error) {\n    console.error('复制JSON失败:', error)\n    return false\n  }\n}\n\n/**\n * 复制 Markdown 格式文本\n */\nexport async function copyAsMarkdown(title: string, content: string): Promise<boolean> {\n  const markdown = `# ${title}\\n\\n${content}`\n  return await copyToClipboard(markdown)\n}\n\n/**\n * 复制为代码块格式\n */\nexport async function copyAsCodeBlock(content: string, language: string = ''): Promise<boolean> {\n  const codeBlock = `\\`\\`\\`${language}\\n${content}\\n\\`\\`\\``\n  return await copyToClipboard(codeBlock)\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;;;AACM,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,uBAAuB;QACvB,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT;QAEA,UAAU;QACV,OAAO,wBAAwB;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,wBAAwB;IACjC;AACF;AAEA;;CAEC,GACD,SAAS,wBAAwB,IAAY;IAC3C,IAAI;QACF,WAAW;QACX,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;QACtB,SAAS,KAAK,CAAC,GAAG,GAAG;QACrB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,QAAQ;QACR,SAAS,KAAK;QACd,SAAS,MAAM;QACf,MAAM,aAAa,SAAS,WAAW,CAAC;QAExC,KAAK;QACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,SAAS;IACd,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS,IAAI,SAAS,WAAW;AACvD;AAKO,eAAe;IACpB,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,OAAO,MAAM,UAAU,SAAS,CAAC,QAAQ;YAC/C,OAAO;QACT;QAEA,gBAAgB;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;IACT;AACF;AAKO,eAAe,iBACpB,IAAY,EACZ,SAAsB,EACtB,OAAiC;IAEjC,MAAM,UAAU,MAAM,gBAAgB;IAEtC,IAAI,SAAS;QACX,sBAAA,gCAAA;IACF,OAAO;QACL,oBAAA,8BAAA,QAAU;IACZ;IAEA,OAAO;AACT;AAKO,SAAS,kBAAkB,IAAY;IAC5C,YAAY;IACZ,OAAO,KACJ,OAAO,CAAC,SAAS,MAAM,QAAQ;KAC/B,OAAO,CAAC,OAAO,MAAM,WAAW;KAChC,IAAI,GAAG,SAAS;;AACrB;AAKO,eAAe,SAAS,IAAS;QAAE,SAAA,iEAAkB;IAC1D,IAAI;QACF,MAAM,aAAa,SACf,KAAK,SAAS,CAAC,MAAM,MAAM,KAC3B,KAAK,SAAS,CAAC;QAEnB,OAAO,MAAM,gBAAgB;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,eAAe,eAAe,KAAa,EAAE,OAAe;IACjE,MAAM,WAAW,AAAC,KAAgB,OAAZ,OAAM,QAAc,OAAR;IAClC,OAAO,MAAM,gBAAgB;AAC/B;AAKO,eAAe,gBAAgB,OAAe;QAAE,WAAA,iEAAmB;IACxE,MAAM,YAAY,AAAC,MAAqB,OAAb,UAAS,MAAY,OAAR,SAAQ;IAChD,OAAO,MAAM,gBAAgB;AAC/B", "debugId": null}}, {"offset": {"line": 4173, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/utils/format.ts"], "sourcesContent": ["/**\n * 格式化工具函数\n */\n\n/**\n * 格式化日期\n */\nexport function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {\n  const date = new Date(dateString)\n  \n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    ...options\n  }\n  \n  return date.toLocaleDateString('zh-CN', defaultOptions)\n}\n\n/**\n * 格式化相对时间\n */\nexport function formatRelativeTime(dateString: string): string {\n  const date = new Date(dateString)\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}分钟前`\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}小时前`\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}天前`\n  }\n  \n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks}周前`\n  }\n  \n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths}个月前`\n  }\n  \n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears}年前`\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 B'\n  \n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 格式化数字（添加千分位分隔符）\n */\nexport function formatNumber(num: number): string {\n  // 处理 NaN、undefined、null 等异常值\n  if (typeof num !== 'number' || isNaN(num) || !isFinite(num)) {\n    return '0'\n  }\n  return num.toLocaleString('zh-CN')\n}\n\n/**\n * 截断文本\n */\nexport function truncateText(text: string, maxLength: number, suffix: string = '...'): string {\n  if (text.length <= maxLength) {\n    return text\n  }\n  \n  return text.slice(0, maxLength - suffix.length) + suffix\n}\n\n/**\n * 高亮搜索关键词\n */\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\n  if (!searchTerm.trim()) {\n    return text\n  }\n  \n  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi')\n  return text.replace(regex, '<mark>$1</mark>')\n}\n\n/**\n * 转义正则表达式特殊字符\n */\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')\n}\n\n/**\n * 格式化标签列表\n */\nexport function formatTagList(tags: string[], maxDisplay: number = 3): string {\n  if (tags.length === 0) {\n    return '无标签'\n  }\n  \n  if (tags.length <= maxDisplay) {\n    return tags.join(', ')\n  }\n  \n  const displayTags = tags.slice(0, maxDisplay)\n  const remainingCount = tags.length - maxDisplay\n  \n  return `${displayTags.join(', ')} +${remainingCount}`\n}\n\n/**\n * 格式化使用次数\n */\nexport function formatUsageCount(count: number): string {\n  // 处理 NaN、undefined、null 等异常值\n  if (typeof count !== 'number' || isNaN(count) || !isFinite(count) || count < 0) {\n    return '未使用'\n  }\n\n  if (count === 0) {\n    return '未使用'\n  }\n\n  if (count === 1) {\n    return '使用1次'\n  }\n\n  if (count < 1000) {\n    return `使用${count}次`\n  }\n\n  if (count < 1000000) {\n    const k = Math.floor(count / 100) / 10\n    return `使用${k}k次`\n  }\n\n  const m = Math.floor(count / 100000) / 10\n  return `使用${m}m次`\n}\n\n/**\n * 格式化颜色值\n */\nexport function formatColor(color: string): string {\n  // 确保颜色值以 # 开头\n  if (!color.startsWith('#')) {\n    return `#${color}`\n  }\n  \n  return color.toLowerCase()\n}\n\n/**\n * 生成随机颜色\n */\nexport function generateRandomColor(): string {\n  const colors = [\n    '#ef4444', // red\n    '#f97316', // orange\n    '#f59e0b', // amber\n    '#eab308', // yellow\n    '#84cc16', // lime\n    '#22c55e', // green\n    '#10b981', // emerald\n    '#14b8a6', // teal\n    '#06b6d4', // cyan\n    '#0ea5e9', // sky\n    '#3b82f6', // blue\n    '#6366f1', // indigo\n    '#8b5cf6', // violet\n    '#a855f7', // purple\n    '#d946ef', // fuchsia\n    '#ec4899', // pink\n    '#f43f5e', // rose\n  ]\n  \n  return colors[Math.floor(Math.random() * colors.length)]\n}\n\n/**\n * 验证颜色值\n */\nexport function isValidColor(color: string): boolean {\n  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/\n  return hexColorRegex.test(color)\n}\n\n/**\n * 格式化搜索查询\n */\nexport function formatSearchQuery(query: string): string {\n  return query\n    .trim()\n    .replace(/\\s+/g, ' ') // 合并多个空格\n    .toLowerCase()\n}\n\n/**\n * 提取文本摘要\n */\nexport function extractSummary(text: string, maxLength: number = 150): string {\n  // 移除 Markdown 语法\n  const cleanText = text\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/\\n+/g, ' ') // 换行符转空格\n    .trim()\n  \n  return truncateText(cleanText, maxLength)\n}\n\n/**\n * 格式化 JSON 数据\n */\nexport function formatJSON(data: any, pretty: boolean = true): string {\n  try {\n    return pretty \n      ? JSON.stringify(data, null, 2)\n      : JSON.stringify(data)\n  } catch (error) {\n    return String(data)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;;;;;;;;;AACM,SAAS,WAAW,UAAkB,EAAE,OAAoC;IACjF,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,GAAG,OAAO;IACZ;IAEA,OAAO,KAAK,kBAAkB,CAAC,SAAS;AAC1C;AAKO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,AAAC,GAAgB,OAAd,eAAc;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,AAAC,GAAe,OAAb,cAAa;IACzB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,AAAC,GAAc,OAAZ,aAAY;AACxB;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAK;QAAM;QAAM;QAAM;KAAK;IAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,aAAa,GAAW;IACtC,6BAA6B;IAC7B,IAAI,OAAO,QAAQ,YAAY,MAAM,QAAQ,CAAC,SAAS,MAAM;QAC3D,OAAO;IACT;IACA,OAAO,IAAI,cAAc,CAAC;AAC5B;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;QAAE,SAAA,iEAAiB;IAC7E,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,OAAO;IACT;IAEA,OAAO,KAAK,KAAK,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACpD;AAKO,SAAS,oBAAoB,IAAY,EAAE,UAAkB;IAClE,IAAI,CAAC,WAAW,IAAI,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,QAAQ,IAAI,OAAO,AAAC,IAA4B,OAAzB,aAAa,aAAY,MAAI;IAC1D,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAEA;;CAEC,GACD,SAAS,aAAa,MAAc;IAClC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C;AAKO,SAAS,cAAc,IAAc;QAAE,aAAA,iEAAqB;IACjE,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,KAAK,MAAM,IAAI,YAAY;QAC7B,OAAO,KAAK,IAAI,CAAC;IACnB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,GAAG;IAClC,MAAM,iBAAiB,KAAK,MAAM,GAAG;IAErC,OAAO,AAAC,GAA6B,OAA3B,YAAY,IAAI,CAAC,OAAM,MAAmB,OAAf;AACvC;AAKO,SAAS,iBAAiB,KAAa;IAC5C,6BAA6B;IAC7B,IAAI,OAAO,UAAU,YAAY,MAAM,UAAU,CAAC,SAAS,UAAU,QAAQ,GAAG;QAC9E,OAAO;IACT;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,IAAI,QAAQ,MAAM;QAChB,OAAO,AAAC,KAAU,OAAN,OAAM;IACpB;IAEA,IAAI,QAAQ,SAAS;QACnB,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,OAAO;QACpC,OAAO,AAAC,KAAM,OAAF,GAAE;IAChB;IAEA,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,UAAU;IACvC,OAAO,AAAC,KAAM,OAAF,GAAE;AAChB;AAKO,SAAS,YAAY,KAAa;IACvC,cAAc;IACd,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM;QAC1B,OAAO,AAAC,IAAS,OAAN;IACb;IAEA,OAAO,MAAM,WAAW;AAC1B;AAKO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAKO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,WAAW;AAChB;AAKO,SAAS,eAAe,IAAY;QAAE,YAAA,iEAAoB;IAC/D,iBAAiB;IACjB,MAAM,YAAY,KACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,IAAI;IAEP,OAAO,aAAa,WAAW;AACjC;AAKO,SAAS,WAAW,IAAS;QAAE,SAAA,iEAAkB;IACtD,IAAI;QACF,OAAO,SACH,KAAK,SAAS,CAAC,MAAM,MAAM,KAC3B,KAAK,SAAS,CAAC;IACrB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 4371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/prompt-detail-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { MarkdownPreview } from '@/components/ui/markdown-preview'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { copyToClipboard } from '@/lib/utils/clipboard'\nimport { formatDate, formatRelativeTime } from '@/lib/utils/format'\nimport type { PromptWithDetails } from '@/types/database'\n\ninterface PromptDetailModalProps {\n  prompt: PromptWithDetails | null\n  isOpen: boolean\n  onClose: () => void\n  onEdit: (promptId: string) => void\n  onDelete: (promptId: string) => void\n  onCopy: (content: string, promptId: string) => void\n}\n\nexport function PromptDetailModal({\n  prompt,\n  isOpen,\n  onClose,\n  onEdit,\n  onDelete,\n  onCopy\n}: PromptDetailModalProps) {\n  const [isMarkdownView, setIsMarkdownView] = useState(true)\n  const { toast } = useToast()\n\n  if (!prompt) return null\n\n  const handleCopy = async () => {\n    const success = await copyToClipboard(prompt.content)\n    if (success) {\n      onCopy(prompt.content, prompt.id)\n      toast({\n        title: \"复制成功\",\n        description: \"提示词已复制到剪贴板\",\n      })\n    } else {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板，请手动复制\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handleEdit = () => {\n    onEdit(prompt.id)\n    onClose()\n  }\n\n  const handleDelete = () => {\n    onDelete(prompt.id)\n    onClose()\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <div className=\"flex items-start justify-between pr-8\">\n            <div className=\"flex-1 min-w-0\">\n              <DialogTitle className=\"text-xl font-semibold mb-2\">\n                {prompt.title}\n              </DialogTitle>\n              {prompt.description && (\n                <DialogDescription className=\"text-base\">\n                  {prompt.description}\n                </DialogDescription>\n              )}\n            </div>\n\n            {/* 操作按钮 - 添加右边距避免与关闭按钮重叠 */}\n            <div className=\"flex items-center gap-2 ml-4 flex-wrap\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleCopy}\n                className=\"flex-shrink-0\"\n              >\n                <Icon name=\"copy\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">复制</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleEdit}\n                className=\"flex-shrink-0\"\n              >\n                <Icon name=\"edit\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">编辑</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleDelete}\n                className=\"text-red-600 hover:text-red-700 flex-shrink-0\"\n              >\n                <Icon name=\"trash\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">删除</span>\n              </Button>\n            </div>\n          </div>\n\n          {/* 元信息 */}\n          <div className=\"flex flex-wrap items-center gap-4 pt-4 border-t\">\n            {/* 分类 */}\n            {prompt.category && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"folder\" className=\"h-4 w-4 text-muted-foreground\" />\n                <Badge\n                  variant=\"secondary\"\n                  style={{ \n                    backgroundColor: `${prompt.category.color}20`,\n                    color: prompt.category.color \n                  }}\n                >\n                  <Icon name={prompt.category.icon as any} className=\"h-3 w-3 mr-1\" />\n                  {prompt.category.name}\n                </Badge>\n              </div>\n            )}\n\n            {/* 标签 */}\n            {prompt.tags && prompt.tags.length > 0 && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"tags\" className=\"h-4 w-4 text-muted-foreground\" />\n                <div className=\"flex flex-wrap gap-1\">\n                  {prompt.tags.map((tag) => (\n                    <Badge\n                      key={tag.id}\n                      variant=\"outline\"\n                      className=\"text-xs\"\n                      style={{ \n                        backgroundColor: `${tag.color}20`,\n                        color: tag.color,\n                        borderColor: `${tag.color}40`\n                      }}\n                    >\n                      {tag.name}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* 使用次数 */}\n            <div className=\"flex items-center gap-2\">\n              <Icon name=\"eye\" className=\"h-4 w-4 text-muted-foreground\" />\n              <span className=\"text-sm text-muted-foreground\">\n                使用 {prompt.usage_count} 次\n              </span>\n            </div>\n\n            {/* 时间信息 */}\n            <div className=\"flex items-center gap-2\">\n              <Icon name=\"clock\" className=\"h-4 w-4 text-muted-foreground\" />\n              <span className=\"text-sm text-muted-foreground\">\n                创建于 {formatDate(prompt.created_at)}\n              </span>\n            </div>\n\n            {prompt.updated_at !== prompt.created_at && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"refresh\" className=\"h-4 w-4 text-muted-foreground\" />\n                <span className=\"text-sm text-muted-foreground\">\n                  更新于 {formatRelativeTime(prompt.updated_at)}\n                </span>\n              </div>\n            )}\n          </div>\n        </DialogHeader>\n\n        {/* 内容区域 */}\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          {/* 视图切换 */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={!isMarkdownView ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setIsMarkdownView(false)}\n                className=\"relative\"\n              >\n                <Icon name=\"file-text\" className=\"h-4 w-4 mr-2\" />\n                原始文本\n              </Button>\n              <Button\n                variant={isMarkdownView ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setIsMarkdownView(true)}\n                className=\"relative\"\n              >\n                <Icon name=\"eye\" className=\"h-4 w-4 mr-2\" />\n                Markdown 预览\n                {isMarkdownView && (\n                  <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse\" />\n                )}\n              </Button>\n\n              {/* Markdown 功能提示 */}\n              <div className=\"flex items-center gap-1 text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md\">\n                <Icon name=\"circle-info\" className=\"h-3 w-3\" />\n                <span>支持 Markdown 格式</span>\n              </div>\n            </div>\n\n            <div className=\"text-sm text-muted-foreground\">\n              {prompt.content.length} 字符\n            </div>\n          </div>\n\n          {/* 内容显示 */}\n          <div className=\"flex-1 overflow-y-auto border rounded-lg p-4 bg-gray-50 dark:bg-gray-900\">\n            {isMarkdownView ? (\n              <div className=\"relative\">\n                {/* Markdown 渲染指示器 */}\n                <div className=\"absolute top-0 right-0 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md\">\n                  <Icon name=\"eye\" className=\"h-3 w-3 inline mr-1\" />\n                  Markdown 渲染\n                </div>\n                <MarkdownPreview\n                  content={prompt.content}\n                  className=\"pt-6\"\n                />\n              </div>\n            ) : (\n              <div className=\"relative\">\n                {/* 原始文本指示器 */}\n                <div className=\"absolute top-0 right-0 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md\">\n                  <Icon name=\"file-text\" className=\"h-3 w-3 inline mr-1\" />\n                  原始文本\n                </div>\n                <pre className=\"whitespace-pre-wrap text-sm font-mono pt-6\">\n                  {prompt.content}\n                </pre>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 底部操作栏 */}\n        <div className=\"flex items-center justify-between pt-4 border-t\">\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // TODO: 实现导出功能\n                console.log('导出提示词')\n              }}\n            >\n              <Icon name=\"download\" className=\"h-4 w-4 mr-2\" />\n              导出\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // TODO: 实现分享功能\n                console.log('分享提示词')\n              }}\n            >\n              <Icon name=\"share\" className=\"h-4 w-4 mr-2\" />\n              分享\n            </Button>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"outline\" onClick={onClose}>\n              关闭\n            </Button>\n            <Button onClick={handleCopy}>\n              <Icon name=\"copy\" className=\"h-4 w-4 mr-2\" />\n              复制内容\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;AA4BO,SAAS,kBAAkB,KAOT;QAPS,EAChC,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,EACiB,GAPS;;IAQhC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;QACpD,IAAI,SAAS;YACX,OAAO,OAAO,OAAO,EAAE,OAAO,EAAE;YAChC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,OAAO;YACL,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,aAAa;QACjB,OAAO,OAAO,EAAE;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,OAAO,EAAE;QAClB;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,OAAO,KAAK;;;;;;wCAEd,OAAO,WAAW,kBACjB,6LAAC,8HAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC1B,OAAO,WAAW;;;;;;;;;;;;8CAMzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAO,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAErC,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAO,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAErC,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAQ,WAAU;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;;gCAEZ,OAAO,QAAQ,kBACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAS,WAAU;;;;;;sDAC9B,6LAAC,6HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,OAAO;gDACL,iBAAiB,AAAC,GAAwB,OAAtB,OAAO,QAAQ,CAAC,KAAK,EAAC;gDAC1C,OAAO,OAAO,QAAQ,CAAC,KAAK;4CAC9B;;8DAEA,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAM,OAAO,QAAQ,CAAC,IAAI;oDAAS,WAAU;;;;;;gDAClD,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;gCAM1B,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wDAC9B,OAAO,IAAI,KAAK;wDAChB,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;oDAC5B;8DAEC,IAAI,IAAI;mDATJ,IAAI,EAAE;;;;;;;;;;;;;;;;8CAiBrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAM,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;;gDAAgC;gDAC1C,OAAO,WAAW;gDAAC;;;;;;;;;;;;;8CAK3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;sDAC7B,6LAAC;4CAAK,WAAU;;gDAAgC;gDACzC,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;gCAIpC,OAAO,UAAU,KAAK,OAAO,UAAU,kBACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAU,WAAU;;;;;;sDAC/B,6LAAC;4CAAK,WAAU;;gDAAgC;gDACzC,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,CAAC,iBAAiB,YAAY;4CACvC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAY,WAAU;;;;;;gDAAiB;;;;;;;sDAGpD,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,YAAY;4CACtC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAM,WAAU;;;;;;gDAAiB;gDAE3C,gCACC,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAc,WAAU;;;;;;8DACnC,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,OAAO,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;sCACZ,+BACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAM,WAAU;;;;;;4CAAwB;;;;;;;kDAGrD,6LAAC,2IAAA,CAAA,kBAAe;wCACd,SAAS,OAAO,OAAO;wCACvB,WAAU;;;;;;;;;;;qDAId,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAY,WAAU;;;;;;4CAAwB;;;;;;;kDAG3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;8BAQzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,eAAe;wCACf,QAAQ,GAAG,CAAC;oCACd;;sDAEA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,WAAU;;;;;;wCAAiB;;;;;;;8CAGnD,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,eAAe;wCACf,QAAQ,GAAG,CAAC;oCACd;;sDAEA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG5C,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAzQgB;;QASI,wHAAA,CAAA,WAAQ;;;KATZ", "debugId": null}}, {"offset": {"line": 5054, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5090, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/prompt-form-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\n// 直接使用 API 客户端\nimport * as api from '@/lib/api/client'\n// 移除旧的数据库导入，直接使用 API\nimport type { \n  PromptWithDetails, \n  Category, \n  Tag,\n  PromptInsert,\n  PromptUpdate \n} from '@/types/database'\n\ninterface PromptFormModalProps {\n  prompt?: PromptWithDetails | null\n  isOpen: boolean\n  onClose: () => void\n  onSuccess: () => void\n}\n\nexport function PromptFormModal({\n  prompt,\n  isOpen,\n  onClose,\n  onSuccess\n}: PromptFormModalProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [content, setContent] = useState('')\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('')\n  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])\n  const [newTagName, setNewTagName] = useState('')\n  const [categories, setCategories] = useState<Category[]>([])\n  const [tags, setTags] = useState<Tag[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [isLoadingData, setIsLoadingData] = useState(false)\n\n  const { toast } = useToast()\n  const isEditing = !!prompt\n\n  // 加载基础数据\n  useEffect(() => {\n    if (isOpen) {\n      loadData()\n    }\n  }, [isOpen])\n\n  // 填充编辑数据\n  useEffect(() => {\n    if (prompt) {\n      setTitle(prompt.title)\n      setDescription(prompt.description || '')\n      setContent(prompt.content)\n      setSelectedCategoryId(prompt.category_id || '')\n      setSelectedTagIds(prompt.tags?.map(tag => tag.id) || [])\n    } else {\n      resetForm()\n    }\n  }, [prompt])\n\n  const loadData = async () => {\n    try {\n      setIsLoadingData(true)\n      const [categoriesData, tagsData] = await Promise.all([\n        api.getCategories(),\n        api.getTags()\n      ])\n      setCategories(categoriesData)\n      setTags(tagsData)\n    } catch (error) {\n      console.error('加载数据失败:', error)\n      toast({\n        title: \"加载失败\",\n        description: \"无法加载分类和标签数据\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoadingData(false)\n    }\n  }\n\n  const resetForm = () => {\n    setTitle('')\n    setDescription('')\n    setContent('')\n    setSelectedCategoryId('')\n    setSelectedTagIds([])\n    setNewTagName('')\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !content.trim()) {\n      toast({\n        title: \"表单验证失败\",\n        description: \"标题和内容不能为空\",\n        variant: \"destructive\",\n      })\n      return\n    }\n\n    try {\n      setIsLoading(true)\n\n      const promptData = {\n        title: title.trim(),\n        description: description.trim() || undefined,\n        content: content.trim(),\n        category_id: selectedCategoryId || undefined,\n      }\n\n      if (isEditing && prompt) {\n        await api.updatePrompt(prompt.id, promptData as PromptUpdate, selectedTagIds)\n        toast({\n          title: \"更新成功\",\n          description: \"提示词已成功更新\",\n        })\n      } else {\n        // 直接调用 API 创建提示词\n        console.log('🚀 直接创建提示词')\n        const newPrompt = await api.createPrompt(promptData as PromptInsert, selectedTagIds)\n\n        // 立即通知父组件更新\n        if (onSuccess) {\n          onSuccess(newPrompt)\n        }\n        toast({\n          title: \"创建成功\",\n          description: \"提示词已成功创建\",\n        })\n      }\n\n      onSuccess()\n      onClose()\n      resetForm()\n    } catch (error) {\n      console.error('保存提示词失败:', error)\n      toast({\n        title: \"保存失败\",\n        description: isEditing ? \"更新提示词时出现错误\" : \"创建提示词时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleAddTag = async () => {\n    if (!newTagName.trim()) return\n\n    try {\n      const newTag = await api.createTag({\n        name: newTagName.trim(),\n        color: '#6366f1' // 默认颜色\n      })\n      \n      setTags(prev => [...prev, newTag])\n      setSelectedTagIds(prev => [...prev, newTag.id])\n      setNewTagName('')\n      \n      toast({\n        title: \"标签创建成功\",\n        description: `标签 \"${newTag.name}\" 已创建并添加`,\n      })\n    } catch (error) {\n      console.error('创建标签失败:', error)\n      toast({\n        title: \"创建标签失败\",\n        description: \"无法创建新标签\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const toggleTag = (tagId: string) => {\n    setSelectedTagIds(prev => \n      prev.includes(tagId) \n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    )\n  }\n\n  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id))\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <DialogTitle>\n            {isEditing ? '编辑提示词' : '创建新提示词'}\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing ? '修改提示词的信息和内容' : '填写提示词的基本信息和内容'}\n          </DialogDescription>\n        </DialogHeader>\n\n        {isLoadingData ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <Icon name=\"spinner\" className=\"h-6 w-6 animate-spin mr-2\" />\n            <span>加载中...</span>\n          </div>\n        ) : (\n          <form onSubmit={handleSubmit} className=\"flex-1 overflow-hidden flex flex-col\">\n            <div className=\"flex-1 overflow-y-auto space-y-4\">\n              {/* 标题 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"title\">标题 *</Label>\n                <Input\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"输入提示词标题\"\n                  required\n                />\n              </div>\n\n              {/* 描述 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">描述</Label>\n                <Input\n                  id=\"description\"\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  placeholder=\"输入提示词的简短描述（可选）\"\n                />\n              </div>\n\n              {/* 分类 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"category\">分类</Label>\n                <select\n                  id=\"category\"\n                  value={selectedCategoryId}\n                  onChange={(e) => setSelectedCategoryId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\n                >\n                  <option value=\"\">选择分类（可选）</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 标签 */}\n              <div className=\"space-y-2\">\n                <Label>标签</Label>\n                \n                {/* 已选标签 */}\n                {selectedTags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mb-2\">\n                    {selectedTags.map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"secondary\"\n                        className=\"cursor-pointer\"\n                        style={{ \n                          backgroundColor: `${tag.color}20`,\n                          color: tag.color \n                        }}\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        {tag.name}\n                        <Icon name=\"times\" className=\"h-3 w-3 ml-1\" />\n                      </Badge>\n                    ))}\n                  </div>\n                )}\n\n                {/* 可选标签 */}\n                <div className=\"flex flex-wrap gap-2 mb-2\">\n                  {tags\n                    .filter(tag => !selectedTagIds.includes(tag.id))\n                    .map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"outline\"\n                        className=\"cursor-pointer hover:bg-gray-100\"\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        <Icon name=\"plus\" className=\"h-3 w-3 mr-1\" />\n                        {tag.name}\n                      </Badge>\n                    ))}\n                </div>\n\n                {/* 新建标签 */}\n                <div className=\"flex gap-2\">\n                  <Input\n                    value={newTagName}\n                    onChange={(e) => setNewTagName(e.target.value)}\n                    placeholder=\"创建新标签\"\n                    className=\"flex-1\"\n                    onKeyPress={(e) => {\n                      if (e.key === 'Enter') {\n                        e.preventDefault()\n                        handleAddTag()\n                      }\n                    }}\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleAddTag}\n                    disabled={!newTagName.trim()}\n                  >\n                    <Icon name=\"plus\" className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              {/* 内容 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"content\">内容 *</Label>\n                <Textarea\n                  id=\"content\"\n                  value={content}\n                  onChange={(e) => setContent(e.target.value)}\n                  placeholder=\"输入提示词内容\"\n                  className=\"min-h-[200px] font-mono\"\n                  required\n                />\n                <div className=\"text-sm text-muted-foreground\">\n                  {content.length} 字符\n                </div>\n              </div>\n            </div>\n\n            {/* 底部按钮 */}\n            <div className=\"flex items-center justify-end gap-2 pt-4 border-t\">\n              <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={isLoading}>\n                {isLoading ? (\n                  <>\n                    <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                    {isEditing ? '更新中...' : '创建中...'}\n                  </>\n                ) : (\n                  <>\n                    <Icon name=\"save\" className=\"h-4 w-4 mr-2\" />\n                    {isEditing ? '更新' : '创建'}\n                  </>\n                )}\n              </Button>\n            </div>\n          </form>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;;AAlBA;;;;;;;;;;;AAmCO,SAAS,gBAAgB,KAKT;QALS,EAC9B,MAAM,EACN,MAAM,EACN,OAAO,EACP,SAAS,EACY,GALS;;IAM9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,CAAC,CAAC;IAEpB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;oBAKQ;gBAJlB,SAAS,OAAO,KAAK;gBACrB,eAAe,OAAO,WAAW,IAAI;gBACrC,WAAW,OAAO,OAAO;gBACzB,sBAAsB,OAAO,WAAW,IAAI;gBAC5C,kBAAkB,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,GAAG;iDAAC,CAAA,MAAO,IAAI,EAAE;oDAAK,EAAE;YACzD,OAAO;gBACL;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,IAAI;YACF,iBAAiB;YACjB,MAAM,CAAC,gBAAgB,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,uHAAA,CAAA,gBAAiB;gBACjB,uHAAA,CAAA,UAAW;aACZ;YACD,cAAc;YACd,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,eAAe;QACf,WAAW;QACX,sBAAsB;QACtB,kBAAkB,EAAE;QACpB,cAAc;IAChB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YACpC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,aAAa;YAEb,MAAM,aAAa;gBACjB,OAAO,MAAM,IAAI;gBACjB,aAAa,YAAY,IAAI,MAAM;gBACnC,SAAS,QAAQ,IAAI;gBACrB,aAAa,sBAAsB;YACrC;YAEA,IAAI,aAAa,QAAQ;gBACvB,MAAM,uHAAA,CAAA,eAAgB,CAAC,OAAO,EAAE,EAAE,YAA4B;gBAC9D,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,iBAAiB;gBACjB,QAAQ,GAAG,CAAC;gBACZ,MAAM,YAAY,MAAM,uHAAA,CAAA,eAAgB,CAAC,YAA4B;gBAErE,YAAY;gBACZ,IAAI,WAAW;oBACb,UAAU;gBACZ;gBACA,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;YAEA;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa,YAAY,eAAe;gBACxC,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,IAAI;YACF,MAAM,SAAS,MAAM,uHAAA,CAAA,YAAa,CAAC;gBACjC,MAAM,WAAW,IAAI;gBACrB,OAAO,UAAU,OAAO;YAC1B;YAEA,QAAQ,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;YACjC,kBAAkB,CAAA,OAAQ;uBAAI;oBAAM,OAAO,EAAE;iBAAC;YAC9C,cAAc;YAEd,MAAM;gBACJ,OAAO;gBACP,aAAa,AAAC,OAAkB,OAAZ,OAAO,IAAI,EAAC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAAO,eAAe,QAAQ,CAAC,IAAI,EAAE;IAEtE,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCACT,YAAY,UAAU;;;;;;sCAEzB,6LAAC,8HAAA,CAAA,oBAAiB;sCACf,YAAY,gBAAgB;;;;;;;;;;;;gBAIhC,8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAU,WAAU;;;;;;sCAC/B,6LAAC;sCAAK;;;;;;;;;;;yCAGR,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACrD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAQ9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;sDAAC;;;;;;wCAGN,aAAa,MAAM,GAAG,mBACrB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wDAC9B,OAAO,IAAI,KAAK;oDAClB;oDACA,SAAS,IAAM,UAAU,IAAI,EAAE;;wDAE9B,IAAI,IAAI;sEACT,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAQ,WAAU;;;;;;;mDAVxB,IAAI,EAAE;;;;;;;;;;sDAiBnB,6LAAC;4CAAI,WAAU;sDACZ,KACE,MAAM,CAAC,CAAA,MAAO,CAAC,eAAe,QAAQ,CAAC,IAAI,EAAE,GAC7C,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,UAAU,IAAI,EAAE;;sEAE/B,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAO,WAAU;;;;;;wDAC3B,IAAI,IAAI;;mDANJ,IAAI,EAAE;;;;;;;;;;sDAYnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;oDACV,YAAY,CAAC;wDACX,IAAI,EAAE,GAAG,KAAK,SAAS;4DACrB,EAAE,cAAc;4DAChB;wDACF;oDACF;;;;;;8DAEF,6LAAC,8HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI;8DAE1B,cAAA,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAK;wDAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG1D,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,0BACC;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAU,WAAU;;;;;;4CAC9B,YAAY,WAAW;;qEAG1B;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAO,WAAU;;;;;;4CAC3B,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC;GA/UgB;;QAiBI,wHAAA,CAAA,WAAQ;;;KAjBZ", "debugId": null}}, {"offset": {"line": 5686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/delete-confirm-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\n\ninterface DeleteConfirmDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  description: string\n  itemName?: string\n  isLoading?: boolean\n}\n\nexport function DeleteConfirmDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  itemName,\n  isLoading = false\n}: DeleteConfirmDialogProps) {\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center justify-center w-10 h-10 bg-red-100 rounded-full\">\n              <Icon name=\"exclamation-triangle\" className=\"h-5 w-5 text-red-600\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-lg font-semibold\">\n                {title}\n              </DialogTitle>\n              <DialogDescription className=\"mt-1\">\n                {description}\n              </DialogDescription>\n            </div>\n          </div>\n        </DialogHeader>\n\n        {itemName && (\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4\">\n            <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n              即将删除：\n            </p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\n              {itemName}\n            </p>\n          </div>\n        )}\n\n        <div className=\"flex items-center justify-end gap-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n          >\n            取消\n          </Button>\n          <Button\n            variant=\"destructive\"\n            onClick={onConfirm}\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <>\n                <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                删除中...\n              </>\n            ) : (\n              <>\n                <Icon name=\"trash\" className=\"h-4 w-4 mr-2\" />\n                确认删除\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AAVA;;;;;AAsBO,SAAS,oBAAoB,KAQT;QARS,EAClC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,KAAK,EACQ,GARS;IASlC,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAuB,WAAU;;;;;;;;;;;0CAE9C,6LAAC;;kDACC,6LAAC,8HAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB;;;;;;kDAEH,6LAAC,8HAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;gBAMR,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAGjE,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCACX;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCAET,0BACC;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAU,WAAU;;;;;;oCAA8B;;6DAI/D;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAQ,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D;KApEgB", "debugId": null}}, {"offset": {"line": 5862, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/app/dashboard/search/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useCallback, Suspense } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport { DashboardHeader } from '@/components/dashboard-header'\nimport { SearchBar } from '@/components/search-bar'\nimport { SearchResults } from '@/components/search-results'\nimport { PromptDetailModal } from '@/components/prompt-detail-modal'\nimport { PromptFormModal } from '@/components/prompt-form-modal'\nimport { DeleteConfirmDialog } from '@/components/delete-confirm-dialog'\nimport { useToast } from '@/hooks/use-toast'\n// 直接使用 API 客户端\nimport * as api from '@/lib/api/client'\nimport type { PromptWithDetails, SearchHistory } from '@/types/database'\n\nfunction SearchPageContent() {\n  const searchParams = useSearchParams()\n  const initialQuery = searchParams.get('q') || ''\n\n  const [searchQuery, setSearchQuery] = useState(initialQuery)\n  const [searchResults, setSearchResults] = useState<PromptWithDetails[]>([])\n  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [total, setTotal] = useState(0)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [hasMore, setHasMore] = useState(false)\n  const [currentSearchParams, setCurrentSearchParams] = useState<any>({})\n\n  // 模态框状态\n  const [selectedPrompt, setSelectedPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)\n  const [isFormModalOpen, setIsFormModalOpen] = useState(false)\n  const [editingPrompt, setEditingPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)\n  const [deletingPrompt, setDeletingPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDeleting, setIsDeleting] = useState(false)\n\n  const { toast } = useToast()\n\n  const loadSearchHistory = useCallback(async () => {\n    try {\n      const data = await api.getSearchHistory(10)\n      setSearchHistory(data)\n    } catch (error) {\n      console.error('加载搜索历史失败:', error)\n    }\n  }, [])\n\n  useEffect(() => {\n    // 初始化函数，只在组件挂载时执行一次\n    const initializePage = async () => {\n      try {\n        // 加载搜索历史\n        await loadSearchHistory()\n\n        // 如果有初始查询，执行搜索\n        if (initialQuery) {\n          setIsLoading(true)\n          setCurrentPage(1)\n\n          const searchParams = {\n            query: initialQuery.trim() || undefined,\n            limit: 12,\n            offset: 0\n          }\n\n          setCurrentSearchParams(searchParams)\n\n          const results = await api.advancedSearch(searchParams)\n          setSearchResults(results)\n          setTotal(results.length)\n          setHasMore(results.length === 12)\n\n          if (initialQuery.trim()) {\n            await api.addSearchHistory(initialQuery.trim())\n            // 重新加载搜索历史\n            const updatedHistory = await api.getSearchHistory(10)\n            setSearchHistory(updatedHistory)\n          }\n        }\n      } catch (error) {\n        console.error('初始化页面失败:', error)\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    initializePage()\n  }, []) // 只在组件挂载时执行一次，避免无限循环\n\n  const handleSearch = useCallback(async (query: string, params: any = {}) => {\n    try {\n      setIsLoading(true)\n      setCurrentPage(1)\n\n      const searchParams = {\n        query: query.trim() || undefined,\n        ...params,\n        limit: 12,\n        offset: 0\n      }\n\n      setCurrentSearchParams(searchParams)\n\n      const results = await api.advancedSearch(searchParams)\n      setSearchResults(results)\n      setTotal(results.length)\n      setHasMore(results.length === 12) // 如果返回了完整的一页，可能还有更多\n\n      if (query.trim()) {\n        await api.addSearchHistory(query.trim())\n        await loadSearchHistory()\n      }\n    } catch (error) {\n      console.error('搜索失败:', error)\n      // 移除 toast 依赖，直接使用 console.error\n      // 用户可以通过控制台看到错误信息\n    } finally {\n      setIsLoading(false)\n    }\n  }, []) // 移除 toast 依赖\n\n  const handleAdvancedSearch = async (params: any) => {\n    await handleSearch('', params)\n  }\n\n  const handleLoadMore = async () => {\n    if (!hasMore || isLoading) return\n\n    try {\n      setIsLoading(true)\n      const nextPage = currentPage + 1\n      \n      const searchParams = {\n        ...currentSearchParams,\n        offset: (nextPage - 1) * 12\n      }\n\n      const results = await api.advancedSearch(searchParams)\n      setSearchResults(prev => [...prev, ...results])\n      setCurrentPage(nextPage)\n      setHasMore(results.length === 12)\n    } catch (error) {\n      console.error('加载更多失败:', error)\n      toast({\n        title: \"加载失败\",\n        description: \"加载更多结果时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleClearSearch = () => {\n    setSearchQuery('')\n    setSearchResults([])\n    setTotal(0)\n    setCurrentPage(1)\n    setHasMore(false)\n    setCurrentSearchParams({})\n  }\n\n  const handlePromptView = (promptId: string) => {\n    // 直接从搜索结果中查找数据，避免不必要的数据库查询\n    const prompt = searchResults.find(p => p.id === promptId)\n    if (prompt) {\n      setSelectedPrompt(prompt)\n      setIsDetailModalOpen(true)\n    } else {\n      console.error('未找到提示词:', promptId)\n      toast({\n        title: \"加载失败\",\n        description: \"未找到指定的提示词\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handlePromptEdit = (promptId: string) => {\n    // 直接从搜索结果中查找数据，避免不必要的数据库查询\n    const prompt = searchResults.find(p => p.id === promptId)\n    if (prompt) {\n      setEditingPrompt(prompt)\n      setIsFormModalOpen(true)\n    } else {\n      console.error('未找到提示词:', promptId)\n      toast({\n        title: \"加载失败\",\n        description: \"未找到指定的提示词\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handlePromptDelete = async (promptId: string) => {\n    const prompt = searchResults.find(p => p.id === promptId)\n    if (prompt) {\n      setDeletingPrompt(prompt)\n      setIsDeleteDialogOpen(true)\n    }\n  }\n\n  const handlePromptCopy = async (content: string, promptId: string) => {\n    try {\n      await api.incrementPromptUsage(promptId)\n      // 更新本地状态中的使用次数\n      setSearchResults(prev => \n        prev.map(p => \n          p.id === promptId \n            ? { ...p, usage_count: p.usage_count + 1 }\n            : p\n        )\n      )\n    } catch (error) {\n      console.error('更新使用次数失败:', error)\n    }\n  }\n\n  const handleDeleteConfirm = async () => {\n    if (!deletingPrompt) return\n\n    try {\n      setIsDeleting(true)\n      await api.deletePrompt(deletingPrompt.id)\n      \n      toast({\n        title: \"删除成功\",\n        description: \"提示词已成功删除\",\n      })\n      \n      setIsDeleteDialogOpen(false)\n      setDeletingPrompt(null)\n      \n      // 从搜索结果中移除已删除的提示词\n      setSearchResults(prev => prev.filter(p => p.id !== deletingPrompt.id))\n      setTotal(prev => prev - 1)\n    } catch (error) {\n      console.error('删除提示词失败:', error)\n      toast({\n        title: \"删除失败\",\n        description: \"删除提示词时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsDeleting(false)\n    }\n  }\n\n  const handleFormSuccess = () => {\n    // 重新搜索以获取最新数据\n    if (currentSearchParams.query || Object.keys(currentSearchParams).length > 1) {\n      handleSearch(searchQuery, currentSearchParams)\n    }\n  }\n\n  const handleClearSearchHistory = async () => {\n    try {\n      await api.clearSearchHistory()\n      setSearchHistory([])\n      toast({\n        title: \"清除成功\",\n        description: \"搜索历史已清除\",\n      })\n    } catch (error) {\n      console.error('清除搜索历史失败:', error)\n      toast({\n        title: \"清除失败\",\n        description: \"清除搜索历史时出现错误\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* 顶部导航 */}\n      <DashboardHeader />\n      \n      <div className=\"max-w-7xl mx-auto p-6\">\n        {/* 搜索栏 */}\n        <div className=\"mb-8\">\n          <div className=\"max-w-2xl\">\n            <SearchBar\n              value={searchQuery}\n              onChange={setSearchQuery}\n              onSearch={handleSearch}\n              searchHistory={searchHistory}\n              onClearHistory={handleClearSearchHistory}\n              placeholder=\"搜索提示词...\"\n            />\n          </div>\n        </div>\n\n        {/* 搜索结果 */}\n        <SearchResults\n          results={searchResults}\n          searchQuery={searchQuery}\n          total={total}\n          isLoading={isLoading}\n          hasMore={hasMore}\n          onLoadMore={handleLoadMore}\n          onPromptView={handlePromptView}\n          onPromptEdit={handlePromptEdit}\n          onPromptDelete={handlePromptDelete}\n          onPromptCopy={handlePromptCopy}\n          onAdvancedSearch={handleAdvancedSearch}\n          onClearSearch={handleClearSearch}\n        />\n      </div>\n\n      {/* 模态框 */}\n      <PromptDetailModal\n        prompt={selectedPrompt}\n        isOpen={isDetailModalOpen}\n        onClose={() => {\n          setIsDetailModalOpen(false)\n          setSelectedPrompt(null)\n        }}\n        onEdit={handlePromptEdit}\n        onDelete={handlePromptDelete}\n        onCopy={handlePromptCopy}\n      />\n\n      <PromptFormModal\n        prompt={editingPrompt}\n        isOpen={isFormModalOpen}\n        onClose={() => {\n          setIsFormModalOpen(false)\n          setEditingPrompt(null)\n        }}\n        onSuccess={handleFormSuccess}\n      />\n\n      <DeleteConfirmDialog\n        isOpen={isDeleteDialogOpen}\n        onClose={() => {\n          setIsDeleteDialogOpen(false)\n          setDeletingPrompt(null)\n        }}\n        onConfirm={handleDeleteConfirm}\n        title=\"删除提示词\"\n        description=\"此操作无法撤销，确定要删除这个提示词吗？\"\n        itemName={deletingPrompt?.title}\n        isLoading={isDeleting}\n      />\n    </div>\n  )\n}\n\nexport default function SearchPage() {\n  return (\n    <Suspense fallback={<div className=\"flex items-center justify-center h-64\">加载中...</div>}>\n      <SearchPageContent />\n    </Suspense>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;;AAZA;;;;;;;;;;;AAeA,SAAS;;IACP,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,aAAa,GAAG,CAAC,QAAQ;IAE9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAErE,QAAQ;IACR,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACpC,IAAI;gBACF,MAAM,OAAO,MAAM,uHAAA,CAAA,mBAAoB,CAAC;gBACxC,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;2DAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,oBAAoB;YACpB,MAAM;8DAAiB;oBACrB,IAAI;wBACF,SAAS;wBACT,MAAM;wBAEN,eAAe;wBACf,IAAI,cAAc;4BAChB,aAAa;4BACb,eAAe;4BAEf,MAAM,eAAe;gCACnB,OAAO,aAAa,IAAI,MAAM;gCAC9B,OAAO;gCACP,QAAQ;4BACV;4BAEA,uBAAuB;4BAEvB,MAAM,UAAU,MAAM,uHAAA,CAAA,iBAAkB,CAAC;4BACzC,iBAAiB;4BACjB,SAAS,QAAQ,MAAM;4BACvB,WAAW,QAAQ,MAAM,KAAK;4BAE9B,IAAI,aAAa,IAAI,IAAI;gCACvB,MAAM,uHAAA,CAAA,mBAAoB,CAAC,aAAa,IAAI;gCAC5C,WAAW;gCACX,MAAM,iBAAiB,MAAM,uHAAA,CAAA,mBAAoB,CAAC;gCAClD,iBAAiB;4BACnB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;oBAC5B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;sCAAG,EAAE,GAAE,qBAAqB;IAE5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,eAAO;gBAAe,0EAAc,CAAC;YACpE,IAAI;gBACF,aAAa;gBACb,eAAe;gBAEf,MAAM,eAAe;oBACnB,OAAO,MAAM,IAAI,MAAM;oBACvB,GAAG,MAAM;oBACT,OAAO;oBACP,QAAQ;gBACV;gBAEA,uBAAuB;gBAEvB,MAAM,UAAU,MAAM,uHAAA,CAAA,iBAAkB,CAAC;gBACzC,iBAAiB;gBACjB,SAAS,QAAQ,MAAM;gBACvB,WAAW,QAAQ,MAAM,KAAK,KAAI,oBAAoB;gBAEtD,IAAI,MAAM,IAAI,IAAI;oBAChB,MAAM,uHAAA,CAAA,mBAAoB,CAAC,MAAM,IAAI;oBACrC,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;YACvB,iCAAiC;YACjC,kBAAkB;YACpB,SAAU;gBACR,aAAa;YACf;QACF;sDAAG,EAAE,EAAE,cAAc;;IAErB,MAAM,uBAAuB,OAAO;QAClC,MAAM,aAAa,IAAI;IACzB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,WAAW;QAE3B,IAAI;YACF,aAAa;YACb,MAAM,WAAW,cAAc;YAE/B,MAAM,eAAe;gBACnB,GAAG,mBAAmB;gBACtB,QAAQ,CAAC,WAAW,CAAC,IAAI;YAC3B;YAEA,MAAM,UAAU,MAAM,uHAAA,CAAA,iBAAkB,CAAC;YACzC,iBAAiB,CAAA,OAAQ;uBAAI;uBAAS;iBAAQ;YAC9C,eAAe;YACf,WAAW,QAAQ,MAAM,KAAK;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,iBAAiB,EAAE;QACnB,SAAS;QACT,eAAe;QACf,WAAW;QACX,uBAAuB,CAAC;IAC1B;IAEA,MAAM,mBAAmB,CAAC;QACxB,2BAA2B;QAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,QAAQ;YACV,kBAAkB;YAClB,qBAAqB;QACvB,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,2BAA2B;QAC3B,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,QAAQ;YACV,iBAAiB;YACjB,mBAAmB;QACrB,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,QAAQ;YACV,kBAAkB;YAClB,sBAAsB;QACxB;IACF;IAEA,MAAM,mBAAmB,OAAO,SAAiB;QAC/C,IAAI;YACF,MAAM,uHAAA,CAAA,uBAAwB,CAAC;YAC/B,eAAe;YACf,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WACL;wBAAE,GAAG,CAAC;wBAAE,aAAa,EAAE,WAAW,GAAG;oBAAE,IACvC;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YACd,MAAM,uHAAA,CAAA,eAAgB,CAAC,eAAe,EAAE;YAExC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,sBAAsB;YACtB,kBAAkB;YAElB,kBAAkB;YAClB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;YACpE,SAAS,CAAA,OAAQ,OAAO;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB;QACxB,cAAc;QACd,IAAI,oBAAoB,KAAK,IAAI,OAAO,IAAI,CAAC,qBAAqB,MAAM,GAAG,GAAG;YAC5E,aAAa,aAAa;QAC5B;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,uHAAA,CAAA,qBAAsB;YAC5B,iBAAiB,EAAE;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,qIAAA,CAAA,kBAAe;;;;;0BAEhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+HAAA,CAAA,YAAS;gCACR,OAAO;gCACP,UAAU;gCACV,UAAU;gCACV,eAAe;gCACf,gBAAgB;gCAChB,aAAY;;;;;;;;;;;;;;;;kCAMlB,6LAAC,mIAAA,CAAA,gBAAa;wBACZ,SAAS;wBACT,aAAa;wBACb,OAAO;wBACP,WAAW;wBACX,SAAS;wBACT,YAAY;wBACZ,cAAc;wBACd,cAAc;wBACd,gBAAgB;wBAChB,cAAc;wBACd,kBAAkB;wBAClB,eAAe;;;;;;;;;;;;0BAKnB,6LAAC,2IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,kBAAkB;gBACpB;gBACA,QAAQ;gBACR,UAAU;gBACV,QAAQ;;;;;;0BAGV,6LAAC,yIAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,mBAAmB;oBACnB,iBAAiB;gBACnB;gBACA,WAAW;;;;;;0BAGb,6LAAC,6IAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS;oBACP,sBAAsB;oBACtB,kBAAkB;gBACpB;gBACA,WAAW;gBACX,OAAM;gBACN,aAAY;gBACZ,QAAQ,EAAE,2BAAA,qCAAA,eAAgB,KAAK;gBAC/B,WAAW;;;;;;;;;;;;AAInB;GA7US;;QACc,qIAAA,CAAA,kBAAe;QAqBlB,wHAAA,CAAA,WAAQ;;;KAtBnB;AA+UM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;YAAI,WAAU;sBAAwC;;;;;;kBACzE,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}