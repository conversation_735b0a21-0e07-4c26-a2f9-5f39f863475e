"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@/components/ui/icon'
import { formatNumber, formatRelativeTime } from '@/lib/utils/format'
import { 
  getPrompts, 
  getCategories, 
  getTags, 
  getPopularPrompts,
  getPopularTags,
  getPopularSearchTerms
} from '@/lib/database'
import type { 
  PromptWithDetails, 
  CategoryWithCount, 
  Tag, 
  SearchHistory 
} from '@/types/database'

interface StatsData {
  totalPrompts: number
  totalCategories: number
  totalTags: number
  totalUsage: number
  popularPrompts: PromptWithDetails[]
  popularTags: Array<Tag & { usage_count: number }>
  popularSearches: SearchHistory[]
  recentPrompts: PromptWithDetails[]
}

export function StatsDashboard() {
  const [stats, setStats] = useState<StatsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setIsLoading(true)
      
      const [
        promptsResponse,
        categories,
        tags,
        popularPrompts,
        popularTags,
        popularSearches
      ] = await Promise.all([
        getPrompts({ limit: 1000 }), // 获取所有提示词来计算统计
        getCategories(),
        getTags(),
        getPopularPrompts(5),
        getPopularTags(10),
        getPopularSearchTerms(5)
      ])

      const totalUsage = promptsResponse.data.reduce((sum, prompt) => sum + (prompt.usage_count || 0), 0)
      
      // 获取最近的提示词
      const recentPrompts = promptsResponse.data
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)

      setStats({
        totalPrompts: promptsResponse.total,
        totalCategories: categories.length,
        totalTags: tags.length,
        totalUsage,
        popularPrompts,
        popularTags,
        popularSearches,
        recentPrompts
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <Icon name="exclamation-triangle" className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-muted-foreground">无法加载统计数据</p>
      </div>
    )
  }

  if (!stats) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* 概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总提示词</CardTitle>
            <Icon name="file-text" className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalPrompts)}</div>
            <p className="text-xs text-muted-foreground">
              已创建的提示词数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总分类</CardTitle>
            <Icon name="folder" className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalCategories)}</div>
            <p className="text-xs text-muted-foreground">
              已创建的分类数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总标签</CardTitle>
            <Icon name="tags" className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalTags)}</div>
            <p className="text-xs text-muted-foreground">
              已创建的标签数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总使用次数</CardTitle>
            <Icon name="eye" className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalUsage)}</div>
            <p className="text-xs text-muted-foreground">
              累计复制使用次数
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 热门提示词 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="fire" className="h-5 w-5 text-orange-500" />
              热门提示词
            </CardTitle>
            <CardDescription>
              使用次数最多的提示词
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.popularPrompts.length > 0 ? (
                stats.popularPrompts.map((prompt, index) => (
                  <div key={prompt.id} className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 text-orange-600 text-xs font-medium">
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{prompt.title}</p>
                      <p className="text-sm text-muted-foreground">
                        使用 {prompt.usage_count} 次
                      </p>
                    </div>
                    {prompt.category && (
                      <Badge
                        variant="secondary"
                        style={{ 
                          backgroundColor: `${prompt.category.color}20`,
                          color: prompt.category.color 
                        }}
                      >
                        {prompt.category.name}
                      </Badge>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  暂无使用数据
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 热门标签 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="hashtag" className="h-5 w-5 text-blue-500" />
              热门标签
            </CardTitle>
            <CardDescription>
              使用最频繁的标签
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {stats.popularTags.length > 0 ? (
                stats.popularTags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant="secondary"
                    className="text-sm"
                    style={{ 
                      backgroundColor: `${tag.color}20`,
                      color: tag.color 
                    }}
                  >
                    {tag.name}
                    <span className="ml-1 text-xs opacity-75">
                      {tag.usageCount}
                    </span>
                  </Badge>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4 w-full">
                  暂无标签数据
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 热门搜索 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="search" className="h-5 w-5 text-green-500" />
              热门搜索
            </CardTitle>
            <CardDescription>
              搜索次数最多的关键词
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.popularSearches.length > 0 ? (
                stats.popularSearches.map((search, index) => (
                  <div key={search.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-green-600">
                        #{index + 1}
                      </span>
                      <span className="text-sm">{search.searchTerm}</span>
                    </div>
                    <Badge variant="outline">
                      {search.searchCount} 次
                    </Badge>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  暂无搜索数据
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 最近创建 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="clock" className="h-5 w-5 text-purple-500" />
              最近创建
            </CardTitle>
            <CardDescription>
              最新创建的提示词
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentPrompts.length > 0 ? (
                stats.recentPrompts.map((prompt) => (
                  <div key={prompt.id} className="flex items-center gap-3">
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{prompt.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatRelativeTime(prompt.createdAt)}
                      </p>
                    </div>
                    {prompt.category && (
                      <Badge
                        variant="secondary"
                        style={{ 
                          backgroundColor: `${prompt.category.color}20`,
                          color: prompt.category.color 
                        }}
                      >
                        {prompt.category.name}
                      </Badge>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  暂无提示词
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
