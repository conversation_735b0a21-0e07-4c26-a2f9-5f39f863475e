module.exports = {

"[project]/.next-internal/server/app/api/categories/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/lib/database/client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "closePool": ()=>closePool,
    "getConnection": ()=>getConnection,
    "getPool": ()=>getPool,
    "query": ()=>query,
    "transaction": ()=>transaction
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
// 数据库连接配置
const dbConfig = {
    host: process.env.DATABASE_HOST || '*************',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    database: process.env.DATABASE_NAME || 'prompt',
    user: process.env.DATABASE_USER || 'Seven',
    password: process.env.DATABASE_PASSWORD || 'Abc112211',
    // 连接池配置
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000
};
// 创建连接池
let pool = null;
function getPool() {
    if (!pool) {
        pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"](dbConfig);
        // 监听连接池事件
        pool.on('error', (err)=>{
            console.error('数据库连接池错误:', err);
        });
        pool.on('connect', ()=>{
            console.log('数据库连接成功');
        });
    }
    return pool;
}
async function getConnection() {
    const pool = getPool();
    return await pool.connect();
}
async function query(text, params) {
    const pool = getPool();
    try {
        const result = await pool.query(text, params);
        return result;
    } catch (error) {
        console.error('数据库查询错误:', error);
        throw error;
    }
}
async function transaction(callback) {
    const client = await getConnection();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    } finally{
        client.release();
    }
}
async function closePool() {
    if (pool) {
        await pool.end();
        pool = null;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/lib/client-cache.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 浏览器端缓存系统
 * 解决服务器端内存缓存在 Vercel 等 serverless 环境中的问题
 */ __turbopack_context__.s({
    "CLIENT_CACHE_KEYS": ()=>CLIENT_CACHE_KEYS,
    "CLIENT_CACHE_TTL": ()=>CLIENT_CACHE_TTL,
    "clientCache": ()=>clientCache,
    "invalidateClientCache": ()=>invalidateClientCache,
    "withClientCache": ()=>withClientCache
});
class ClientCache {
    memoryCache = new Map();
    version = '1.0.0' // 缓存版本，用于缓存失效
    ;
    /**
   * 生成稳定的缓存键
   */ generateKey(key, params) {
        if (!params) return key;
        // 确保对象键的顺序一致
        const sortedParams = this.sortObject(params);
        return `${key}:${JSON.stringify(sortedParams)}`;
    }
    /**
   * 递归排序对象键，确保缓存键的一致性
   */ sortObject(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (Array.isArray(obj)) return obj.map((item)=>this.sortObject(item));
        const sorted = {};
        Object.keys(obj).sort().forEach((key)=>{
            sorted[key] = this.sortObject(obj[key]);
        });
        return sorted;
    }
    /**
   * 设置缓存（内存 + localStorage）
   */ set(key, data, ttl = 5 * 60 * 1000, params) {
        const cacheKey = this.generateKey(key, params);
        const item = {
            data,
            timestamp: Date.now(),
            ttl,
            version: this.version
        };
        // 内存缓存
        this.memoryCache.set(cacheKey, item);
        // localStorage 缓存（仅在浏览器环境）
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 获取缓存
   */ get(key, params) {
        const cacheKey = this.generateKey(key, params);
        // 先尝试内存缓存
        let item = this.memoryCache.get(cacheKey);
        // 如果内存缓存没有，尝试 localStorage
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        if (!item) return null;
        // 检查版本
        if (item.version !== this.version) {
            this.delete(key, params);
            return null;
        }
        // 检查是否过期
        if (Date.now() - item.timestamp > item.ttl) {
            this.delete(key, params);
            return null;
        }
        return item.data;
    }
    /**
   * 删除缓存
   */ delete(key, params) {
        const cacheKey = this.generateKey(key, params);
        this.memoryCache.delete(cacheKey);
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 清除所有缓存
   */ clear() {
        this.memoryCache.clear();
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 清除匹配模式的缓存
   */ clearPattern(pattern) {
        // 清除内存缓存
        for (const key of this.memoryCache.keys()){
            if (key.includes(pattern)) {
                this.memoryCache.delete(key);
            }
        }
        // 清除 localStorage 缓存
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 获取缓存统计信息
   */ getStats() {
        let localStorageSize = 0;
        const keys = [];
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        return {
            memorySize: this.memoryCache.size,
            localStorageSize,
            keys: Array.from(new Set([
                ...Array.from(this.memoryCache.keys()),
                ...keys
            ]))
        };
    }
}
const clientCache = new ClientCache();
const CLIENT_CACHE_KEYS = {
    CATEGORIES: 'categories',
    PROMPTS: 'prompts',
    TAGS: 'tags',
    SEARCH_HISTORY: 'search_history',
    PROMPT_DETAIL: 'prompt_detail',
    APP_PREFERENCES: 'app_preferences'
};
const CLIENT_CACHE_TTL = {
    SHORT: 30 * 1000,
    MEDIUM: 2 * 60 * 1000,
    LONG: 10 * 60 * 1000,
    VERY_LONG: 30 * 60 * 1000
};
async function withClientCache(cacheKey, ttl, fn, params) {
    // 尝试从缓存获取
    const cached = clientCache.get(cacheKey, params);
    if (cached !== null) {
        console.log(`Client cache hit: ${cacheKey}`, params);
        return cached;
    }
    // 缓存未命中，执行函数
    console.log(`Client cache miss: ${cacheKey}`, params);
    const result = await fn();
    // 存入缓存
    clientCache.set(cacheKey, result, ttl, params);
    return result;
}
function invalidateClientCache(patterns) {
    patterns.forEach((pattern)=>{
        clientCache.clearPattern(pattern);
    });
    console.log(`Invalidated client cache patterns: ${patterns.join(', ')}`);
}
}),
"[project]/lib/database/categories.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "createCategory": ()=>createCategory,
    "deleteCategory": ()=>deleteCategory,
    "getCategories": ()=>getCategories,
    "getCategoryById": ()=>getCategoryById,
    "updateCategoriesOrder": ()=>updateCategoriesOrder,
    "updateCategory": ()=>updateCategory
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/client-cache.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function getCategories() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withClientCache"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].CATEGORIES, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_TTL"].LONG, async ()=>{
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
          SELECT 
            c.id,
            c.name,
            c.description,
            c.color,
            c.icon,
            c.sort_order,
            c.created_at,
            c.updated_at,
            COUNT(p.id) as prompt_count
          FROM categories c
          LEFT JOIN prompts p ON c.id = p.category_id AND p.deleted_at IS NULL
          WHERE c.deleted_at IS NULL
          GROUP BY c.id, c.name, c.description, c.color, c.icon, c.sort_order, c.created_at, c.updated_at
          ORDER BY c.sort_order, c.name
        `);
            return result.rows.map((row)=>({
                    id: row.id,
                    name: row.name,
                    description: row.description,
                    color: row.color,
                    icon: row.icon,
                    sort_order: row.sort_order,
                    created_at: row.created_at,
                    updated_at: row.updated_at,
                    deleted_at: row.deleted_at,
                    prompt_count: parseInt(row.prompt_count)
                }));
        } catch (error) {
            console.error('获取分类失败:', error);
            // 网络错误或其他问题时返回空数组，避免阻塞整个应用
            return [];
        }
    });
}
async function getCategoryById(id) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT * FROM categories 
      WHERE id = $1 AND deleted_at IS NULL
    `, [
            id
        ]);
        if (result.rows.length === 0) {
            return null;
        }
        const row = result.rows[0];
        return {
            id: row.id,
            name: row.name,
            description: row.description,
            color: row.color,
            icon: row.icon,
            sortOrder: row.sort_order,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    } catch (error) {
        console.error('获取分类失败:', error);
        throw new Error('获取分类失败');
    }
}
async function createCategory(data) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      INSERT INTO categories (name, description, color, icon, sort_order)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
            data.name,
            data.description,
            data.color,
            data.icon,
            data.sortOrder || 0
        ]);
        // 清除缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].CATEGORIES
        ]);
        const row = result.rows[0];
        return {
            id: row.id,
            name: row.name,
            description: row.description,
            color: row.color,
            icon: row.icon,
            sortOrder: row.sort_order,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    } catch (error) {
        console.error('创建分类失败:', error);
        if (error instanceof Error && error.message.includes('duplicate key')) {
            throw new Error('分类名称已存在');
        }
        throw new Error('创建分类失败');
    }
}
async function updateCategory(id, data) {
    try {
        const updateFields = [];
        const updateValues = [];
        let paramIndex = 1;
        if (data.name !== undefined) {
            updateFields.push(`name = $${paramIndex}`);
            updateValues.push(data.name);
            paramIndex++;
        }
        if (data.description !== undefined) {
            updateFields.push(`description = $${paramIndex}`);
            updateValues.push(data.description);
            paramIndex++;
        }
        if (data.color !== undefined) {
            updateFields.push(`color = $${paramIndex}`);
            updateValues.push(data.color);
            paramIndex++;
        }
        if (data.icon !== undefined) {
            updateFields.push(`icon = $${paramIndex}`);
            updateValues.push(data.icon);
            paramIndex++;
        }
        if (data.sortOrder !== undefined) {
            updateFields.push(`sort_order = $${paramIndex}`);
            updateValues.push(data.sortOrder);
            paramIndex++;
        }
        if (updateFields.length === 0) {
            throw new Error('没有要更新的字段');
        }
        updateFields.push(`updated_at = NOW()`);
        updateValues.push(id);
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      UPDATE categories 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND deleted_at IS NULL
      RETURNING *
    `, updateValues);
        if (result.rows.length === 0) {
            throw new Error('分类不存在');
        }
        // 清除缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].CATEGORIES
        ]);
        const row = result.rows[0];
        return {
            id: row.id,
            name: row.name,
            description: row.description,
            color: row.color,
            icon: row.icon,
            sortOrder: row.sort_order,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    } catch (error) {
        console.error('更新分类失败:', error);
        if (error instanceof Error && error.message.includes('duplicate key')) {
            throw new Error('分类名称已存在');
        }
        throw new Error('更新分类失败');
    }
}
async function deleteCategory(id) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transaction"])(async (client)=>{
            // 检查是否有提示词使用此分类
            const promptResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM prompts 
        WHERE category_id = $1 AND deleted_at IS NULL
      `, [
                id
            ]);
            const promptCount = parseInt(promptResult.rows[0].count);
            if (promptCount > 0) {
                throw new Error(`无法删除分类，还有 ${promptCount} 个提示词使用此分类`);
            }
            // 软删除分类
            const result = await client.query(`
        UPDATE categories 
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
        RETURNING id
      `, [
                id
            ]);
            if (result.rows.length === 0) {
                throw new Error('分类不存在');
            }
        });
        // 清除缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].CATEGORIES
        ]);
    } catch (error) {
        console.error('删除分类失败:', error);
        throw error;
    }
}
async function updateCategoriesOrder(categoryOrders) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transaction"])(async (client)=>{
            for (const { id, sortOrder } of categoryOrders){
                await client.query(`
          UPDATE categories 
          SET sort_order = $1, updated_at = NOW()
          WHERE id = $2 AND deleted_at IS NULL
        `, [
                    sortOrder,
                    id
                ]);
            }
        });
        // 清除缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].CATEGORIES
        ]);
    } catch (error) {
        console.error('更新分类排序失败:', error);
        throw new Error('更新分类排序失败');
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/app/api/categories/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "GET": ()=>GET,
    "PATCH": ()=>PATCH,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/categories.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function GET(request) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCategories"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('获取分类列表失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '获取分类列表失败'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const data = await request.json();
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCategory"])(data);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('创建分类失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error instanceof Error ? error.message : '创建分类失败'
        }, {
            status: 500
        });
    }
}
async function PATCH(request) {
    try {
        const data = await request.json();
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateCategoriesOrder"])(data);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('更新分类排序失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error instanceof Error ? error.message : '更新分类排序失败'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__99d7893e._.js.map