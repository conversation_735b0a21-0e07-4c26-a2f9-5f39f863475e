module.exports = {

"[project]/.next-internal/server/app/api/prompts/[id]/usage/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/lib/database/client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "closePool": ()=>closePool,
    "getConnection": ()=>getConnection,
    "getPool": ()=>getPool,
    "query": ()=>query,
    "transaction": ()=>transaction
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
// 数据库连接配置
const dbConfig = {
    host: process.env.DATABASE_HOST || '*************',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    database: process.env.DATABASE_NAME || 'prompt',
    user: process.env.DATABASE_USER || 'Seven',
    password: process.env.DATABASE_PASSWORD || 'Abc112211',
    // 连接池配置
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000
};
// 创建连接池
let pool = null;
function getPool() {
    if (!pool) {
        pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"](dbConfig);
        // 监听连接池事件
        pool.on('error', (err)=>{
            console.error('数据库连接池错误:', err);
        });
        pool.on('connect', ()=>{
            console.log('数据库连接成功');
        });
    }
    return pool;
}
async function getConnection() {
    const pool = getPool();
    return await pool.connect();
}
async function query(text, params) {
    const pool = getPool();
    try {
        const result = await pool.query(text, params);
        return result;
    } catch (error) {
        console.error('数据库查询错误:', error);
        throw error;
    }
}
async function transaction(callback) {
    const client = await getConnection();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    } finally{
        client.release();
    }
}
async function closePool() {
    if (pool) {
        await pool.end();
        pool = null;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/lib/client-cache.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 浏览器端缓存系统
 * 解决服务器端内存缓存在 Vercel 等 serverless 环境中的问题
 */ __turbopack_context__.s({
    "CLIENT_CACHE_KEYS": ()=>CLIENT_CACHE_KEYS,
    "CLIENT_CACHE_TTL": ()=>CLIENT_CACHE_TTL,
    "clientCache": ()=>clientCache,
    "invalidateClientCache": ()=>invalidateClientCache,
    "withClientCache": ()=>withClientCache
});
class ClientCache {
    memoryCache = new Map();
    version = '1.0.0' // 缓存版本，用于缓存失效
    ;
    /**
   * 生成稳定的缓存键
   */ generateKey(key, params) {
        if (!params) return key;
        // 确保对象键的顺序一致
        const sortedParams = this.sortObject(params);
        return `${key}:${JSON.stringify(sortedParams)}`;
    }
    /**
   * 递归排序对象键，确保缓存键的一致性
   */ sortObject(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (Array.isArray(obj)) return obj.map((item)=>this.sortObject(item));
        const sorted = {};
        Object.keys(obj).sort().forEach((key)=>{
            sorted[key] = this.sortObject(obj[key]);
        });
        return sorted;
    }
    /**
   * 设置缓存（内存 + localStorage）
   */ set(key, data, ttl = 5 * 60 * 1000, params) {
        const cacheKey = this.generateKey(key, params);
        const item = {
            data,
            timestamp: Date.now(),
            ttl,
            version: this.version
        };
        // 内存缓存
        this.memoryCache.set(cacheKey, item);
        // localStorage 缓存（仅在浏览器环境）
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 获取缓存
   */ get(key, params) {
        const cacheKey = this.generateKey(key, params);
        // 先尝试内存缓存
        let item = this.memoryCache.get(cacheKey);
        // 如果内存缓存没有，尝试 localStorage
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        if (!item) return null;
        // 检查版本
        if (item.version !== this.version) {
            this.delete(key, params);
            return null;
        }
        // 检查是否过期
        if (Date.now() - item.timestamp > item.ttl) {
            this.delete(key, params);
            return null;
        }
        return item.data;
    }
    /**
   * 删除缓存
   */ delete(key, params) {
        const cacheKey = this.generateKey(key, params);
        this.memoryCache.delete(cacheKey);
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 清除所有缓存
   */ clear() {
        this.memoryCache.clear();
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 清除匹配模式的缓存
   */ clearPattern(pattern) {
        // 清除内存缓存
        for (const key of this.memoryCache.keys()){
            if (key.includes(pattern)) {
                this.memoryCache.delete(key);
            }
        }
        // 清除 localStorage 缓存
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    /**
   * 获取缓存统计信息
   */ getStats() {
        let localStorageSize = 0;
        const keys = [];
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        return {
            memorySize: this.memoryCache.size,
            localStorageSize,
            keys: Array.from(new Set([
                ...Array.from(this.memoryCache.keys()),
                ...keys
            ]))
        };
    }
}
const clientCache = new ClientCache();
const CLIENT_CACHE_KEYS = {
    CATEGORIES: 'categories',
    PROMPTS: 'prompts',
    TAGS: 'tags',
    SEARCH_HISTORY: 'search_history',
    PROMPT_DETAIL: 'prompt_detail',
    APP_PREFERENCES: 'app_preferences'
};
const CLIENT_CACHE_TTL = {
    SHORT: 30 * 1000,
    MEDIUM: 2 * 60 * 1000,
    LONG: 10 * 60 * 1000,
    VERY_LONG: 30 * 60 * 1000
};
async function withClientCache(cacheKey, ttl, fn, params) {
    // 尝试从缓存获取
    const cached = clientCache.get(cacheKey, params);
    if (cached !== null) {
        console.log(`Client cache hit: ${cacheKey}`, params);
        return cached;
    }
    // 缓存未命中，执行函数
    console.log(`Client cache miss: ${cacheKey}`, params);
    const result = await fn();
    // 存入缓存
    clientCache.set(cacheKey, result, ttl, params);
    return result;
}
function invalidateClientCache(patterns) {
    patterns.forEach((pattern)=>{
        clientCache.clearPattern(pattern);
    });
    console.log(`Invalidated client cache patterns: ${patterns.join(', ')}`);
}
}),
"[project]/lib/database/prompts.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "createPrompt": ()=>createPrompt,
    "deletePrompt": ()=>deletePrompt,
    "getPromptById": ()=>getPromptById,
    "getPrompts": ()=>getPrompts,
    "incrementUsageCount": ()=>incrementUsageCount,
    "updatePrompt": ()=>updatePrompt
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/client-cache.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function getPrompts(params = {}) {
    const { query: searchQuery, categoryId, tagIds = [], sortBy = 'updated_at', sortOrder = 'desc', limit = 12, offset = 0 } = params;
    // 只对非搜索查询进行缓存（搜索结果变化频繁）
    const shouldCache = !searchQuery && offset === 0 && limit <= 12;
    if (shouldCache) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withClientCache"])(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPTS, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_TTL"].MEDIUM, ()=>executeQuery(params), params);
    } else {
        return executeQuery(params);
    }
}
async function executeQuery(params) {
    const { query: searchQuery, categoryId, tagIds = [], sortBy = 'updated_at', sortOrder = 'desc', limit = 12, offset = 0 } = params;
    try {
        // 构建基础查询
        let queryText = `
      SELECT 
        p.id,
        p.title,
        p.description,
        p.content,
        p.category_id,
        p.usage_count,
        p.metadata,
        p.created_at,
        p.updated_at,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),
          ARRAY[]::TEXT[]
        ) as tag_names,
        COALESCE(
          ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),
          ARRAY[]::UUID[]
        ) as tag_ids,
        COALESCE(
          ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),
          ARRAY[]::TEXT[]
        ) as tag_colors
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `;
        const queryParams = [];
        let paramIndex = 1;
        // 添加分类过滤
        if (categoryId) {
            queryText += ` AND p.category_id = $${paramIndex}`;
            queryParams.push(categoryId);
            paramIndex++;
        }
        // 添加搜索条件
        if (searchQuery) {
            queryText += ` AND (
        p.title ILIKE $${paramIndex} 
        OR p.content ILIKE $${paramIndex}
        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${paramIndex + 1})
      )`;
            queryParams.push(`%${searchQuery}%`, searchQuery);
            paramIndex += 2;
        }
        // 添加标签过滤
        if (tagIds.length > 0) {
            queryText += ` AND p.id IN (
        SELECT pt.prompt_id 
        FROM prompt_tags pt 
        WHERE pt.tag_id = ANY($${paramIndex})
      )`;
            queryParams.push(tagIds);
            paramIndex++;
        }
        queryText += ` GROUP BY p.id, c.name, c.color, c.icon`;
        // 添加排序
        const validSortColumns = [
            'created_at',
            'updated_at',
            'usage_count',
            'title'
        ];
        const validSortOrders = [
            'asc',
            'desc'
        ];
        if (validSortColumns.includes(sortBy) && validSortOrders.includes(sortOrder)) {
            queryText += ` ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}`;
        } else {
            queryText += ` ORDER BY p.updated_at DESC`;
        }
        // 添加分页
        queryText += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        queryParams.push(limit, offset);
        // 执行查询
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(queryText, queryParams);
        // 获取总数
        let countQuery = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM prompts p
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      WHERE p.deleted_at IS NULL
    `;
        const countParams = [];
        let countParamIndex = 1;
        if (categoryId) {
            countQuery += ` AND p.category_id = $${countParamIndex}`;
            countParams.push(categoryId);
            countParamIndex++;
        }
        if (searchQuery) {
            countQuery += ` AND (
        p.title ILIKE $${countParamIndex} 
        OR p.content ILIKE $${countParamIndex}
        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${countParamIndex + 1})
      )`;
            countParams.push(`%${searchQuery}%`, searchQuery);
            countParamIndex += 2;
        }
        if (tagIds.length > 0) {
            countQuery += ` AND p.id IN (
        SELECT pt.prompt_id 
        FROM prompt_tags pt 
        WHERE pt.tag_id = ANY($${countParamIndex})
      )`;
            countParams.push(tagIds);
        }
        const countResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(countQuery, countParams);
        const total = parseInt(countResult.rows[0].total);
        // 处理结果数据
        const prompts = result.rows.map((row)=>({
                id: row.id,
                title: row.title,
                description: row.description,
                content: row.content,
                category_id: row.category_id,
                usage_count: row.usage_count,
                user_id: row.user_id,
                metadata: row.metadata,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
                category: row.category_name ? {
                    id: row.category_id,
                    name: row.category_name,
                    color: row.category_color,
                    icon: row.category_icon
                } : null,
                tags: row.tag_names.map((name, index)=>({
                        id: row.tag_ids[index],
                        name,
                        color: row.tag_colors[index]
                    })).filter((tag)=>tag.name)
            }));
        return {
            data: prompts,
            total,
            hasMore: offset + limit < total,
            nextOffset: offset + limit < total ? offset + limit : null
        };
    } catch (error) {
        console.error('获取提示词列表失败:', error);
        throw new Error('获取提示词列表失败');
    }
}
async function getPromptById(id) {
    try {
        // 如果是本地ID，不要查询数据库
        if (id.startsWith('local_')) {
            console.warn('尝试查询本地ID，跳过查询:', id);
            return null;
        }
        // 使用客户端缓存，缓存时间为5分钟
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withClientCache"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPT_DETAIL}_${id}`, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_TTL"].MEDIUM, async ()=>{
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
          SELECT 
            p.id,
            p.title,
            p.description,
            p.content,
            p.category_id,
            p.usage_count,
            p.metadata,
            p.created_at,
            p.updated_at,
            c.name as category_name,
            c.color as category_color,
            c.icon as category_icon,
            COALESCE(
              ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),
              ARRAY[]::TEXT[]
            ) as tag_names,
            COALESCE(
              ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),
              ARRAY[]::UUID[]
            ) as tag_ids,
            COALESCE(
              ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),
              ARRAY[]::TEXT[]
            ) as tag_colors
          FROM prompts p
          LEFT JOIN categories c ON p.category_id = c.id
          LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
          LEFT JOIN tags t ON pt.tag_id = t.id
          WHERE p.id = $1 AND p.deleted_at IS NULL
          GROUP BY p.id, c.name, c.color, c.icon
        `, [
                id
            ]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            return {
                id: row.id,
                title: row.title,
                description: row.description,
                content: row.content,
                category_id: row.category_id,
                usage_count: row.usage_count,
                user_id: row.user_id,
                metadata: row.metadata,
                created_at: row.created_at,
                updated_at: row.updated_at,
                deleted_at: row.deleted_at,
                category: row.category_name ? {
                    id: row.category_id,
                    name: row.category_name,
                    color: row.category_color,
                    icon: row.category_icon
                } : null,
                tags: row.tag_names.map((name, index)=>({
                        id: row.tag_ids[index],
                        name,
                        color: row.tag_colors[index]
                    })).filter((tag)=>tag.name)
            };
        });
    } catch (error) {
        console.error('获取提示词失败:', error);
        throw new Error('获取提示词失败');
    }
}
async function createPrompt(data) {
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transaction"])(async (client)=>{
            // 插入提示词
            const promptResult = await client.query(`
        INSERT INTO prompts (title, description, content, category_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [
                data.title,
                data.description,
                data.content,
                data.category_id,
                data.metadata || {}
            ]);
            const prompt = promptResult.rows[0];
            // 处理标签关联
            if (data.tagIds && data.tagIds.length > 0) {
                for (const tagId of data.tagIds){
                    await client.query(`
            INSERT INTO prompt_tags (prompt_id, tag_id)
            VALUES ($1, $2)
            ON CONFLICT (prompt_id, tag_id) DO NOTHING
          `, [
                        prompt.id,
                        tagId
                    ]);
                }
            }
            // 清除缓存
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPTS
            ]);
            // 获取完整的提示词信息
            return await getPromptById(prompt.id);
        });
    } catch (error) {
        console.error('创建提示词失败:', error);
        throw new Error('创建提示词失败');
    }
}
async function updatePrompt(id, data) {
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transaction"])(async (client)=>{
            // 更新提示词基本信息
            const updateFields = [];
            const updateValues = [];
            let paramIndex = 1;
            if (data.title !== undefined) {
                updateFields.push(`title = $${paramIndex}`);
                updateValues.push(data.title);
                paramIndex++;
            }
            if (data.description !== undefined) {
                updateFields.push(`description = $${paramIndex}`);
                updateValues.push(data.description);
                paramIndex++;
            }
            if (data.content !== undefined) {
                updateFields.push(`content = $${paramIndex}`);
                updateValues.push(data.content);
                paramIndex++;
            }
            if (data.category_id !== undefined) {
                updateFields.push(`category_id = $${paramIndex}`);
                updateValues.push(data.category_id);
                paramIndex++;
            }
            if (data.metadata !== undefined) {
                updateFields.push(`metadata = $${paramIndex}`);
                updateValues.push(data.metadata);
                paramIndex++;
            }
            if (updateFields.length > 0) {
                updateFields.push(`updated_at = NOW()`);
                updateValues.push(id);
                await client.query(`
          UPDATE prompts
          SET ${updateFields.join(', ')}
          WHERE id = $${paramIndex} AND deleted_at IS NULL
        `, updateValues);
            }
            // 更新标签关联
            if (data.tagIds !== undefined) {
                // 删除现有标签关联
                await client.query(`
          DELETE FROM prompt_tags WHERE prompt_id = $1
        `, [
                    id
                ]);
                // 添加新的标签关联
                if (data.tagIds.length > 0) {
                    for (const tagId of data.tagIds){
                        await client.query(`
              INSERT INTO prompt_tags (prompt_id, tag_id)
              VALUES ($1, $2)
            `, [
                            id,
                            tagId
                        ]);
                    }
                }
            }
            // 清除缓存
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
                __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPTS,
                `${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPT_DETAIL}_${id}`
            ]);
            // 获取更新后的提示词信息
            return await getPromptById(id);
        });
    } catch (error) {
        console.error('更新提示词失败:', error);
        throw new Error('更新提示词失败');
    }
}
async function deletePrompt(id) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      UPDATE prompts
      SET deleted_at = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `, [
            id
        ]);
        // 清除缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPTS,
            `${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPT_DETAIL}_${id}`
        ]);
    } catch (error) {
        console.error('删除提示词失败:', error);
        throw new Error('删除提示词失败');
    }
}
async function incrementUsageCount(id) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      UPDATE prompts
      SET usage_count = usage_count + 1, updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `, [
            id
        ]);
        // 清除相关缓存
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invalidateClientCache"])([
            `${__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPT_DETAIL}_${id}`,
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$client$2d$cache$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CLIENT_CACHE_KEYS"].PROMPTS
        ]);
    } catch (error) {
        console.error('增加使用次数失败:', error);
        throw new Error('增加使用次数失败');
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/app/api/prompts/[id]/usage/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/prompts.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function POST(request, { params }) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["incrementUsageCount"])(params.id);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('增加使用次数失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '增加使用次数失败'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__77568ca3._.js.map