{"version": 1, "files": ["../../../webpack-runtime.js", "../../../chunks/985.js", "../../../chunks/519.js", "../../../chunks/484.js", "../../../chunks/884.js", "../../../chunks/710.js", "../../../chunks/236.js", "page_client-reference-manifest.js", "../../../../../components/dashboard-header.tsx", "../../../../../components/ui/icon.tsx", "../../../../../components/delete-confirm-dialog.tsx", "../../../../../hooks/use-toast.ts", "../../../../../lib/api/client.ts", "../../../../../components/category-form-modal.tsx", "../../../../../components/sortable-category-item.tsx", "../../../../../lib/database/index.ts", "../../../../../package.json", "../../../../../lib/utils/format.ts", "../../../../../components/ui/input.tsx", "../../../../../components/ui/dialog.tsx", "../../../../../components/ui/textarea.tsx", "../../../../../components/ui/label.tsx", "../../../../../components/ui/dropdown-menu.tsx"]}