exports.id=261,exports.ids=[261],exports.modules={18968:(a,b,c)=>{"use strict";c.d(b,{Ws:()=>g,eI:()=>h,nc:()=>i,uW:()=>f});class d{generateKey(a,b){if(!b)return a;let c=this.sortObject(b);return`${a}:${JSON.stringify(c)}`}sortObject(a){if(null===a||"object"!=typeof a)return a;if(Array.isArray(a))return a.map(a=>this.sortObject(a));let b={};return Object.keys(a).sort().forEach(c=>{b[c]=this.sortObject(a[c])}),b}set(a,b,c=3e5,d){let e=this.generateKey(a,d),f={data:b,timestamp:Date.now(),ttl:c,version:this.version};this.memoryCache.set(e,f)}get(a,b){let c=this.generateKey(a,b),d=this.memoryCache.get(c);return d?d.version!==this.version||Date.now()-d.timestamp>d.ttl?(this.delete(a,b),null):d.data:null}delete(a,b){let c=this.generateKey(a,b);this.memoryCache.delete(c)}clear(){this.memoryCache.clear()}clearPattern(a){for(let b of this.memoryCache.keys())b.includes(a)&&this.memoryCache.delete(b)}getStats(){return{memorySize:this.memoryCache.size,localStorageSize:0,keys:Array.from(new Set([...Array.from(this.memoryCache.keys())]))}}constructor(){this.memoryCache=new Map,this.version="1.0.0"}}let e=new d,f={CATEGORIES:"categories",PROMPTS:"prompts",TAGS:"tags",SEARCH_HISTORY:"search_history",PROMPT_DETAIL:"prompt_detail",APP_PREFERENCES:"app_preferences"},g={SHORT:3e4,MEDIUM:12e4,LONG:6e5,VERY_LONG:18e5};async function h(a,b,c,d){let f=e.get(a,d);if(null!==f)return console.log(`Client cache hit: ${a}`,d),f;console.log(`Client cache miss: ${a}`,d);let g=await c();return e.set(a,g,b,d),g}function i(a){a.forEach(a=>{e.clearPattern(a)}),console.log(`Invalidated client cache patterns: ${a.join(", ")}`)}},43779:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{P:()=>i,Rn:()=>j});var e=c(64939),f=a([e]);e=(f.then?(await f)():f)[0];let k={host:process.env.DATABASE_HOST||"*************",port:parseInt(process.env.DATABASE_PORT||"5432"),database:process.env.DATABASE_NAME||"prompt",user:process.env.DATABASE_USER||"Seven",password:process.env.DATABASE_PASSWORD||"Abc112211",max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3},l=null;function g(){return l||((l=new e.Pool(k)).on("error",a=>{console.error("数据库连接池错误:",a)}),l.on("connect",()=>{console.log("数据库连接成功")})),l}async function h(){let a=g();return await a.connect()}async function i(a,b){let c=g();try{return await c.query(a,b)}catch(a){throw console.error("数据库查询错误:",a),a}}async function j(a){let b=await h();try{await b.query("BEGIN");let c=await a(b);return await b.query("COMMIT"),c}catch(a){throw await b.query("ROLLBACK"),a}finally{b.release()}}d()}catch(a){d(a)}})},78335:()=>{},80537:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Fw:()=>j,HK:()=>k,Ir:()=>m,oO:()=>h,qG:()=>n,qj:()=>l});var e=c(43779),f=c(18968),g=a([e]);async function h(a={}){let{query:b,categoryId:c,tagIds:d=[],sortBy:e="updated_at",sortOrder:g="desc",limit:j=12,offset:k=0}=a;return!b&&0===k&&j<=12?(0,f.eI)(f.uW.PROMPTS,f.Ws.MEDIUM,()=>i(a),a):i(a)}async function i(a){let{query:b,categoryId:c,tagIds:d=[],sortBy:f="updated_at",sortOrder:g="desc",limit:h=12,offset:i=0}=a;try{let a=`
      SELECT 
        p.id,
        p.title,
        p.description,
        p.content,
        p.category_id,
        p.usage_count,
        p.metadata,
        p.created_at,
        p.updated_at,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),
          ARRAY[]::TEXT[]
        ) as tag_names,
        COALESCE(
          ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),
          ARRAY[]::UUID[]
        ) as tag_ids,
        COALESCE(
          ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),
          ARRAY[]::TEXT[]
        ) as tag_colors
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `,j=[],k=1;c&&(a+=` AND p.category_id = $${k}`,j.push(c),k++),b&&(a+=` AND (
        p.title ILIKE $${k} 
        OR p.content ILIKE $${k}
        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${k+1})
      )`,j.push(`%${b}%`,b),k+=2),d.length>0&&(a+=` AND p.id IN (
        SELECT pt.prompt_id 
        FROM prompt_tags pt 
        WHERE pt.tag_id = ANY($${k})
      )`,j.push(d),k++),a+=" GROUP BY p.id, c.name, c.color, c.icon",["created_at","updated_at","usage_count","title"].includes(f)&&["asc","desc"].includes(g)?a+=` ORDER BY p.${f} ${g.toUpperCase()}`:a+=" ORDER BY p.updated_at DESC",a+=` LIMIT $${k} OFFSET $${k+1}`,j.push(h,i);let l=await (0,e.P)(a,j),m=`
      SELECT COUNT(DISTINCT p.id) as total
      FROM prompts p
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      WHERE p.deleted_at IS NULL
    `,n=[],o=1;c&&(m+=` AND p.category_id = $${o}`,n.push(c),o++),b&&(m+=` AND (
        p.title ILIKE $${o} 
        OR p.content ILIKE $${o}
        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${o+1})
      )`,n.push(`%${b}%`,b),o+=2),d.length>0&&(m+=` AND p.id IN (
        SELECT pt.prompt_id 
        FROM prompt_tags pt 
        WHERE pt.tag_id = ANY($${o})
      )`,n.push(d));let p=await (0,e.P)(m,n),q=parseInt(p.rows[0].total);return{data:l.rows.map(a=>({id:a.id,title:a.title,description:a.description,content:a.content,category_id:a.category_id,usage_count:a.usage_count,user_id:a.user_id,metadata:a.metadata,created_at:a.created_at,updated_at:a.updated_at,deleted_at:a.deleted_at,category:a.category_name?{id:a.category_id,name:a.category_name,color:a.category_color,icon:a.category_icon}:null,tags:a.tag_names.map((b,c)=>({id:a.tag_ids[c],name:b,color:a.tag_colors[c]})).filter(a=>a.name)})),total:q,hasMore:i+h<q,nextOffset:i+h<q?i+h:null}}catch(a){throw console.error("获取提示词列表失败:",a),Error("获取提示词列表失败")}}async function j(a){try{if(a.startsWith("local_"))return console.warn("尝试查询本地ID，跳过查询:",a),null;return(0,f.eI)(`${f.uW.PROMPT_DETAIL}_${a}`,f.Ws.MEDIUM,async()=>{let b=await (0,e.P)(`
          SELECT 
            p.id,
            p.title,
            p.description,
            p.content,
            p.category_id,
            p.usage_count,
            p.metadata,
            p.created_at,
            p.updated_at,
            c.name as category_name,
            c.color as category_color,
            c.icon as category_icon,
            COALESCE(
              ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),
              ARRAY[]::TEXT[]
            ) as tag_names,
            COALESCE(
              ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),
              ARRAY[]::UUID[]
            ) as tag_ids,
            COALESCE(
              ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),
              ARRAY[]::TEXT[]
            ) as tag_colors
          FROM prompts p
          LEFT JOIN categories c ON p.category_id = c.id
          LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
          LEFT JOIN tags t ON pt.tag_id = t.id
          WHERE p.id = $1 AND p.deleted_at IS NULL
          GROUP BY p.id, c.name, c.color, c.icon
        `,[a]);if(0===b.rows.length)return null;let c=b.rows[0];return{id:c.id,title:c.title,description:c.description,content:c.content,category_id:c.category_id,usage_count:c.usage_count,user_id:c.user_id,metadata:c.metadata,created_at:c.created_at,updated_at:c.updated_at,deleted_at:c.deleted_at,category:c.category_name?{id:c.category_id,name:c.category_name,color:c.category_color,icon:c.category_icon}:null,tags:c.tag_names.map((a,b)=>({id:c.tag_ids[b],name:a,color:c.tag_colors[b]})).filter(a=>a.name)}})}catch(a){throw console.error("获取提示词失败:",a),Error("获取提示词失败")}}async function k(a){try{return await (0,e.Rn)(async b=>{let c=(await b.query(`
        INSERT INTO prompts (title, description, content, category_id, metadata)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `,[a.title,a.description,a.content,a.category_id,a.metadata||{}])).rows[0];if(a.tagIds&&a.tagIds.length>0)for(let d of a.tagIds)await b.query(`
            INSERT INTO prompt_tags (prompt_id, tag_id)
            VALUES ($1, $2)
            ON CONFLICT (prompt_id, tag_id) DO NOTHING
          `,[c.id,d]);return(0,f.nc)([f.uW.PROMPTS]),await j(c.id)})}catch(a){throw console.error("创建提示词失败:",a),Error("创建提示词失败")}}async function l(a,b){try{return await (0,e.Rn)(async c=>{let d=[],e=[],g=1;if(void 0!==b.title&&(d.push(`title = $${g}`),e.push(b.title),g++),void 0!==b.description&&(d.push(`description = $${g}`),e.push(b.description),g++),void 0!==b.content&&(d.push(`content = $${g}`),e.push(b.content),g++),void 0!==b.category_id&&(d.push(`category_id = $${g}`),e.push(b.category_id),g++),void 0!==b.metadata&&(d.push(`metadata = $${g}`),e.push(b.metadata),g++),d.length>0&&(d.push("updated_at = NOW()"),e.push(a),await c.query(`
          UPDATE prompts
          SET ${d.join(", ")}
          WHERE id = $${g} AND deleted_at IS NULL
        `,e)),void 0!==b.tagIds&&(await c.query(`
          DELETE FROM prompt_tags WHERE prompt_id = $1
        `,[a]),b.tagIds.length>0))for(let d of b.tagIds)await c.query(`
              INSERT INTO prompt_tags (prompt_id, tag_id)
              VALUES ($1, $2)
            `,[a,d]);return(0,f.nc)([f.uW.PROMPTS,`${f.uW.PROMPT_DETAIL}_${a}`]),await j(a)})}catch(a){throw console.error("更新提示词失败:",a),Error("更新提示词失败")}}async function m(a){try{await (0,e.P)(`
      UPDATE prompts
      SET deleted_at = NOW(), updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `,[a]),(0,f.nc)([f.uW.PROMPTS,`${f.uW.PROMPT_DETAIL}_${a}`])}catch(a){throw console.error("删除提示词失败:",a),Error("删除提示词失败")}}async function n(a){try{await (0,e.P)(`
      UPDATE prompts
      SET usage_count = usage_count + 1, updated_at = NOW()
      WHERE id = $1 AND deleted_at IS NULL
    `,[a]),(0,f.nc)([`${f.uW.PROMPT_DETAIL}_${a}`,f.uW.PROMPTS])}catch(a){throw console.error("增加使用次数失败:",a),Error("增加使用次数失败")}}e=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},96487:()=>{}};