'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { 
  Trash2, 
  Copy, 
  Move, 
  Download, 
  ChevronDown,
  X,
  CheckSquare,
  Square,
  Minus
} from 'lucide-react'
import { 
  batchDeletePrompts, 
  batchMovePrompts, 
  batchCopyPrompts, 
  batchExportPrompts,
  getBatchOperationConfirmMessage,
  type BatchSelectionManager 
} from '@/lib/batch-operations'
import { downloadFile } from '@/lib/data-export'

interface BatchOperationsToolbarProps {
  selectionManager: BatchSelectionManager
  selectedIds: string[]
  allIds: string[]
  categories: Array<{ id: string; name: string }>
  onOperationComplete: () => void
  className?: string
}

export function BatchOperationsToolbar({
  selectionManager,
  selectedIds,
  allIds,
  categories,
  onOperationComplete,
  className = ''
}: BatchOperationsToolbarProps) {
  const { toast } = useToast()
  const [isOperating, setIsOperating] = useState(false)

  const selectedCount = selectedIds.length
  const totalCount = allIds.length
  const hasSelection = selectedCount > 0
  const isAllSelected = selectionManager.isAllSelected(allIds)
  const isPartiallySelected = selectionManager.isPartiallySelected(allIds)

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (isAllSelected) {
      selectionManager.deselectAll()
    } else {
      selectionManager.selectAll(allIds)
    }
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (!hasSelection) return

    const confirmMessage = getBatchOperationConfirmMessage('delete', selectedCount)
    if (!confirm(confirmMessage)) return

    setIsOperating(true)
    try {
      const result = await batchDeletePrompts(selectedIds)
      
      if (result.success) {
        toast({
          title: "批量删除成功",
          description: `成功删除 ${result.successful} 个提示词${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,
        })
        
        // 清空选择
        selectionManager.deselectAll()
        onOperationComplete()
      } else {
        toast({
          title: "批量删除失败",
          description: result.errors[0] || "删除过程中发生错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量删除失败",
        description: "操作过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsOperating(false)
    }
  }

  // 处理批量移动
  const handleBatchMove = async (targetCategoryId: string) => {
    if (!hasSelection) return

    const targetCategory = categories.find(cat => cat.id === targetCategoryId)
    const categoryName = targetCategory?.name || '未分类'
    
    const confirmMessage = getBatchOperationConfirmMessage('move', selectedCount, categoryName)
    if (!confirm(confirmMessage)) return

    setIsOperating(true)
    try {
      const result = await batchMovePrompts(selectedIds, targetCategoryId === 'none' ? null : targetCategoryId)
      
      if (result.success) {
        toast({
          title: "批量移动成功",
          description: `成功移动 ${result.successful} 个提示词到 ${categoryName}${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,
        })
        
        // 清空选择
        selectionManager.deselectAll()
        onOperationComplete()
      } else {
        toast({
          title: "批量移动失败",
          description: result.errors[0] || "移动过程中发生错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量移动失败",
        description: "操作过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsOperating(false)
    }
  }

  // 处理批量复制
  const handleBatchCopy = async () => {
    if (!hasSelection) return

    const confirmMessage = getBatchOperationConfirmMessage('copy', selectedCount)
    if (!confirm(confirmMessage)) return

    setIsOperating(true)
    try {
      const result = await batchCopyPrompts(selectedIds)
      
      if (result.success) {
        toast({
          title: "批量复制成功",
          description: `成功复制 ${result.successful} 个提示词${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,
        })
        
        // 清空选择
        selectionManager.deselectAll()
        onOperationComplete()
      } else {
        toast({
          title: "批量复制失败",
          description: result.errors[0] || "复制过程中发生错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量复制失败",
        description: "操作过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsOperating(false)
    }
  }

  // 处理批量导出
  const handleBatchExport = async () => {
    if (!hasSelection) return

    const confirmMessage = getBatchOperationConfirmMessage('export', selectedCount)
    if (!confirm(confirmMessage)) return

    setIsOperating(true)
    try {
      const result = await batchExportPrompts(selectedIds)
      
      if (result.success && result.data) {
        const filename = `batch-export-${selectedCount}-prompts-${new Date().toISOString().split('T')[0]}.json`
        downloadFile(result.data, filename, 'application/json')
        
        toast({
          title: "批量导出成功",
          description: `成功导出 ${selectedCount} 个提示词到 ${filename}`,
        })
      } else {
        toast({
          title: "批量导出失败",
          description: result.error || "导出过程中发生错误",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "批量导出失败",
        description: "操作过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsOperating(false)
    }
  }

  // 如果没有项目，不显示工具栏
  if (totalCount === 0) {
    return null
  }

  return (
    <div className={`flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border ${className}`}>
      {/* 全选复选框 */}
      <div className="flex items-center gap-2">
        <Checkbox
          checked={isAllSelected}
          ref={(el) => {
            if (el) {
              el.indeterminate = isPartiallySelected
            }
          }}
          onCheckedChange={handleSelectAll}
          disabled={isOperating}
        />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {hasSelection ? `已选择 ${selectedCount} / ${totalCount}` : `全选 (${totalCount})`}
        </span>
      </div>

      {/* 批量操作按钮 */}
      {hasSelection && (
        <>
          <div className="h-4 w-px bg-gray-300 dark:bg-gray-600" />
          
          {/* 批量删除 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleBatchDelete}
            disabled={isOperating}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            删除 ({selectedCount})
          </Button>

          {/* 批量移动 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isOperating}
              >
                <Move className="h-4 w-4 mr-1" />
                移动到
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleBatchMove('none')}>
                未分类
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {categories.map(category => (
                <DropdownMenuItem 
                  key={category.id}
                  onClick={() => handleBatchMove(category.id)}
                >
                  {category.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 批量复制 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleBatchCopy}
            disabled={isOperating}
          >
            <Copy className="h-4 w-4 mr-1" />
            复制
          </Button>

          {/* 批量导出 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleBatchExport}
            disabled={isOperating}
          >
            <Download className="h-4 w-4 mr-1" />
            导出
          </Button>

          {/* 取消选择 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => selectionManager.deselectAll()}
            disabled={isOperating}
          >
            <X className="h-4 w-4 mr-1" />
            取消选择
          </Button>
        </>
      )}

      {/* 操作状态指示 */}
      {isOperating && (
        <div className="flex items-center gap-2 text-sm text-blue-600">
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent" />
          操作中...
        </div>
      )}
    </div>
  )
}
