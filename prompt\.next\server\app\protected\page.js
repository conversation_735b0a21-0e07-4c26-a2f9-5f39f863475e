(()=>{var a={};a.id=379,a.ids=[379],a.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("D:\\Cursor Project\\prompy augment - 副本\\prompt\\node_modules\\next\\dist\\client\\app-dir\\link.js")},8034:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>l});var d=c(60687),e=c(24934),f=c(55629),g=c(21134),h=c(363),i=c(34410),j=c(10218),k=c(43210);let l=()=>{let[a,b]=(0,k.useState)(!1),{theme:c,setTheme:l}=(0,j.D)();return((0,k.useEffect)(()=>{b(!0)},[]),a)?(0,d.jsxs)(f.rI,{children:[(0,d.jsx)(f.ty,{asChild:!0,children:(0,d.jsx)(e.$,{variant:"ghost",size:"sm",children:"light"===c?(0,d.jsx)(g.A,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,d.jsx)(h.A,{size:16,className:"text-muted-foreground"},"dark"):(0,d.jsx)(i.A,{size:16,className:"text-muted-foreground"},"system")})}),(0,d.jsx)(f.SQ,{className:"w-content",align:"start",children:(0,d.jsxs)(f.Hr,{value:c,onValueChange:a=>l(a),children:[(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"light",children:[(0,d.jsx)(g.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Light"})]}),(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"dark",children:[(0,d.jsx)(h.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Dark"})]}),(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"system",children:[(0,d.jsx)(i.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"System"})]})]})})]}):null}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11811:(a,b,c)=>{"use strict";c.d(b,{Checkbox:()=>D});var d=c(60687),e=c(43210),f=c(98599),g=c(11273),h=c(70569),i=c(65551),j=c(18853),k=c(46059),l=c(14163),m="Checkbox",[n,o]=(0,g.A)(m),[p,q]=n(m);function r(a){let{__scopeCheckbox:b,checked:c,children:f,defaultChecked:g,disabled:h,form:j,name:k,onCheckedChange:l,required:n,value:o="on",internal_do_not_use_render:q}=a,[r,s]=(0,i.i)({prop:c,defaultProp:g??!1,onChange:l,caller:m}),[t,u]=e.useState(null),[v,w]=e.useState(null),x=e.useRef(!1),y=!t||!!j||!!t.closest("form"),A={checked:r,disabled:h,setChecked:s,control:t,setControl:u,name:k,form:j,value:o,hasConsumerStoppedPropagationRef:x,required:n,defaultChecked:!z(g)&&g,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,d.jsx)(p,{scope:b,...A,children:"function"==typeof q?q(A):f})}var s="CheckboxTrigger",t=e.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...g},i)=>{let{control:j,value:k,disabled:m,checked:n,required:o,setControl:p,setChecked:r,hasConsumerStoppedPropagationRef:t,isFormControl:u,bubbleInput:v}=q(s,a),w=(0,f.s)(i,p),x=e.useRef(n);return e.useEffect(()=>{let a=j?.form;if(a){let b=()=>r(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[j,r]),(0,d.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":z(n)?"mixed":n,"aria-required":o,"data-state":A(n),"data-disabled":m?"":void 0,disabled:m,value:k,...g,ref:w,onKeyDown:(0,h.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,h.m)(c,a=>{r(a=>!!z(a)||!a),v&&u&&(t.current=a.isPropagationStopped(),t.current||a.stopPropagation())})})});t.displayName=s;var u=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:e,checked:f,defaultChecked:g,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,d.jsx)(r,{__scopeCheckbox:c,checked:f,defaultChecked:g,disabled:i,required:h,onCheckedChange:k,name:e,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t,{...m,ref:b,__scopeCheckbox:c}),a&&(0,d.jsx)(y,{__scopeCheckbox:c})]})})});u.displayName=m;var v="CheckboxIndicator",w=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:e,...f}=a,g=q(v,c);return(0,d.jsx)(k.C,{present:e||z(g.checked)||!0===g.checked,children:(0,d.jsx)(l.sG.span,{"data-state":A(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b,style:{pointerEvents:"none",...a.style}})})});w.displayName=v;var x="CheckboxBubbleInput",y=e.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:g,hasConsumerStoppedPropagationRef:h,checked:i,defaultChecked:k,required:m,disabled:n,name:o,value:p,form:r,bubbleInput:s,setBubbleInput:t}=q(x,a),u=(0,f.s)(c,t),v=function(a){let b=e.useRef({value:a,previous:a});return e.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(i),w=(0,j.X)(g);e.useEffect(()=>{if(!s)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!h.current;if(v!==i&&a){let c=new Event("click",{bubbles:b});s.indeterminate=z(i),a.call(s,!z(i)&&i),s.dispatchEvent(c)}},[s,v,i,h]);let y=e.useRef(!z(i)&&i);return(0,d.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??y.current,required:m,disabled:n,name:o,value:p,form:r,...b,tabIndex:-1,ref:u,style:{...b.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(a){return"indeterminate"===a}function A(a){return z(a)?"indeterminate":a?"checked":"unchecked"}y.displayName=x;var B=c(13964),C=c(96241);let D=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(u,{ref:c,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...b,children:(0,d.jsx)(w,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})}));D.displayName=u.displayName},13755:(a,b,c)=>{"use strict";c.d(b,{CodeBlock:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\tutorial\\code-block.tsx","CodeBlock")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30125:(a,b,c)=>{"use strict";c.d(b,{CodeBlock:()=>i});var d=c(60687),e=c(43210),f=c(24934);let g=()=>(0,d.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,d.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,d.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]}),h=()=>(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,d.jsx)("polyline",{points:"20 6 9 17 4 12"})});function i({code:a}){let[b,c]=(0,e.useState)(g),i=async()=>{await navigator?.clipboard?.writeText(a),c(h),setTimeout(()=>c(g),2e3)};return(0,d.jsxs)("pre",{className:"bg-muted rounded-md p-6 my-6 relative",children:[(0,d.jsx)(f.$,{size:"icon",onClick:i,variant:"outline",className:"absolute right-2 top-2",children:b}),(0,d.jsx)("code",{className:"text-xs p-3",children:a})]})}},33116:(a,b,c)=>{Promise.resolve().then(c.bind(c,8034)),Promise.resolve().then(c.t.bind(c,85814,23))},33873:a=>{"use strict";a.exports=require("path")},39916:(a,b,c)=>{"use strict";var d=c(97576);c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41868:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["protected",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,81300)),"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,46410)),"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,38836)),"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,90253))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,59479))).default(a)],manifest:void 0}}]}.children,H=["D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\protected\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/protected/page",pathname:"/protected",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/protected/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},46410:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>av});var d=c(37413),e=c(4536),f=c.n(e),g=c(61120);function h(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var i=function(a){let b=function(a){let b=g.forwardRef((a,b)=>{let{children:c,...d}=a;if(g.isValidElement(c)){var e;let a,f,i=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==g.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=h(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():h(a[b],null)}}}}(b,i):i),g.cloneElement(c,j)}return g.Children.count(c)>1?g.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=g.forwardRef((a,c)=>{let{children:e,...f}=a,h=g.Children.toArray(e),i=h.find(k);if(i){let a=i.props.children,e=h.map(b=>b!==i?b:g.Children.count(a)>1?g.Children.only(null):g.isValidElement(a)?a.props.children:null);return(0,d.jsx)(b,{...f,ref:c,children:g.isValidElement(a)?g.cloneElement(a,void 0,e):null})}return(0,d.jsx)(b,{...f,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}("Slot"),j=Symbol("radix.slottable");function k(a){return g.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===j}function l(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}let m=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,n=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return l(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=m(b)||m(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return l(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)},o=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?o(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},p=/^\[(.+)\]$/,q=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:r(b,a)).classGroupId=c;return}if("function"==typeof a)return s(a)?void q(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{q(e,r(b,a),c,d)})})},r=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},s=a=>a.isThemeGetter,t=/\s+/;function u(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=v(a))&&(d&&(d+=" "),d+=b);return d}let v=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=v(a[d]))&&(c&&(c+=" "),c+=b);return c},w=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,y=/^\((?:(\w[\w-]*):)?(.+)\)$/i,z=/^\d+\/\d+$/,A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,B=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,C=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,D=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,F=a=>z.test(a),G=a=>!!a&&!Number.isNaN(Number(a)),H=a=>!!a&&Number.isInteger(Number(a)),I=a=>a.endsWith("%")&&G(a.slice(0,-1)),J=a=>A.test(a),K=()=>!0,L=a=>B.test(a)&&!C.test(a),M=()=>!1,N=a=>D.test(a),O=a=>E.test(a),P=a=>!R(a)&&!X(a),Q=a=>ac(a,ag,M),R=a=>x.test(a),S=a=>ac(a,ah,L),T=a=>ac(a,ai,G),U=a=>ac(a,ae,M),V=a=>ac(a,af,O),W=a=>ac(a,ak,N),X=a=>y.test(a),Y=a=>ad(a,ah),Z=a=>ad(a,aj),$=a=>ad(a,ae),_=a=>ad(a,ag),aa=a=>ad(a,af),ab=a=>ad(a,ak,!0),ac=(a,b,c)=>{let d=x.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},ad=(a,b,c=!1)=>{let d=y.exec(a);return!!d&&(d[1]?b(d[1]):c)},ae=a=>"position"===a||"percentage"===a,af=a=>"image"===a||"url"===a,ag=a=>"length"===a||"size"===a||"bg-size"===a,ah=a=>"length"===a,ai=a=>"number"===a,aj=a=>"family-name"===a,ak=a=>"shadow"===a;Symbol.toStringTag;let al=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)q(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),o(c,b)||(a=>{if(p.test(a)){let b=p.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(t),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(u.apply(null,arguments))}}(()=>{let a=w("color"),b=w("font"),c=w("text"),d=w("font-weight"),e=w("tracking"),f=w("leading"),g=w("breakpoint"),h=w("container"),i=w("spacing"),j=w("radius"),k=w("shadow"),l=w("inset-shadow"),m=w("text-shadow"),n=w("drop-shadow"),o=w("blur"),p=w("perspective"),q=w("aspect"),r=w("ease"),s=w("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),X,R],x=()=>["auto","hidden","clip","visible","scroll"],y=()=>["auto","contain","none"],z=()=>[X,R,i],A=()=>[F,"full","auto",...z()],B=()=>[H,"none","subgrid",X,R],C=()=>["auto",{span:["full",H,X,R]},H,X,R],D=()=>[H,"auto",X,R],E=()=>["auto","min","max","fr",X,R],L=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],M=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...z()],O=()=>[F,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...z()],ac=()=>[a,X,R],ad=()=>[...u(),$,U,{position:[X,R]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",_,Q,{size:[X,R]}],ag=()=>[I,Y,S],ah=()=>["","none","full",j,X,R],ai=()=>["",G,Y,S],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[G,I,$,U],am=()=>["","none",o,X,R],an=()=>["none",G,X,R],ao=()=>["none",G,X,R],ap=()=>[G,X,R],aq=()=>[F,"full",...z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[J],breakpoint:[J],color:[K],container:[J],"drop-shadow":[J],ease:["in","out","in-out"],font:[P],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[J],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[J],shadow:[J],spacing:["px",G],text:[J],"text-shadow":[J],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",F,R,X,q]}],container:["container"],columns:[{columns:[G,R,X,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:x()}],"overflow-x":[{"overflow-x":x()}],"overflow-y":[{"overflow-y":x()}],overscroll:[{overscroll:y()}],"overscroll-x":[{"overscroll-x":y()}],"overscroll-y":[{"overscroll-y":y()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[H,"auto",X,R]}],basis:[{basis:[F,"full","auto",h,...z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[G,F,"auto","initial","none",R]}],grow:[{grow:["",G,X,R]}],shrink:[{shrink:["",G,X,R]}],order:[{order:[H,"first","last","none",X,R]}],"grid-cols":[{"grid-cols":B()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":B()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:z()}],"gap-x":[{"gap-x":z()}],"gap-y":[{"gap-y":z()}],"justify-content":[{justify:[...L(),"normal"]}],"justify-items":[{"justify-items":[...M(),"normal"]}],"justify-self":[{"justify-self":["auto",...M()]}],"align-content":[{content:["normal",...L()]}],"align-items":[{items:[...M(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...M(),{baseline:["","last"]}]}],"place-content":[{"place-content":L()}],"place-items":[{"place-items":[...M(),"baseline"]}],"place-self":[{"place-self":["auto",...M()]}],p:[{p:z()}],px:[{px:z()}],py:[{py:z()}],ps:[{ps:z()}],pe:[{pe:z()}],pt:[{pt:z()}],pr:[{pr:z()}],pb:[{pb:z()}],pl:[{pl:z()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":z()}],"space-y-reverse":["space-y-reverse"],size:[{size:O()}],w:[{w:[h,"screen",...O()]}],"min-w":[{"min-w":[h,"screen","none",...O()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...O()]}],h:[{h:["screen","lh",...O()]}],"min-h":[{"min-h":["screen","lh","none",...O()]}],"max-h":[{"max-h":["screen","lh",...O()]}],"font-size":[{text:["base",c,Y,S]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,X,T]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,R]}],"font-family":[{font:[Z,R,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,X,R]}],"line-clamp":[{"line-clamp":[G,"none",X,T]}],leading:[{leading:[f,...z()]}],"list-image":[{"list-image":["none",X,R]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,R]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[G,"from-font","auto",X,S]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[G,"auto",X,R]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,R]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,R]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},H,X,R],radial:["",X,R],conic:[H,X,R]},aa,V]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[G,X,R]}],"outline-w":[{outline:["",G,Y,S]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",k,ab,W]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",l,ab,W]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[G,S]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",m,ab,W]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[G,X,R]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[G]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[X,R]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[G]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,R]}],filter:[{filter:["","none",X,R]}],blur:[{blur:am()}],brightness:[{brightness:[G,X,R]}],contrast:[{contrast:[G,X,R]}],"drop-shadow":[{"drop-shadow":["","none",n,ab,W]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",G,X,R]}],"hue-rotate":[{"hue-rotate":[G,X,R]}],invert:[{invert:["",G,X,R]}],saturate:[{saturate:[G,X,R]}],sepia:[{sepia:["",G,X,R]}],"backdrop-filter":[{"backdrop-filter":["","none",X,R]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[G,X,R]}],"backdrop-contrast":[{"backdrop-contrast":[G,X,R]}],"backdrop-grayscale":[{"backdrop-grayscale":["",G,X,R]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[G,X,R]}],"backdrop-invert":[{"backdrop-invert":["",G,X,R]}],"backdrop-opacity":[{"backdrop-opacity":[G,X,R]}],"backdrop-saturate":[{"backdrop-saturate":[G,X,R]}],"backdrop-sepia":[{"backdrop-sepia":["",G,X,R]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":z()}],"border-spacing-x":[{"border-spacing-x":z()}],"border-spacing-y":[{"border-spacing-y":z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,R]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[G,"initial",X,R]}],ease:[{ease:["linear","initial",r,X,R]}],delay:[{delay:[G,X,R]}],animate:[{animate:["none",s,X,R]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,X,R]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[X,R,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,R]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,R]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[G,Y,S,T]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function am(...a){return al(l(a))}let an=process.env.NEXT_PUBLIC_SUPABASE_URL&&process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY,ao=n("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),ap=g.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},g)=>(0,d.jsx)(e?i:"button",{className:am(ao({variant:b,size:c,className:a})),ref:g,...f}));function aq(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f(),{href:"https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png",target:"_blank",children:(0,d.jsxs)(ap,{className:"flex items-center gap-2",size:"sm",children:[(0,d.jsx)("svg",{className:"h-3 w-3",viewBox:"0 0 76 65",fill:"hsl(var(--background)/1)",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{d:"M37.5274 0L75.0548 65H0L37.5274 0Z",fill:"inherit"})}),(0,d.jsx)("span",{children:"Deploy to Vercel"})]})})})}ap.displayName="Button";let ar=n("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function as({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:am(ar({variant:b}),a),...c})}function at(){return(0,d.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,d.jsx)(as,{variant:"outline",className:"font-normal",children:"Supabase environment variables required"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(ap,{size:"sm",variant:"outline",disabled:!0,children:"Sign in"}),(0,d.jsx)(ap,{size:"sm",variant:"default",disabled:!0,children:"Sign up"})]})]})}var au=c(54656);function av({children:a}){return(0,d.jsx)("main",{className:"min-h-screen flex flex-col items-center",children:(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-20 items-center",children:[(0,d.jsx)("nav",{className:"w-full flex justify-center border-b border-b-foreground/10 h-16",children:(0,d.jsxs)("div",{className:"w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm",children:[(0,d.jsxs)("div",{className:"flex gap-5 items-center font-semibold",children:[(0,d.jsx)(f(),{href:"/",children:"Next.js Supabase Starter"}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)(aq,{})})]}),an?(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/auth-button'");throw a.code="MODULE_NOT_FOUND",a}()),{}):(0,d.jsx)(at,{})]})}),(0,d.jsx)("div",{className:"flex-1 flex flex-col gap-20 max-w-5xl p-5",children:a}),(0,d.jsxs)("footer",{className:"w-full flex items-center justify-center border-t mx-auto text-center text-xs gap-8 py-16",children:[(0,d.jsxs)("p",{children:["Powered by"," ",(0,d.jsx)("a",{href:"https://supabase.com/?utm_source=create-next-app&utm_medium=template&utm_term=nextjs",target:"_blank",className:"font-bold hover:underline",rel:"noreferrer",children:"Supabase"})]}),(0,d.jsx)(au.ThemeSwitcher,{})]})]})})}!function(){var a=Error("Cannot find module '@/components/auth-button'");throw a.code="MODULE_NOT_FOUND",a}()},46684:(a,b,c)=>{Promise.resolve().then(c.bind(c,54656)),Promise.resolve().then(c.t.bind(c,4536,23))},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},54256:(a,b,c)=>{Promise.resolve().then(c.bind(c,30125)),Promise.resolve().then(c.bind(c,11811))},54656:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\theme-switcher.tsx","ThemeSwitcher")},55629:(a,b,c)=>{"use strict";c.d(b,{Hr:()=>m,Ht:()=>p,SQ:()=>n,_2:()=>o,mB:()=>q,rI:()=>k,ty:()=>l});var d=c(60687),e=c(43210),f=c(10966),g=c(14952),h=c(13964),i=c(65822),j=c(96241);let k=f.bL,l=f.l9;f.YJ,f.ZL,f.Pb;let m=f.z6;e.forwardRef(({className:a,inset:b,children:c,...e},h)=>(0,d.jsxs)(f.ZP,{ref:h,className:(0,j.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...e,children:[c,(0,d.jsx)(g.A,{className:"ml-auto"})]})).displayName=f.ZP.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.G5,{ref:c,className:(0,j.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=f.G5.displayName;let n=e.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(f.ZL,{children:(0,d.jsx)(f.UC,{ref:e,sideOffset:b,className:(0,j.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));n.displayName=f.UC.displayName;let o=e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.q7,{ref:e,className:(0,j.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c}));o.displayName=f.q7.displayName,e.forwardRef(({className:a,children:b,checked:c,...e},g)=>(0,d.jsxs)(f.H_,{ref:g,className:(0,j.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...e,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(h.A,{className:"h-4 w-4"})})}),b]})).displayName=f.H_.displayName;let p=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.hN,{ref:e,className:(0,j.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),b]}));p.displayName=f.hN.displayName,e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.JU,{ref:e,className:(0,j.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b}));q.displayName=f.wv.displayName},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},81300:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s});var d=c(37413),e=c(39916),f=c(61120);let g=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},h=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let j=(0,f.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:g,iconNode:j,...k},l)=>(0,f.createElement)("svg",{ref:l,...i,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:h("lucide",e),...!g&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,f.createElement)(a,b)),...Array.isArray(g)?g:[g]])),k=((a,b)=>{let c=(0,f.forwardRef)(({className:c,...d},e)=>(0,f.createElement)(j,{ref:e,iconNode:b,className:h(`lucide-${g(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=g(a),c})("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var l=c(98975);function m({title:a,children:b}){return(0,d.jsxs)("li",{className:"relative",children:[(0,d.jsx)(l.Checkbox,{id:a,name:a,className:"absolute top-[3px] mr-2 peer"}),(0,d.jsxs)("label",{htmlFor:a,className:"relative text-base text-foreground peer-checked:line-through font-medium",children:[(0,d.jsx)("span",{className:"ml-8",children:a}),(0,d.jsx)("div",{className:"ml-8 text-sm peer-checked:line-through font-normal text-muted-foreground",children:b})]})]})}var n=c(13755);let o=`create table notes (
  id bigserial primary key,
  title text
);

insert into notes(title)
values
  ('Today I created a Supabase project.'),
  ('I added some data and queried it from Next.js.'),
  ('It was awesome!');
`.trim(),p=`import { createClient } from '@/utils/supabase/server'

export default async function Page() {
  const supabase = await createClient()
  const { data: notes } = await supabase.from('notes').select()

  return <pre>{JSON.stringify(notes, null, 2)}</pre>
}
`.trim(),q=`'use client'

import { createClient } from '@/utils/supabase/client'
import { useEffect, useState } from 'react'

export default function Page() {
  const [notes, setNotes] = useState<any[] | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const getData = async () => {
      const { data } = await supabase.from('notes').select()
      setNotes(data)
    }
    getData()
  }, [])

  return <pre>{JSON.stringify(notes, null, 2)}</pre>
}
`.trim();function r(){return(0,d.jsxs)("ol",{className:"flex flex-col gap-6",children:[(0,d.jsxs)(m,{title:"Create some tables and insert some data",children:[(0,d.jsxs)("p",{children:["Head over to the"," ",(0,d.jsx)("a",{href:"https://supabase.com/dashboard/project/_/editor",className:"font-bold hover:underline text-foreground/80",target:"_blank",rel:"noreferrer",children:"Table Editor"})," ","for your Supabase project to create a table and insert some example data. If you're stuck for creativity, you can copy and paste the following into the"," ",(0,d.jsx)("a",{href:"https://supabase.com/dashboard/project/_/sql/new",className:"font-bold hover:underline text-foreground/80",target:"_blank",rel:"noreferrer",children:"SQL Editor"})," ","and click RUN!"]}),(0,d.jsx)(n.CodeBlock,{code:o})]}),(0,d.jsxs)(m,{title:"Query Supabase data from Next.js",children:[(0,d.jsxs)("p",{children:["To create a Supabase client and query data from an Async Server Component, create a new page.tsx file at"," ",(0,d.jsx)("span",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-xs font-medium text-secondary-foreground border",children:"/app/notes/page.tsx"})," ","and add the following."]}),(0,d.jsx)(n.CodeBlock,{code:p}),(0,d.jsx)("p",{children:"Alternatively, you can use a Client Component."}),(0,d.jsx)(n.CodeBlock,{code:q})]}),(0,d.jsxs)(m,{title:"Explore the Supabase UI Library",children:[(0,d.jsxs)("p",{children:["Head over to the"," ",(0,d.jsx)("a",{href:"https://supabase.com/ui",className:"font-bold hover:underline text-foreground/80",children:"Supabase UI library"})," ","and try installing some blocks. For example, you can install a Realtime Chat block by running:"]}),(0,d.jsx)(n.CodeBlock,{code:"npx shadcn@latest add https://supabase.com/ui/r/realtime-chat-nextjs.json"})]}),(0,d.jsx)(m,{title:"Build in a weekend and scale to millions!",children:(0,d.jsx)("p",{children:"You're ready to launch your product to the world! \uD83D\uDE80"})})]})}async function s(){let a=await Object(function(){var a=Error("Cannot find module '@/lib/supabase/server'");throw a.code="MODULE_NOT_FOUND",a}())(),{data:b,error:c}=await a.auth.getClaims();return(c||!b?.claims)&&(0,e.redirect)("/auth/login"),(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-12",children:[(0,d.jsx)("div",{className:"w-full",children:(0,d.jsxs)("div",{className:"bg-accent text-sm p-3 px-5 rounded-md text-foreground flex gap-3 items-center",children:[(0,d.jsx)(k,{size:"16",strokeWidth:2}),"This is a protected page that you can only see as an authenticated user"]})}),(0,d.jsxs)("div",{className:"flex flex-col gap-2 items-start",children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Your user details"}),(0,d.jsx)("pre",{className:"text-xs font-mono p-3 rounded border max-h-32 overflow-auto",children:JSON.stringify(b.claims,null,2)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Next steps"}),(0,d.jsx)(r,{})]})]})}!function(){var a=Error("Cannot find module '@/lib/supabase/server'");throw a.code="MODULE_NOT_FOUND",a}()},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},96112:(a,b,c)=>{Promise.resolve().then(c.bind(c,13755)),Promise.resolve().then(c.bind(c,98975))},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},98975:(a,b,c)=>{"use strict";c.d(b,{Checkbox:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Cursor Project\\prompy augment - 副本\\prompt\\components\\ui\\checkbox.tsx","Checkbox")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,519,484,710],()=>b(b.s=41868));module.exports=c})();