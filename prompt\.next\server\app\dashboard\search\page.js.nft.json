{"version": 1, "files": ["../../../webpack-runtime.js", "../../../chunks/985.js", "../../../chunks/519.js", "../../../chunks/484.js", "../../../chunks/884.js", "../../../chunks/438.js", "../../../chunks/710.js", "../../../chunks/236.js", "../../../chunks/23.js", "page_client-reference-manifest.js", "../../../../../components/dashboard-header.tsx", "../../../../../components/ui/icon.tsx", "../../../../../components/search-bar.tsx", "../../../../../components/search-results.tsx", "../../../../../components/prompt-detail-modal.tsx", "../../../../../components/prompt-form-modal.tsx", "../../../../../components/delete-confirm-dialog.tsx", "../../../../../hooks/use-toast.ts", "../../../../../lib/api/client.ts", "../../../../../lib/database/index.ts", "../../../../../package.json", "../../../../../components/ui/card.tsx", "../../../../../lib/utils/format.ts", "../../../../../components/prompt-card.tsx", "../../../../../components/advanced-search-modal.tsx", "../../../../../components/ui/input.tsx", "../../../../../components/ui/markdown-preview.tsx", "../../../../../components/ui/dialog.tsx", "../../../../../lib/utils/clipboard.ts", "../../../../../components/ui/textarea.tsx", "../../../../../components/ui/label.tsx", "../../../../../components/ui/dropdown-menu.tsx", "../../../../../components/ui/code-block.tsx"]}