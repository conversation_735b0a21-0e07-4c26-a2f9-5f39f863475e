"use strict";exports.id=236,exports.ids=[236],exports.modules={8034:(a,b,c)=>{c.d(b,{ThemeSwitcher:()=>l});var d=c(60687),e=c(24934),f=c(55629),g=c(21134),h=c(363),i=c(34410),j=c(10218),k=c(43210);let l=()=>{let[a,b]=(0,k.useState)(!1),{theme:c,setTheme:l}=(0,j.D)();return((0,k.useEffect)(()=>{b(!0)},[]),a)?(0,d.jsxs)(f.rI,{children:[(0,d.jsx)(f.ty,{asChild:!0,children:(0,d.jsx)(e.$,{variant:"ghost",size:"sm",children:"light"===c?(0,d.jsx)(g.A,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,d.jsx)(h.A,{size:16,className:"text-muted-foreground"},"dark"):(0,d.jsx)(i.A,{size:16,className:"text-muted-foreground"},"system")})}),(0,d.jsx)(f.SQ,{className:"w-content",align:"start",children:(0,d.jsxs)(f.Hr,{value:c,onValueChange:a=>l(a),children:[(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"light",children:[(0,d.jsx)(g.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Light"})]}),(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"dark",children:[(0,d.jsx)(h.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"Dark"})]}),(0,d.jsxs)(f.Ht,{className:"flex gap-2",value:"system",children:[(0,d.jsx)(i.A,{size:16,className:"text-muted-foreground"})," ",(0,d.jsx)("span",{children:"System"})]})]})})]}):null}},24934:(a,b,c)=>{c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},32418:(a,b,c)=>{c.d(b,{I:()=>j});var d=c(60687),e=c(4161),f=c(39163),g=c(97e3);f.Yv.add(g.ao0,g.Uj9,g.jTw,g.LFz,g.e4L,g.KMJ,g.eST,g.MjD,g.QLR,g.jPR,g.MT7,g.yLS,g.pS3,g.DX_,g.qcK,g.t5Z,g.cbP,g.JmV,g.yy,g.G06,g.mRM,g.YpS,g.TJL,g.l9V,g.ITF,g.dB,g.X46,g.GxD,g.v02,g.Jt$,g.w2A,g.Wzs,g.XkK,g.GRI,g.e68,g.zpE,g.iW_,g.wRm,g.ckx,g.vaG,g.z1G,g.Vpu,g.hSh,g.U23,g.yek,g.oZK,g.gr3,g.AaJ,g.KTq,g.Iae,g.h8M,g.ruc,g.vZS,g.Cyq,g.n2W,g.XaT,g.A4h,g.okg,g.a$,g.CYF,g.Hzw,g.CQO,g.Bwz,g.DW4,g.KKb,g.V2x,g.hem,g.D6w,g.jBL,g.ArK,g.GrJ,g.w7B,g.YBv,g.fyG,g._eQ,g.nET,g.rC2,g.p1w,g.zm_,g.kNw,g.R70,g.zqi,g.iHh,g.B9e,g.LPI,g.pvD,g.s6x,g.Pcr,g.Q9Y,g.TBz,g.e5w,g.$Fj);let h={folder:"folder","folder-open":"folder-open",code:"code","pen-to-square":"pen-to-square",bullhorn:"bullhorn",rocket:"rocket","graduation-cap":"graduation-cap",search:"search",plus:"plus",copy:"copy",edit:"edit",trash:"trash",eye:"eye",tags:"tags",heart:"heart",share:"share",download:"download",upload:"upload",star:"star",bookmark:"bookmark",filter:"filter","sort-amount-down":"sort-amount-down","sort-amount-up":"sort-amount-up",grid:"grid",list:"list",cog:"cog",user:"user","sign-out-alt":"sign-out-alt",home:"home","chevron-down":"chevron-down","chevron-up":"chevron-up","chevron-left":"chevron-left","chevron-right":"chevron-right",times:"times",check:"check","exclamation-triangle":"exclamation-triangle","info-circle":"info-circle","question-circle":"question-circle",bars:"bars","ellipsis-v":"ellipsis-v",spinner:"spinner",refresh:"refresh",save:"save",undo:"undo",redo:"redo",expand:"expand",compress:"compress","external-link-alt":"external-link-alt",clipboard:"clipboard","clipboard-check":"clipboard-check",markdown:"markdown","file-code":"file-code","file-text":"file-text",image:"image",video:"video",music:"music",file:"file",calendar:"calendar",clock:"clock",hashtag:"hashtag",at:"at",link:"link",globe:"globe",lock:"lock",unlock:"unlock",shield:"shield",database:"database",server:"server",cloud:"cloud",desktop:"desktop",mobile:"mobile",tablet:"tablet",laptop:"laptop",palette:"palette","paint-brush":"paint-brush",magic:"magic",lightbulb:"lightbulb",flash:"flash",bolt:"bolt",fire:"fire",gem:"gem",crown:"crown",trophy:"trophy",medal:"medal",award:"award",bullseye:"bullseye",flag:"flag","map-marker":"map-marker",compass:"compass",route:"route",map:"map",chart:"chart-bar"};var i=c(96241);function j({name:a,className:b,size:c,spin:f=!1,pulse:g=!1,color:j}){let k=a&&h[a]?a:"question-circle";return(0,d.jsx)(e.g,{icon:h[k],className:(0,i.cn)(b),size:c,spin:f,pulse:g,color:j})}},32945:(a,b,c)=>{c.d(b,{a:()=>k});var d=c(60687),e=c(16189),f=c(85814),g=c.n(f),h=c(24934),i=c(32418),j=c(8034);function k({onCreatePrompt:a,children:b}){let c=(0,e.usePathname)(),f=[{href:"/dashboard",label:"提示词",icon:"home",active:"/dashboard"===c},{href:"/dashboard/search",label:"搜索",icon:"search",active:"/dashboard/search"===c},{href:"/dashboard/categories",label:"分类管理",icon:"folder",active:"/dashboard/categories"===c},{href:"/dashboard/stats",label:"数据统计",icon:"chart",active:"/dashboard/stats"===c}];return(0,d.jsx)("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex items-center gap-8",children:[(0,d.jsxs)(g(),{href:"/dashboard",className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg",children:(0,d.jsx)(i.I,{name:"lightbulb",className:"h-5 w-5 text-white"})}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"提示词管理"})]}),(0,d.jsx)("nav",{className:"hidden md:flex items-center gap-1",children:f.map(a=>(0,d.jsx)(g(),{href:a.href,children:(0,d.jsxs)(h.$,{variant:a.active?"secondary":"ghost",size:"sm",className:"gap-2",children:[(0,d.jsx)(i.I,{name:a.icon,className:"h-4 w-4"}),a.label]})},a.href))})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[a&&(0,d.jsxs)(h.$,{variant:"default",size:"sm",onClick:a,className:"hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200",children:[(0,d.jsx)(i.I,{name:"plus",className:"h-4 w-4 mr-2"}),"新建提示词"]}),b,(0,d.jsx)(j.ThemeSwitcher,{})]})]}),(0,d.jsx)("div",{className:"md:hidden border-t border-gray-200 dark:border-gray-700",children:(0,d.jsx)("nav",{className:"flex items-center gap-1 py-2",children:f.map(a=>(0,d.jsx)(g(),{href:a.href,className:"flex-1",children:(0,d.jsxs)(h.$,{variant:a.active?"secondary":"ghost",size:"sm",className:"w-full gap-2",children:[(0,d.jsx)(i.I,{name:a.icon,className:"h-4 w-4"}),a.label]})},a.href))})})]})})}},44655:(a,b,c)=>{function d(a,b){let c=new Date(a),d={year:"numeric",month:"short",day:"numeric",...b};return c.toLocaleDateString("zh-CN",d)}function e(a){let b=new Date(a),c=Math.floor((new Date().getTime()-b.getTime())/1e3);if(c<60)return"刚刚";let d=Math.floor(c/60);if(d<60)return`${d}分钟前`;let e=Math.floor(d/60);if(e<24)return`${e}小时前`;let f=Math.floor(e/24);if(f<7)return`${f}天前`;let g=Math.floor(f/7);if(g<4)return`${g}周前`;let h=Math.floor(f/30);if(h<12)return`${h}个月前`;let i=Math.floor(f/365);return`${i}年前`}function f(a){return"number"!=typeof a||isNaN(a)||!isFinite(a)?"0":a.toLocaleString("zh-CN")}function g(a,b,c="..."){return a.length<=b?a:a.slice(0,b-c.length)+c}function h(a){return/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(a)}c.d(b,{EJ:()=>g,Yq:()=>d,ZV:()=>f,fw:()=>e,o1:()=>h})},55629:(a,b,c)=>{c.d(b,{Hr:()=>m,Ht:()=>p,SQ:()=>n,_2:()=>o,mB:()=>q,rI:()=>k,ty:()=>l});var d=c(60687),e=c(43210),f=c(10966),g=c(14952),h=c(13964),i=c(65822),j=c(96241);let k=f.bL,l=f.l9;f.YJ,f.ZL,f.Pb;let m=f.z6;e.forwardRef(({className:a,inset:b,children:c,...e},h)=>(0,d.jsxs)(f.ZP,{ref:h,className:(0,j.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...e,children:[c,(0,d.jsx)(g.A,{className:"ml-auto"})]})).displayName=f.ZP.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.G5,{ref:c,className:(0,j.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=f.G5.displayName;let n=e.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(f.ZL,{children:(0,d.jsx)(f.UC,{ref:e,sideOffset:b,className:(0,j.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));n.displayName=f.UC.displayName;let o=e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.q7,{ref:e,className:(0,j.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c}));o.displayName=f.q7.displayName,e.forwardRef(({className:a,children:b,checked:c,...e},g)=>(0,d.jsxs)(f.H_,{ref:g,className:(0,j.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...e,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(h.A,{className:"h-4 w-4"})})}),b]})).displayName=f.H_.displayName;let p=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.hN,{ref:e,className:(0,j.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),b]}));p.displayName=f.hN.displayName,e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.JU,{ref:e,className:(0,j.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b}));q.displayName=f.wv.displayName},59821:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},89532:(a,b,c)=>{async function d(a,b={}){let c=`http://localhost:3000/api${a}`,e=await fetch(c,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!e.ok)throw Error((await e.json().catch(()=>({error:"Network error"}))).error||`HTTP ${e.status}`);return e.json()}async function e(a={}){let b=new URLSearchParams;a.query&&b.set("query",a.query),a.categoryId&&b.set("categoryId",a.categoryId),a.tagIds?.length&&b.set("tagIds",a.tagIds.join(",")),a.sortBy&&b.set("sortBy",a.sortBy),a.sortOrder&&b.set("sortOrder",a.sortOrder),a.limit&&b.set("limit",a.limit.toString()),a.offset&&b.set("offset",a.offset.toString());let c=b.toString();return d(`/prompts${c?`?${c}`:""}`)}async function f(a){return d(`/prompts/${a}`)}async function g(a,b){return d("/prompts",{method:"POST",body:JSON.stringify(b?{...a,tagIds:b}:a)})}async function h(a,b,c){return d(`/prompts/${a}`,{method:"PUT",body:JSON.stringify(c?{...b,tagIds:c}:b)})}async function i(a){await d(`/prompts/${a}`,{method:"DELETE"})}async function j(a){await d(`/prompts/${a}/usage`,{method:"POST"})}async function k(){return d("/categories")}async function l(a){return d("/categories",{method:"POST",body:JSON.stringify(a)})}async function m(a,b){return d(`/categories/${a}`,{method:"PUT",body:JSON.stringify(b)})}async function n(a){await d(`/categories/${a}`,{method:"DELETE"})}async function o(a){await d("/categories",{method:"PATCH",body:JSON.stringify(a)})}async function p(){return d("/tags")}async function q(a){return d("/tags",{method:"POST",body:JSON.stringify(a)})}async function r(a=10){return[]}async function s(a){console.log("搜索历史功能已禁用:",a)}async function t(){console.log("搜索历史功能已禁用")}async function u(a){let b={query:a.query,categoryId:a.categoryId,tagIds:a.tagIds,sortBy:a.sortBy||"updated_at",sortOrder:a.sortOrder||"desc",limit:a.limit||20,offset:a.offset||0};return(await e(b)).data}async function v(a,b){try{return(await k()).some(c=>c.name.toLowerCase()===a.toLowerCase()&&(!b||c.id!==b))}catch(a){return console.error("检查分类名称失败:",a),!1}}c.d(b,{FA:()=>r,Fw:()=>f,HK:()=>g,Ir:()=>i,K7:()=>n,Q2:()=>p,VZ:()=>q,Wf:()=>t,bW:()=>k,c1:()=>v,eQ:()=>s,jG:()=>u,oO:()=>e,pB:()=>w,qG:()=>j,qj:()=>h,st:()=>m,wp:()=>o,zZ:()=>l});let w=j}};