"use strict";exports.id=23,exports.ids=[23],exports.modules={8522:(a,b,c)=>{c.d(b,{n:()=>o});var d=c(60687),e=c(43210),f=c(49376),g=c(37826),h=c(24934),i=c(59821),j=c(32418),k=c(71702);async function l(a){try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(a),!0;return m(a)}catch(b){return console.error("复制到剪贴板失败:",b),m(a)}}function m(a){try{let b=document.createElement("textarea");b.value=a,b.style.position="fixed",b.style.left="-999999px",b.style.top="-999999px",document.body.appendChild(b),b.focus(),b.select();let c=document.execCommand("copy");return document.body.removeChild(b),c}catch(a){return console.error("降级复制方法失败:",a),!1}}var n=c(44655);function o({prompt:a,isOpen:b,onClose:c,onEdit:m,onDelete:o,onCopy:p}){let[q,r]=(0,e.useState)(!0),{toast:s}=(0,k.dj)();if(!a)return null;let t=async()=>{await l(a.content)?(p(a.content,a.id),s({title:"复制成功",description:"提示词已复制到剪贴板"})):s({title:"复制失败",description:"无法复制到剪贴板，请手动复制",variant:"destructive"})};return(0,d.jsx)(g.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(g.Cf,{className:"max-w-4xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,d.jsxs)(g.c7,{children:[(0,d.jsxs)("div",{className:"flex items-start justify-between pr-8",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)(g.L3,{className:"text-xl font-semibold mb-2",children:a.title}),a.description&&(0,d.jsx)(g.rr,{className:"text-base",children:a.description})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 ml-4 flex-wrap",children:[(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:t,className:"flex-shrink-0",children:[(0,d.jsx)(j.I,{name:"copy",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"复制"})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>{m(a.id),c()},className:"flex-shrink-0",children:[(0,d.jsx)(j.I,{name:"edit",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"编辑"})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>{o(a.id),c()},className:"text-red-600 hover:text-red-700 flex-shrink-0",children:[(0,d.jsx)(j.I,{name:"trash",className:"h-4 w-4 sm:mr-2"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"删除"})]})]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-4 pt-4 border-t",children:[a.category&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.I,{name:"folder",className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)(i.E,{variant:"secondary",style:{backgroundColor:`${a.category.color}20`,color:a.category.color},children:[(0,d.jsx)(j.I,{name:a.category.icon,className:"h-3 w-3 mr-1"}),a.category.name]})]}),a.tags&&a.tags.length>0&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.I,{name:"tags",className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.tags.map(a=>(0,d.jsx)(i.E,{variant:"outline",className:"text-xs",style:{backgroundColor:`${a.color}20`,color:a.color,borderColor:`${a.color}40`},children:a.name},a.id))})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.I,{name:"eye",className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["使用 ",a.usage_count," 次"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.I,{name:"clock",className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["创建于 ",(0,n.Yq)(a.created_at)]})]}),a.updated_at!==a.created_at&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.I,{name:"refresh",className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["更新于 ",(0,n.fw)(a.updated_at)]})]})]})]}),(0,d.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(h.$,{variant:q?"outline":"default",size:"sm",onClick:()=>r(!1),className:"relative",children:[(0,d.jsx)(j.I,{name:"file-text",className:"h-4 w-4 mr-2"}),"原始文本"]}),(0,d.jsxs)(h.$,{variant:q?"default":"outline",size:"sm",onClick:()=>r(!0),className:"relative",children:[(0,d.jsx)(j.I,{name:"eye",className:"h-4 w-4 mr-2"}),"Markdown 预览",q&&(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md",children:[(0,d.jsx)(j.I,{name:"circle-info",className:"h-3 w-3"}),(0,d.jsx)("span",{children:"支持 Markdown 格式"})]})]}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:[a.content.length," 字符"]})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto border rounded-lg p-4 bg-gray-50 dark:bg-gray-900",children:q?(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"absolute top-0 right-0 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md",children:[(0,d.jsx)(j.I,{name:"eye",className:"h-3 w-3 inline mr-1"}),"Markdown 渲染"]}),(0,d.jsx)(f.G,{content:a.content,className:"pt-6"})]}):(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"absolute top-0 right-0 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md",children:[(0,d.jsx)(j.I,{name:"file-text",className:"h-3 w-3 inline mr-1"}),"原始文本"]}),(0,d.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono pt-6",children:a.content})]})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>{console.log("导出提示词")},children:[(0,d.jsx)(j.I,{name:"download",className:"h-4 w-4 mr-2"}),"导出"]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>{console.log("分享提示词")},children:[(0,d.jsx)(j.I,{name:"share",className:"h-4 w-4 mr-2"}),"分享"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.$,{variant:"outline",onClick:c,children:"关闭"}),(0,d.jsxs)(h.$,{onClick:t,children:[(0,d.jsx)(j.I,{name:"copy",className:"h-4 w-4 mr-2"}),"复制内容"]})]})]})]})})}},15616:(a,b,c)=>{c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},32675:(a,b,c)=>{c.d(b,{I:()=>k});var d=c(60687),e=c(43210),f=c(68988),g=c(24934),h=c(32418),i=c(59821),j=c(96241);function k({value:a,onChange:b,onSearch:c,onRemoteSearch:k,placeholder:l="搜索提示词...",searchHistory:m=[],onClearHistory:n,className:o,showRemoteSearch:p=!1}){let[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(-1),u=(0,e.useRef)(null),v=(0,e.useRef)(null),w=(0,e.useRef)(null),x=(0,e.useCallback)(a=>{w.current&&clearTimeout(w.current),w.current=setTimeout(()=>{c(a)},300)},[c]),y=m.filter(b=>b?.term&&"string"==typeof b.term&&b.term.toLowerCase().includes(a.toLowerCase())&&b.term!==a).slice(0,5);return(0,d.jsxs)("div",{className:(0,j.cn)("relative w-full",o),children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.I,{name:"search",className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(f.p,{ref:u,type:"text",placeholder:l,value:a,onChange:a=>{let c=a.target.value;b(c),r(c.length>0||y.length>0),t(-1),x(c)},onFocus:()=>{r(a.length>0||y.length>0)},className:"pl-10 pr-20"}),(0,d.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1",children:[a&&(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-gray-100",onClick:()=>{b(""),r(!1),t(-1),u.current?.focus()},children:(0,d.jsx)(h.I,{name:"times",className:"h-3 w-3"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-6 px-2 text-xs hover:bg-blue-100 hover:text-blue-600",onClick:()=>{a.trim()&&(c(a.trim()),r(!1),t(-1))},disabled:!a.trim(),children:"搜索"})]})]}),q&&(y.length>0||m.length>0)&&(0,d.jsxs)("div",{ref:v,className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto",children:[y.length>0&&(0,d.jsxs)("div",{className:"p-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:"搜索历史"}),n&&(0,d.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-5 px-1 text-xs text-muted-foreground hover:text-red-600",onClick:n,children:"清除"})]}),y.map((a,e)=>(0,d.jsxs)("div",{className:(0,j.cn)("flex items-center justify-between px-2 py-1.5 rounded cursor-pointer transition-colors",s===e?"bg-blue-50 text-blue-600":"hover:bg-gray-50"),onClick:()=>{var d;b(d=a.term),c(d),r(!1),t(-1)},children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[(0,d.jsx)(h.I,{name:"clock",className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,d.jsx)("span",{className:"text-sm truncate",children:a.term})]}),(0,d.jsx)(i.E,{variant:"secondary",className:"text-xs ml-2",children:a.count})]},a.id))]}),0===y.length&&a&&(0,d.jsxs)("div",{className:"p-2",children:[(0,d.jsxs)("div",{className:"p-2 text-center text-sm text-muted-foreground",children:['正在搜索 "',a,'"...']}),p&&k&&(0,d.jsxs)("button",{className:"w-full p-2 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors",onClick:()=>{k(a),r(!1)},children:[(0,d.jsx)(h.I,{name:"search",className:"h-3 w-3 mr-2 inline"}),"在线搜索更多结果"]})]})]})]})}},37826:(a,b,c)=>{c.d(b,{Cf:()=>l,L3:()=>n,c7:()=>m,lG:()=>i,rr:()=>o});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(96241);let i=f.bL;f.l9;let j=f.ZL;f.bm;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));k.displayName=f.hJ.displayName;let l=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(j,{children:[(0,d.jsx)(k,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"关闭"})]})]})]}));l.displayName=f.UC.displayName;let m=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});m.displayName="DialogHeader";let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));n.displayName=f.hE.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));o.displayName=f.VY.displayName},39390:(a,b,c)=>{c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},49376:(a,b,c)=>{c.d(b,{G:()=>n});var d=c(60687),e=c(4127),f=c(43210),g=c(67965),h=c(35800),i=c(24934),j=c(32418),k=c(71702),l=c(96241);function m({children:a,className:b,inline:c}){let[e,m]=(0,f.useState)(!1),{toast:n}=(0,k.dj)(),o=/language-(\w+)/.exec(b||""),p=o?o[1]:"",q=async()=>{try{await navigator.clipboard.writeText(a),m(!0),n({title:"复制成功",description:"代码已复制到剪贴板"}),setTimeout(()=>m(!1),2e3)}catch(a){n({title:"复制失败",description:"无法复制到剪贴板",variant:"destructive"})}};return c?(0,d.jsx)("code",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",children:a}):(0,d.jsxs)("div",{className:"relative group",children:[(0,d.jsx)("div",{className:"absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:q,className:(0,l.cn)("h-8 px-2 bg-background/80 backdrop-blur-sm border-border/50",e&&"bg-green-100 border-green-300 text-green-700"),children:[(0,d.jsx)(j.I,{name:e?"check":"copy",className:"h-3 w-3 mr-1"}),e?"已复制":"复制"]})}),(0,d.jsx)(g.A,{style:h.A,language:p,PreTag:"div",className:"rounded-md !mt-0 !mb-0",customStyle:{margin:0,borderRadius:"0.375rem",fontSize:"0.875rem",lineHeight:"1.25rem"},children:a})]})}function n({content:a,className:b="",truncate:c=!1,maxLines:f=3}){return(0,d.jsx)("div",{className:`prose prose-sm max-w-none dark:prose-invert ${b}`,children:(0,d.jsx)(e.oz,{components:{code:({node:a,inline:b,className:e,children:f,...g})=>b?(0,d.jsx)("code",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",children:f}):c?(0,d.jsx)("pre",{className:"bg-muted rounded p-2 text-xs overflow-hidden",children:(0,d.jsx)("code",{children:String(f).replace(/\n$/,"")})}):(0,d.jsx)(m,{inline:b,className:e,...g,children:String(f).replace(/\n$/,"")}),h1:({children:a})=>c?(0,d.jsx)("span",{className:"font-bold text-base",children:a}):(0,d.jsx)("h1",{className:"text-xl font-bold mb-2",children:a}),h2:({children:a})=>c?(0,d.jsx)("span",{className:"font-semibold text-sm",children:a}):(0,d.jsx)("h2",{className:"text-lg font-semibold mb-2",children:a}),h3:({children:a})=>c?(0,d.jsx)("span",{className:"font-medium text-sm",children:a}):(0,d.jsx)("h3",{className:"text-base font-medium mb-1",children:a}),p:({children:a})=>c?(0,d.jsxs)("span",{className:"inline",children:[a," "]}):(0,d.jsx)("p",{className:"mb-2",children:a}),ul:({children:a})=>c?(0,d.jsx)("span",{className:"inline",children:a}):(0,d.jsx)("ul",{className:"list-disc list-inside mb-2",children:a}),ol:({children:a})=>c?(0,d.jsx)("span",{className:"inline",children:a}):(0,d.jsx)("ol",{className:"list-decimal list-inside mb-2",children:a}),li:({children:a})=>c?(0,d.jsxs)("span",{className:"inline",children:["• ",a," "]}):(0,d.jsx)("li",{className:"mb-1",children:a}),strong:({children:a})=>(0,d.jsx)("strong",{className:"font-semibold",children:a}),em:({children:a})=>(0,d.jsx)("em",{className:"italic",children:a}),blockquote:({children:a})=>c?(0,d.jsx)("span",{className:"inline italic",children:a}):(0,d.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic mb-2",children:a})},children:a})})}},55192:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},68988:(a,b,c)=>{c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:e,...c}));g.displayName="Input"},89904:(a,b,c)=>{c.d(b,{o:()=>h});var d=c(60687),e=c(37826),f=c(24934),g=c(32418);function h({isOpen:a,onClose:b,onConfirm:c,title:h,description:i,itemName:j,isLoading:k=!1}){return(0,d.jsx)(e.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(e.Cf,{className:"max-w-md",children:[(0,d.jsx)(e.c7,{children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-red-100 rounded-full",children:(0,d.jsx)(g.I,{name:"exclamation-triangle",className:"h-5 w-5 text-red-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)(e.L3,{className:"text-lg font-semibold",children:h}),(0,d.jsx)(e.rr,{className:"mt-1",children:i})]})]})}),j&&(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"即将删除："}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:j})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4",children:[(0,d.jsx)(f.$,{variant:"outline",onClick:b,disabled:k,children:"取消"}),(0,d.jsx)(f.$,{variant:"destructive",onClick:c,disabled:k,children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.I,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),"删除中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.I,{name:"trash",className:"h-4 w-4 mr-2"}),"确认删除"]})})]})]})})}},90890:(a,b,c)=>{c.d(b,{G:()=>o});var d=c(60687),e=c(43210),f=c(37826),g=c(24934),h=c(68988),i=c(15616),j=c(39390),k=c(59821),l=c(32418),m=c(71702),n=c(89532);function o({prompt:a,isOpen:b,onClose:c,onSuccess:o}){let[p,q]=(0,e.useState)(""),[r,s]=(0,e.useState)(""),[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)(""),[x,y]=(0,e.useState)([]),[z,A]=(0,e.useState)(""),[B,C]=(0,e.useState)([]),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)(!1),[H,I]=(0,e.useState)(!1),{toast:J}=(0,m.dj)(),K=!!a,L=async b=>{if(b.preventDefault(),!p.trim()||!t.trim())return void J({title:"表单验证失败",description:"标题和内容不能为空",variant:"destructive"});try{G(!0);let b={title:p.trim(),description:r.trim()||void 0,content:t.trim(),category_id:v||void 0};if(K&&a)await n.qj(a.id,b,x),J({title:"更新成功",description:"提示词已成功更新"});else{console.log("\uD83D\uDE80 直接创建提示词");let a=await n.HK(b,x);o&&o(a),J({title:"创建成功",description:"提示词已成功创建"})}o(),c(),q(""),s(""),u(""),w(""),y([]),A("")}catch(a){console.error("保存提示词失败:",a),J({title:"保存失败",description:K?"更新提示词时出现错误":"创建提示词时出现错误",variant:"destructive"})}finally{G(!1)}},M=async()=>{if(z.trim())try{let a=await n.VZ({name:z.trim(),color:"#6366f1"});E(b=>[...b,a]),y(b=>[...b,a.id]),A(""),J({title:"标签创建成功",description:`标签 "${a.name}" 已创建并添加`})}catch(a){console.error("创建标签失败:",a),J({title:"创建标签失败",description:"无法创建新标签",variant:"destructive"})}},N=a=>{y(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])},O=D.filter(a=>x.includes(a.id));return(0,d.jsx)(f.lG,{open:b,onOpenChange:c,children:(0,d.jsxs)(f.Cf,{className:"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col",children:[(0,d.jsxs)(f.c7,{children:[(0,d.jsx)(f.L3,{children:K?"编辑提示词":"创建新提示词"}),(0,d.jsx)(f.rr,{children:K?"修改提示词的信息和内容":"填写提示词的基本信息和内容"})]}),H?(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(l.I,{name:"spinner",className:"h-6 w-6 animate-spin mr-2"}),(0,d.jsx)("span",{children:"加载中..."})]}):(0,d.jsxs)("form",{onSubmit:L,className:"flex-1 overflow-hidden flex flex-col",children:[(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"title",children:"标题 *"}),(0,d.jsx)(h.p,{id:"title",value:p,onChange:a=>q(a.target.value),placeholder:"输入提示词标题",required:!0})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"description",children:"描述"}),(0,d.jsx)(h.p,{id:"description",value:r,onChange:a=>s(a.target.value),placeholder:"输入提示词的简短描述（可选）"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"category",children:"分类"}),(0,d.jsxs)("select",{id:"category",value:v,onChange:a=>w(a.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,d.jsx)("option",{value:"",children:"选择分类（可选）"}),B.map(a=>(0,d.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{children:"标签"}),O.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:O.map(a=>(0,d.jsxs)(k.E,{variant:"secondary",className:"cursor-pointer",style:{backgroundColor:`${a.color}20`,color:a.color},onClick:()=>N(a.id),children:[a.name,(0,d.jsx)(l.I,{name:"times",className:"h-3 w-3 ml-1"})]},a.id))}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:D.filter(a=>!x.includes(a.id)).map(a=>(0,d.jsxs)(k.E,{variant:"outline",className:"cursor-pointer hover:bg-gray-100",onClick:()=>N(a.id),children:[(0,d.jsx)(l.I,{name:"plus",className:"h-3 w-3 mr-1"}),a.name]},a.id))}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(h.p,{value:z,onChange:a=>A(a.target.value),placeholder:"创建新标签",className:"flex-1",onKeyPress:a=>{"Enter"===a.key&&(a.preventDefault(),M())}}),(0,d.jsx)(g.$,{type:"button",variant:"outline",size:"sm",onClick:M,disabled:!z.trim(),children:(0,d.jsx)(l.I,{name:"plus",className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(j.J,{htmlFor:"content",children:"内容 *"}),(0,d.jsx)(i.T,{id:"content",value:t,onChange:a=>u(a.target.value),placeholder:"输入提示词内容",className:"min-h-[200px] font-mono",required:!0}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:[t.length," 字符"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end gap-2 pt-4 border-t",children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:c,children:"取消"}),(0,d.jsx)(g.$,{type:"submit",disabled:F,children:F?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.I,{name:"spinner",className:"h-4 w-4 mr-2 animate-spin"}),K?"更新中...":"创建中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.I,{name:"save",className:"h-4 w-4 mr-2"}),K?"更新":"创建"]})})]})]})]})})}}};