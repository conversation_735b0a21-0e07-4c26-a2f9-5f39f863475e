"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface SearchHistory {
  id: string
  term: string
  count: number
  lastSearched: string
}

interface SearchBarProps {
  value: string
  onChange: (value: string) => void
  onSearch: (term: string) => void
  onRemoteSearch?: (term: string) => void // 远程搜索回调
  placeholder?: string
  searchHistory?: SearchHistory[]
  onClearHistory?: () => void
  className?: string
  showRemoteSearch?: boolean // 是否显示远程搜索选项
}

export function SearchBar({
  value,
  onChange,
  onSearch,
  onRemoteSearch,
  placeholder = "搜索提示词...",
  searchHistory = [],
  onClearHistory,
  className,
  showRemoteSearch = false
}: SearchBarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)

  // 防抖搜索函数
  const debouncedSearch = useCallback((searchTerm: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(() => {
      onSearch(searchTerm)
    }, 300) // 300ms 防抖延迟
  }, [onSearch])

  // 过滤搜索历史
  const filteredHistory = searchHistory
    .filter(item =>
      item?.term &&
      typeof item.term === 'string' &&
      item.term.toLowerCase().includes(value.toLowerCase()) &&
      item.term !== value
    )
    .slice(0, 5)

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setHighlightedIndex(prev => 
            prev < filteredHistory.length - 1 ? prev + 1 : prev
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1)
          break
        case 'Enter':
          e.preventDefault()
          if (highlightedIndex >= 0 && filteredHistory[highlightedIndex]) {
            const selectedTerm = filteredHistory[highlightedIndex].term
            onChange(selectedTerm)
            onSearch(selectedTerm)
            setIsOpen(false)
            setHighlightedIndex(-1)
          } else if (value.trim()) {
            onSearch(value.trim())
            setIsOpen(false)
            setHighlightedIndex(-1)
          }
          break
        case 'Escape':
          setIsOpen(false)
          setHighlightedIndex(-1)
          inputRef.current?.blur()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, highlightedIndex, filteredHistory])

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false)
        setHighlightedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    setIsOpen(newValue.length > 0 || filteredHistory.length > 0)
    setHighlightedIndex(-1)

    // 实时搜索：输入时触发防抖搜索
    debouncedSearch(newValue)
  }

  const handleInputFocus = () => {
    setIsOpen(value.length > 0 || filteredHistory.length > 0)
  }

  const handleSearch = () => {
    if (value.trim()) {
      onSearch(value.trim())
      setIsOpen(false)
      setHighlightedIndex(-1)
    }
  }

  const handleHistoryItemClick = (term: string) => {
    onChange(term)
    onSearch(term)
    setIsOpen(false)
    setHighlightedIndex(-1)
  }

  const handleClear = () => {
    onChange('')
    setIsOpen(false)
    setHighlightedIndex(-1)
    inputRef.current?.focus()
  }

  return (
    <div className={cn("relative w-full", className)}>
      <div className="relative">
        <Icon 
          name="search" 
          className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" 
        />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          className="pl-10 pr-20"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {value && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 hover:bg-gray-100 dark:hover:bg-gray-700"
              onClick={handleClear}
            >
              <Icon name="times" className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400"
            onClick={handleSearch}
            disabled={!value.trim()}
          >
            搜索
          </Button>
        </div>
      </div>

      {/* 搜索历史下拉框 */}
      {isOpen && (filteredHistory.length > 0 || searchHistory.length > 0) && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto"
        >
          {filteredHistory.length > 0 && (
            <div className="p-2">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-muted-foreground">搜索历史</span>
                {onClearHistory && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-1 text-xs text-muted-foreground hover:text-red-600"
                    onClick={onClearHistory}
                  >
                    清除
                  </Button>
                )}
              </div>
              {filteredHistory.map((item, index) => (
                <div
                  key={item.id}
                  className={cn(
                    "flex items-center justify-between px-2 py-1.5 rounded cursor-pointer transition-colors",
                    highlightedIndex === index
                      ? "bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400"
                      : "hover:bg-gray-50 dark:hover:bg-gray-700"
                  )}
                  onClick={() => handleHistoryItemClick(item.term)}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <Icon name="clock" className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm truncate">{item.term}</span>
                  </div>
                  <Badge variant="secondary" className="text-xs ml-2">
                    {item.count}
                  </Badge>
                </div>
              ))}
            </div>
          )}

          {filteredHistory.length === 0 && value && (
            <div className="p-2">
              <div className="p-2 text-center text-sm text-muted-foreground">
                正在搜索 &quot;{value}&quot;...
              </div>
              {showRemoteSearch && onRemoteSearch && (
                <button
                  className="w-full p-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 rounded transition-colors"
                  onClick={() => {
                    onRemoteSearch(value)
                    setIsOpen(false)
                  }}
                >
                  <Icon name="search" className="h-3 w-3 mr-2 inline" />
                  在线搜索更多结果
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
