'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Download, 
  Upload, 
  FileText, 
  Database,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { 
  exportAndDownloadJSON, 
  exportAndDownloadCSV, 
  importFromJSON,
  type ImportResult 
} from '@/lib/data-export'

interface DataImportExportModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete?: () => void
}

export function DataImportExportModal({
  open,
  onOpenChange,
  onImportComplete
}: DataImportExportModalProps) {
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [importProgress, setImportProgress] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)

  // 导出 JSON
  const handleExportJSON = async () => {
    setIsExporting(true)
    try {
      const result = await exportAndDownloadJSON()
      if (result.success) {
        toast({
          title: "导出成功",
          description: result.filename ? `数据已保存到 ${result.filename}` : "数据已下载到默认目录",
        })
      } else {
        // 如果用户取消保存，不显示错误提示
        if (result.error !== '用户取消保存') {
          toast({
            title: "导出失败",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // 导出 CSV
  const handleExportCSV = async () => {
    setIsExporting(true)
    try {
      const result = await exportAndDownloadCSV()
      if (result.success) {
        toast({
          title: "导出成功",
          description: result.filename ? `提示词已保存到 ${result.filename}` : "提示词已下载到默认目录",
        })
      } else {
        // 如果用户取消保存，不显示错误提示
        if (result.error !== '用户取消保存') {
          toast({
            title: "导出失败",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出过程中发生错误",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.dataTransfer.dropEffect = 'copy'
    setIsDragOver(true)
  }

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    // 只有当离开整个拖拽区域时才设置为 false
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      const file = files[0]
      handleFileImport(file)
    }
  }

  // 处理文件导入（支持拖拽和点击）
  const handleFileImport = async (file: File) => {
    if (!file) return

    if (!file.name.endsWith('.json')) {
      toast({
        title: "文件格式错误",
        description: "请选择 JSON 格式的文件",
        variant: "destructive",
      })
      return
    }

    setIsImporting(true)
    setImportProgress(0)
    setImportResult(null)

    try {
      const fileContent = await file.text()

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90))
      }, 100)

      const result = await importFromJSON(fileContent)

      clearInterval(progressInterval)
      setImportProgress(100)
      setImportResult(result)

      if (result.success) {
        toast({
          title: "导入成功",
          description: `成功导入 ${result.imported.prompts} 个提示词和 ${result.imported.categories} 个分类`,
        })

        // 通知父组件刷新数据
        if (onImportComplete) {
          onImportComplete()
        }
      } else {
        toast({
          title: "导入失败",
          description: "导入过程中发生错误，请查看详细信息",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "导入失败",
        description: "读取文件失败",
        variant: "destructive",
      })
    } finally {
      setIsImporting(false)
    }
  }

  // 处理文件输入变化
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileImport(file)
    }
    // 清空文件输入以允许重复选择同一文件
    event.target.value = ''
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            数据导入/导出
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="export" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              导出数据
            </TabsTrigger>
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              导入数据
            </TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    完整数据导出 (JSON)
                  </CardTitle>
                  <CardDescription>
                    导出所有提示词、分类和标签数据，可用于备份或迁移到其他设备
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={handleExportJSON}
                    disabled={isExporting}
                    className="w-full"
                  >
                    {isExporting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        导出中...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        导出 JSON 文件
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    提示词导出 (CSV)
                  </CardTitle>
                  <CardDescription>
                    仅导出提示词数据为 CSV 格式，可在 Excel 等软件中查看和编辑
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={handleExportCSV}
                    disabled={isExporting}
                    variant="outline"
                    className="w-full"
                  >
                    {isExporting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        导出中...
                      </>
                    ) : (
                      <>
                        <FileText className="mr-2 h-4 w-4" />
                        导出 CSV 文件
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  从 JSON 文件导入
                </CardTitle>
                <CardDescription>
                  选择之前导出的 JSON 文件来导入数据。重复的分类将被跳过，提示词将被添加。
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-center w-full">
                  <label
                    className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      isDragOver
                        ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-500'
                        : 'border-gray-300 bg-gray-50 hover:bg-gray-100 dark:hover:bg-gray-800 dark:bg-gray-700 dark:border-gray-600'
                    } ${isImporting ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onDragOver={handleDragOver}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className={`w-8 h-8 mb-4 ${
                        isDragOver
                          ? 'text-blue-500 dark:text-blue-400'
                          : 'text-gray-500 dark:text-gray-400'
                      }`} />
                      <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                        <span className="font-semibold">
                          {isDragOver ? '释放文件到此处' : '点击选择文件'}
                        </span>
                        {!isDragOver && ' 或拖拽到此处'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        仅支持 JSON 格式文件
                      </p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".json"
                      onChange={handleFileInputChange}
                      disabled={isImporting}
                    />
                  </label>
                </div>

                {isImporting && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">导入中...</span>
                    </div>
                    <Progress value={importProgress} className="w-full" />
                  </div>
                )}

                {importResult && (
                  <div className="space-y-3">
                    <Alert className={importResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                      <div className="flex items-center gap-2">
                        {importResult.success ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        )}
                        <AlertDescription className={importResult.success ? "text-green-800" : "text-red-800"}>
                          {importResult.success ? "导入成功！" : "导入失败"}
                        </AlertDescription>
                      </div>
                    </Alert>

                    {importResult.success && (
                      <div className="text-sm space-y-1">
                        <p>✅ 导入了 {importResult.imported.prompts} 个提示词</p>
                        <p>✅ 导入了 {importResult.imported.categories} 个分类</p>
                      </div>
                    )}

                    {importResult.warnings.length > 0 && (
                      <div className="text-sm space-y-1">
                        <p className="font-medium text-yellow-600">警告:</p>
                        {importResult.warnings.map((warning, index) => (
                          <p key={index} className="text-yellow-600">⚠️ {warning}</p>
                        ))}
                      </div>
                    )}

                    {importResult.errors.length > 0 && (
                      <div className="text-sm space-y-1">
                        <p className="font-medium text-red-600">错误:</p>
                        {importResult.errors.map((error, index) => (
                          <p key={index} className="text-red-600">❌ {error}</p>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
