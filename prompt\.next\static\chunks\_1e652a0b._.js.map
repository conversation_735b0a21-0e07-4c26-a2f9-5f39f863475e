{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/fontawesome.ts"], "sourcesContent": ["// Font Awesome 配置\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport {\n  faFolder,\n  faFolderOpen,\n  faCode,\n  faPenToSquare,\n  faBullhorn,\n  faRocket,\n  faGraduationCap,\n  faSearch,\n  faPlus,\n  faCopy,\n  faEdit,\n  faTrash,\n  faEye,\n  faTags,\n  faHeart,\n  faShare,\n  faDownload,\n  faUpload,\n  faStar,\n  faBookmark,\n  faFilter,\n  faSortAmountDown,\n  faSortAmountUp,\n  faThLarge as faGrid,\n  faList,\n  faCog,\n  faUser,\n  faSignOutAlt,\n  faHome,\n  faChevronDown,\n  faChevronUp,\n  faChevronLeft,\n  faChevronRight,\n  faTimes,\n  faCheck,\n  faExclamationTriangle,\n  faInfoCircle,\n  faQuestionCircle,\n  faBars,\n  faEllipsisV,\n  faSpinner,\n  faRefresh,\n  faSave,\n  faUndo,\n  faRedo,\n  faExpand,\n  faCompress,\n  faExternalLinkAlt,\n  faClipboard,\n  faClipboardCheck,\n  faFileAlt as faMarkdown,\n  faFileCode,\n  faFileText,\n  faImage,\n  faVideo,\n  faMusic,\n  faFile,\n  faCalendar,\n  faClock,\n  faHashtag,\n  faAt,\n  faLink,\n  faGlobe,\n  faLock,\n  faUnlock,\n  faShield,\n  faDatabase,\n  faServer,\n  faCloud,\n  faDesktop,\n  faMobile,\n  faTablet,\n  faLaptop,\n  faPalette,\n  faPaintBrush,\n  faMagic,\n  faLightbulb,\n  faZap as faFlash,\n  faBolt,\n  faFire,\n  faGem,\n  faCrown,\n  faTrophy,\n  faMedal,\n  faAward,\n  faBullseye,\n  faFlag,\n  faMapMarkerAlt as faMapMarker,\n  faCompass,\n  faRoute,\n  faMap,\n  faChartBar,\n  faThLarge,\n  faFileAlt,\n  faZap,\n  faMapMarkerAlt\n} from '@fortawesome/free-solid-svg-icons'\n\n// 添加图标到库中\nlibrary.add(\n  faFolder,\n  faFolderOpen,\n  faCode,\n  faPenToSquare,\n  faBullhorn,\n  faRocket,\n  faGraduationCap,\n  faSearch,\n  faPlus,\n  faCopy,\n  faEdit,\n  faTrash,\n  faEye,\n  faTags,\n  faHeart,\n  faShare,\n  faDownload,\n  faUpload,\n  faStar,\n  faBookmark,\n  faFilter,\n  faSortAmountDown,\n  faSortAmountUp,\n  faGrid,\n  faList,\n  faCog,\n  faUser,\n  faSignOutAlt,\n  faHome,\n  faChevronDown,\n  faChevronUp,\n  faChevronLeft,\n  faChevronRight,\n  faTimes,\n  faCheck,\n  faExclamationTriangle,\n  faInfoCircle,\n  faQuestionCircle,\n  faBars,\n  faEllipsisV,\n  faSpinner,\n  faRefresh,\n  faSave,\n  faUndo,\n  faRedo,\n  faExpand,\n  faCompress,\n  faExternalLinkAlt,\n  faClipboard,\n  faClipboardCheck,\n  faMarkdown,\n  faFileCode,\n  faFileText,\n  faImage,\n  faVideo,\n  faMusic,\n  faFile,\n  faCalendar,\n  faClock,\n  faHashtag,\n  faAt,\n  faLink,\n  faGlobe,\n  faLock,\n  faUnlock,\n  faShield,\n  faDatabase,\n  faServer,\n  faCloud,\n  faDesktop,\n  faMobile,\n  faTablet,\n  faLaptop,\n  faPalette,\n  faPaintBrush,\n  faMagic,\n  faLightbulb,\n  faFlash,\n  faBolt,\n  faFire,\n  faGem,\n  faCrown,\n  faTrophy,\n  faMedal,\n  faAward,\n  faBullseye,\n  faFlag,\n  faMapMarker,\n  faCompass,\n  faRoute,\n  faMap,\n  faChartBar\n)\n\n// 导出常用图标名称映射\nexport const iconMap = {\n  // 分类图标\n  folder: 'folder',\n  'folder-open': 'folder-open',\n  code: 'code',\n  'pen-to-square': 'pen-to-square',\n  bullhorn: 'bullhorn',\n  rocket: 'rocket',\n  'graduation-cap': 'graduation-cap',\n  \n  // 操作图标\n  search: 'search',\n  plus: 'plus',\n  copy: 'copy',\n  edit: 'edit',\n  trash: 'trash',\n  eye: 'eye',\n  tags: 'tags',\n  heart: 'heart',\n  share: 'share',\n  download: 'download',\n  upload: 'upload',\n  star: 'star',\n  bookmark: 'bookmark',\n  \n  // 界面图标\n  filter: 'filter',\n  'sort-amount-down': 'sort-amount-down',\n  'sort-amount-up': 'sort-amount-up',\n  grid: 'grid',\n  list: 'list',\n  cog: 'cog',\n  user: 'user',\n  'sign-out-alt': 'sign-out-alt',\n  home: 'home',\n  \n  // 导航图标\n  'chevron-down': 'chevron-down',\n  'chevron-up': 'chevron-up',\n  'chevron-left': 'chevron-left',\n  'chevron-right': 'chevron-right',\n  times: 'times',\n  check: 'check',\n  \n  // 状态图标\n  'exclamation-triangle': 'exclamation-triangle',\n  'info-circle': 'info-circle',\n  'question-circle': 'question-circle',\n  bars: 'bars',\n  'ellipsis-v': 'ellipsis-v',\n  spinner: 'spinner',\n  refresh: 'refresh',\n  \n  // 编辑图标\n  save: 'save',\n  undo: 'undo',\n  redo: 'redo',\n  expand: 'expand',\n  compress: 'compress',\n  'external-link-alt': 'external-link-alt',\n  clipboard: 'clipboard',\n  'clipboard-check': 'clipboard-check',\n  \n  // 文件类型图标\n  markdown: 'markdown',\n  'file-code': 'file-code',\n  'file-text': 'file-text',\n  image: 'image',\n  video: 'video',\n  music: 'music',\n  file: 'file',\n  \n  // 时间图标\n  calendar: 'calendar',\n  clock: 'clock',\n  \n  // 社交图标\n  hashtag: 'hashtag',\n  at: 'at',\n  link: 'link',\n  globe: 'globe',\n  \n  // 安全图标\n  lock: 'lock',\n  unlock: 'unlock',\n  shield: 'shield',\n  \n  // 技术图标\n  database: 'database',\n  server: 'server',\n  cloud: 'cloud',\n  desktop: 'desktop',\n  mobile: 'mobile',\n  tablet: 'tablet',\n  laptop: 'laptop',\n  \n  // 设计图标\n  palette: 'palette',\n  'paint-brush': 'paint-brush',\n  magic: 'magic',\n  \n  // 创意图标\n  lightbulb: 'lightbulb',\n  flash: 'flash',\n  bolt: 'bolt',\n  fire: 'fire',\n  gem: 'gem',\n  \n  // 成就图标\n  crown: 'crown',\n  trophy: 'trophy',\n  medal: 'medal',\n  award: 'award',\n  bullseye: 'bullseye',\n  \n  // 位置图标\n  flag: 'flag',\n  'map-marker': 'map-marker',\n  compass: 'compass',\n  route: 'route',\n  map: 'map',\n\n  // 图表图标\n  chart: 'chart-bar'\n} as const\n\nexport type IconName = keyof typeof iconMap\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;AAClB;AACA;;;AAmGA,UAAU;AACV,wKAAA,CAAA,UAAO,CAAC,GAAG,CACT,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,kBAAe,EACf,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,iBAAc,EACd,2KAAA,CAAA,YAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,gBAAa,EACb,2KAAA,CAAA,iBAAc,EACd,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,wBAAqB,EACrB,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,oBAAiB,EACjB,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,mBAAgB,EAChB,2KAAA,CAAA,YAAU,EACV,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,OAAI,EACJ,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,eAAY,EACZ,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,cAAW,EACX,2KAAA,CAAA,QAAO,EACP,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,WAAQ,EACR,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,aAAU,EACV,2KAAA,CAAA,SAAM,EACN,2KAAA,CAAA,iBAAW,EACX,2KAAA,CAAA,YAAS,EACT,2KAAA,CAAA,UAAO,EACP,2KAAA,CAAA,QAAK,EACL,2KAAA,CAAA,aAAU;AAIL,MAAM,UAAU;IACrB,OAAO;IACP,QAAQ;IACR,eAAe;IACf,MAAM;IACN,iBAAiB;IACjB,UAAU;IACV,QAAQ;IACR,kBAAkB;IAElB,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;IACR,MAAM;IACN,UAAU;IAEV,OAAO;IACP,QAAQ;IACR,oBAAoB;IACpB,kBAAkB;IAClB,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,gBAAgB;IAChB,MAAM;IAEN,OAAO;IACP,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,OAAO;IACP,OAAO;IAEP,OAAO;IACP,wBAAwB;IACxB,eAAe;IACf,mBAAmB;IACnB,MAAM;IACN,cAAc;IACd,SAAS;IACT,SAAS;IAET,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,qBAAqB;IACrB,WAAW;IACX,mBAAmB;IAEnB,SAAS;IACT,UAAU;IACV,aAAa;IACb,aAAa;IACb,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IAEN,OAAO;IACP,UAAU;IACV,OAAO;IAEP,OAAO;IACP,SAAS;IACT,IAAI;IACJ,MAAM;IACN,OAAO;IAEP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IAER,OAAO;IACP,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IAER,OAAO;IACP,SAAS;IACT,eAAe;IACf,OAAO;IAEP,OAAO;IACP,WAAW;IACX,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IAEL,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IAEV,OAAO;IACP,MAAM;IACN,cAAc;IACd,SAAS;IACT,OAAO;IACP,KAAK;IAEL,OAAO;IACP,OAAO;AACT", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/icon.tsx"], "sourcesContent": ["\"use client\"\n\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome'\nimport { IconName, iconMap } from '@/lib/fontawesome'\nimport { cn } from '@/lib/utils'\n\ninterface IconProps {\n  name: IconName\n  className?: string\n  size?: 'xs' | 'sm' | 'lg' | 'xl' | '2xl'\n  spin?: boolean\n  pulse?: boolean\n  color?: string\n}\n\nexport function Icon({\n  name,\n  className,\n  size,\n  spin = false,\n  pulse = false,\n  color\n}: IconProps) {\n  // 如果 name 为 undefined 或不存在于 iconMap 中，使用默认图标\n  const iconName = name && iconMap[name] ? name : 'question-circle'\n\n  return (\n    <FontAwesomeIcon\n      icon={iconMap[iconName] as any}\n      className={cn(className)}\n      size={size}\n      spin={spin}\n      pulse={pulse}\n      color={color}\n    />\n  )\n}\n\nexport type { IconName }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeO,SAAS,KAAK,KAOT;QAPS,EACnB,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,OAAO,KAAK,EACZ,QAAQ,KAAK,EACb,KAAK,EACK,GAPS;IAQnB,6CAA6C;IAC7C,MAAM,WAAW,QAAQ,qHAAA,CAAA,UAAO,CAAC,KAAK,GAAG,OAAO;IAEhD,qBACE,6LAAC,uKAAA,CAAA,kBAAe;QACd,MAAM,qHAAA,CAAA,UAAO,CAAC,SAAS;QACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;QACd,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;;;;;;AAGb;KArBgB", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Icon, IconName } from '@/components/ui/icon'\nimport { Badge } from '@/components/ui/badge'\nimport { cn } from '@/lib/utils'\nimport type { CategoryWithCount } from '@/types/database'\n\ninterface SidebarCategory {\n  id: string\n  name: string\n  description?: string\n  color: string\n  icon: IconName\n  count: number\n  sortOrder: number\n}\n\ninterface SidebarProps {\n  categories: CategoryWithCount[]\n  selectedCategoryId?: string\n  onCategorySelect: (categoryId: string | undefined) => void\n  onCategoryCreate: () => void\n  onCategoryEdit: (categoryId: string) => void\n  onCategoryDelete: (categoryId: string) => void\n  isCollapsed?: boolean\n  onToggleCollapse?: () => void\n  className?: string\n}\n\nexport function Sidebar({\n  categories,\n  selectedCategoryId,\n  onCategorySelect,\n  onCategoryCreate,\n  onCategoryEdit,\n  onCategoryDelete,\n  isCollapsed = false,\n  onToggleCollapse,\n  className\n}: SidebarProps) {\n  const [hoveredCategoryId, setHoveredCategoryId] = useState<string | null>(null)\n\n  // 按排序顺序排列分类\n  const sortedCategories = [...categories].sort((a, b) => a.sort_order - b.sort_order)\n\n  // 计算总提示词数量 - 使用安全的数字处理避免 NaN\n  const totalCount = categories.reduce((sum, cat) => sum + (cat.prompt_count || 0), 0)\n\n  return (\n    <div className={cn(\n      \"flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300\",\n      isCollapsed ? \"w-16\" : \"w-64\",\n      \"md:relative absolute md:translate-x-0 z-30\",\n      isCollapsed ? \"md:w-16 w-0 -translate-x-full\" : \"md:w-64 w-64 translate-x-0\",\n      className\n    )}>\n      {/* 头部 */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n        {!isCollapsed && (\n          <div>\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">分类</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              {totalCount} 个提示词\n            </p>\n          </div>\n        )}\n        {onToggleCollapse && (\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-8 w-8\"\n            onClick={onToggleCollapse}\n          >\n            <Icon \n              name={isCollapsed ? \"chevron-right\" : \"chevron-left\"} \n              className=\"h-4 w-4\" \n            />\n          </Button>\n        )}\n      </div>\n\n      {/* 全部分类 */}\n      <div className=\"p-2\">\n        <Button\n          variant={selectedCategoryId === undefined ? \"secondary\" : \"ghost\"}\n          className={cn(\n            \"w-full justify-start gap-3 h-10\",\n            isCollapsed && \"justify-center px-2\"\n          )}\n          onClick={() => onCategorySelect(undefined)}\n        >\n          <Icon name=\"home\" className=\"h-4 w-4 flex-shrink-0\" />\n          {!isCollapsed && (\n            <>\n              <span className=\"flex-1 text-left\">全部</span>\n              <Badge variant=\"secondary\" className=\"ml-auto\">\n                {totalCount}\n              </Badge>\n            </>\n          )}\n        </Button>\n      </div>\n\n      {/* 分类列表 */}\n      <div className=\"flex-1 overflow-y-auto p-2 space-y-1\">\n        {sortedCategories.map((category) => (\n          <div\n            key={category.id}\n            className=\"relative group\"\n            onMouseEnter={() => setHoveredCategoryId(category.id)}\n            onMouseLeave={() => setHoveredCategoryId(null)}\n          >\n            <Button\n              variant={selectedCategoryId === category.id ? \"secondary\" : \"ghost\"}\n              className={cn(\n                \"w-full justify-start gap-3 h-10 relative\",\n                isCollapsed && \"justify-center px-2\"\n              )}\n              onClick={() => onCategorySelect(category.id)}\n            >\n              <Icon\n                name={category.icon || 'folder'}\n                className=\"h-4 w-4 flex-shrink-0\"\n                color={category.color}\n              />\n              {!isCollapsed && (\n                <>\n                  <span className=\"flex-1 text-left truncate\">\n                    {category.name}\n                  </span>\n                  <Badge\n                    variant=\"secondary\"\n                    className={cn(\n                      \"ml-auto transition-opacity duration-200\",\n                      hoveredCategoryId === category.id ? \"opacity-0\" : \"opacity-100\"\n                    )}\n                    style={{\n                      backgroundColor: `${category.color}20`,\n                      color: category.color\n                    }}\n                  >\n                    {category.prompt_count || 0}\n                  </Badge>\n                </>\n              )}\n            </Button>\n\n            {/* 悬停时显示的操作按钮 */}\n            {!isCollapsed && hoveredCategoryId === category.id && (\n              <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1 bg-white dark:bg-gray-700 rounded shadow-sm border border-gray-200 dark:border-gray-600\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-6 w-6 hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    onCategoryEdit(category.id)\n                  }}\n                >\n                  <Icon name=\"edit\" className=\"h-3 w-3\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"h-6 w-6 hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600 dark:hover:text-red-400\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    onCategoryDelete(category.id)\n                  }}\n                >\n                  <Icon name=\"trash\" className=\"h-3 w-3\" />\n                </Button>\n              </div>\n            )}\n\n            {/* 折叠状态下的工具提示 */}\n            {isCollapsed && (\n              <div className=\"absolute left-full top-0 ml-2 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\">\n                {category.name} ({category.prompt_count || 0})\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* 底部操作 */}\n      <div className=\"p-2 border-t border-gray-200 dark:border-gray-700\">\n        <Button\n          variant=\"outline\"\n          className={cn(\n            \"w-full gap-2\",\n            isCollapsed && \"justify-center px-2\"\n          )}\n          onClick={onCategoryCreate}\n        >\n          <Icon name=\"plus\" className=\"h-4 w-4\" />\n          {!isCollapsed && \"新建分类\"}\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA+BO,SAAS,QAAQ,KAUT;QAVS,EACtB,UAAU,EACV,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,cAAc,KAAK,EACnB,gBAAgB,EAChB,SAAS,EACI,GAVS;;IAWtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,YAAY;IACZ,MAAM,mBAAmB;WAAI;KAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAEnF,6BAA6B;IAC7B,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,YAAY,IAAI,CAAC,GAAG;IAElF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,4HACA,cAAc,SAAS,QACvB,8CACA,cAAc,kCAAkC,8BAChD;;0BAGA,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CACpE,6LAAC;gCAAE,WAAU;;oCACV;oCAAW;;;;;;;;;;;;;oBAIjB,kCACC,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,4HAAA,CAAA,OAAI;4BACH,MAAM,cAAc,kBAAkB;4BACtC,WAAU;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAS,uBAAuB,YAAY,cAAc;oBAC1D,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mCACA,eAAe;oBAEjB,SAAS,IAAM,iBAAiB;;sCAEhC,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAO,WAAU;;;;;;wBAC3B,CAAC,6BACA;;8CACE,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;;;;;;;;;;;;;;0BAQX,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;wBAEC,WAAU;wBACV,cAAc,IAAM,qBAAqB,SAAS,EAAE;wBACpD,cAAc,IAAM,qBAAqB;;0CAEzC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS,uBAAuB,SAAS,EAAE,GAAG,cAAc;gCAC5D,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,eAAe;gCAEjB,SAAS,IAAM,iBAAiB,SAAS,EAAE;;kDAE3C,6LAAC,4HAAA,CAAA,OAAI;wCACH,MAAM,SAAS,IAAI,IAAI;wCACvB,WAAU;wCACV,OAAO,SAAS,KAAK;;;;;;oCAEtB,CAAC,6BACA;;0DACE,6LAAC;gDAAK,WAAU;0DACb,SAAS,IAAI;;;;;;0DAEhB,6LAAC,6HAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2CACA,sBAAsB,SAAS,EAAE,GAAG,cAAc;gDAEpD,OAAO;oDACL,iBAAiB,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;oDACnC,OAAO,SAAS,KAAK;gDACvB;0DAEC,SAAS,YAAY,IAAI;;;;;;;;;;;;;;4BAOjC,CAAC,eAAe,sBAAsB,SAAS,EAAE,kBAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,eAAe,SAAS,EAAE;wCAC5B;kDAEA,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;;;;;;kDAE9B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,iBAAiB,SAAS,EAAE;wCAC9B;kDAEA,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;;;;;;;;;;;;4BAMlC,6BACC,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,IAAI;oCAAC;oCAAG,SAAS,YAAY,IAAI;oCAAE;;;;;;;;uBAvE5C,SAAS,EAAE;;;;;;;;;;0BA+EtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gBACA,eAAe;oBAEjB,SAAS;;sCAET,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAO,WAAU;;;;;;wBAC3B,CAAC,eAAe;;;;;;;;;;;;;;;;;;AAK3B;GA5KgB;KAAA", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/search-bar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef, useCallback } from 'react'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { Badge } from '@/components/ui/badge'\nimport { cn } from '@/lib/utils'\n\ninterface SearchHistory {\n  id: string\n  term: string\n  count: number\n  lastSearched: string\n}\n\ninterface SearchBarProps {\n  value: string\n  onChange: (value: string) => void\n  onSearch: (term: string) => void\n  onRemoteSearch?: (term: string) => void // 远程搜索回调\n  placeholder?: string\n  searchHistory?: SearchHistory[]\n  onClearHistory?: () => void\n  className?: string\n  showRemoteSearch?: boolean // 是否显示远程搜索选项\n}\n\nexport function SearchBar({\n  value,\n  onChange,\n  onSearch,\n  onRemoteSearch,\n  placeholder = \"搜索提示词...\",\n  searchHistory = [],\n  onClearHistory,\n  className,\n  showRemoteSearch = false\n}: SearchBarProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [highlightedIndex, setHighlightedIndex] = useState(-1)\n  const inputRef = useRef<HTMLInputElement>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)\n\n  // 防抖搜索函数\n  const debouncedSearch = useCallback((searchTerm: string) => {\n    if (debounceTimerRef.current) {\n      clearTimeout(debounceTimerRef.current)\n    }\n\n    debounceTimerRef.current = setTimeout(() => {\n      onSearch(searchTerm)\n    }, 300) // 300ms 防抖延迟\n  }, [onSearch])\n\n  // 过滤搜索历史\n  const filteredHistory = searchHistory\n    .filter(item =>\n      item?.term &&\n      typeof item.term === 'string' &&\n      item.term.toLowerCase().includes(value.toLowerCase()) &&\n      item.term !== value\n    )\n    .slice(0, 5)\n\n  // 处理键盘事件\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!isOpen) return\n\n      switch (e.key) {\n        case 'ArrowDown':\n          e.preventDefault()\n          setHighlightedIndex(prev => \n            prev < filteredHistory.length - 1 ? prev + 1 : prev\n          )\n          break\n        case 'ArrowUp':\n          e.preventDefault()\n          setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1)\n          break\n        case 'Enter':\n          e.preventDefault()\n          if (highlightedIndex >= 0 && filteredHistory[highlightedIndex]) {\n            const selectedTerm = filteredHistory[highlightedIndex].term\n            onChange(selectedTerm)\n            onSearch(selectedTerm)\n            setIsOpen(false)\n            setHighlightedIndex(-1)\n          } else if (value.trim()) {\n            onSearch(value.trim())\n            setIsOpen(false)\n            setHighlightedIndex(-1)\n          }\n          break\n        case 'Escape':\n          setIsOpen(false)\n          setHighlightedIndex(-1)\n          inputRef.current?.blur()\n          break\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isOpen, highlightedIndex, filteredHistory])\n\n  // 点击外部关闭下拉框\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current && \n        !dropdownRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setIsOpen(false)\n        setHighlightedIndex(-1)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      // 清理防抖定时器\n      if (debounceTimerRef.current) {\n        clearTimeout(debounceTimerRef.current)\n      }\n    }\n  }, [])\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value\n    onChange(newValue)\n    setIsOpen(newValue.length > 0 || filteredHistory.length > 0)\n    setHighlightedIndex(-1)\n\n    // 实时搜索：输入时触发防抖搜索\n    debouncedSearch(newValue)\n  }\n\n  const handleInputFocus = () => {\n    setIsOpen(value.length > 0 || filteredHistory.length > 0)\n  }\n\n  const handleSearch = () => {\n    if (value.trim()) {\n      onSearch(value.trim())\n      setIsOpen(false)\n      setHighlightedIndex(-1)\n    }\n  }\n\n  const handleHistoryItemClick = (term: string) => {\n    onChange(term)\n    onSearch(term)\n    setIsOpen(false)\n    setHighlightedIndex(-1)\n  }\n\n  const handleClear = () => {\n    onChange('')\n    setIsOpen(false)\n    setHighlightedIndex(-1)\n    inputRef.current?.focus()\n  }\n\n  return (\n    <div className={cn(\"relative w-full\", className)}>\n      <div className=\"relative\">\n        <Icon \n          name=\"search\" \n          className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" \n        />\n        <Input\n          ref={inputRef}\n          type=\"text\"\n          placeholder={placeholder}\n          value={value}\n          onChange={handleInputChange}\n          onFocus={handleInputFocus}\n          className=\"pl-10 pr-20\"\n        />\n        <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\">\n          {value && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-6 w-6 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              onClick={handleClear}\n            >\n              <Icon name=\"times\" className=\"h-3 w-3\" />\n            </Button>\n          )}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"h-6 px-2 text-xs hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400\"\n            onClick={handleSearch}\n            disabled={!value.trim()}\n          >\n            搜索\n          </Button>\n        </div>\n      </div>\n\n      {/* 搜索历史下拉框 */}\n      {isOpen && (filteredHistory.length > 0 || searchHistory.length > 0) && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto\"\n        >\n          {filteredHistory.length > 0 && (\n            <div className=\"p-2\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-xs font-medium text-muted-foreground\">搜索历史</span>\n                {onClearHistory && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-5 px-1 text-xs text-muted-foreground hover:text-red-600\"\n                    onClick={onClearHistory}\n                  >\n                    清除\n                  </Button>\n                )}\n              </div>\n              {filteredHistory.map((item, index) => (\n                <div\n                  key={item.id}\n                  className={cn(\n                    \"flex items-center justify-between px-2 py-1.5 rounded cursor-pointer transition-colors\",\n                    highlightedIndex === index\n                      ? \"bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400\"\n                      : \"hover:bg-gray-50 dark:hover:bg-gray-700\"\n                  )}\n                  onClick={() => handleHistoryItemClick(item.term)}\n                >\n                  <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n                    <Icon name=\"clock\" className=\"h-3 w-3 text-muted-foreground flex-shrink-0\" />\n                    <span className=\"text-sm truncate\">{item.term}</span>\n                  </div>\n                  <Badge variant=\"secondary\" className=\"text-xs ml-2\">\n                    {item.count}\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {filteredHistory.length === 0 && value && (\n            <div className=\"p-2\">\n              <div className=\"p-2 text-center text-sm text-muted-foreground\">\n                正在搜索 &quot;{value}&quot;...\n              </div>\n              {showRemoteSearch && onRemoteSearch && (\n                <button\n                  className=\"w-full p-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900 rounded transition-colors\"\n                  onClick={() => {\n                    onRemoteSearch(value)\n                    setIsOpen(false)\n                  }}\n                >\n                  <Icon name=\"search\" className=\"h-3 w-3 mr-2 inline\" />\n                  在线搜索更多结果\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4BO,SAAS,UAAU,KAUT;QAVS,EACxB,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,cAAc,UAAU,EACxB,gBAAgB,EAAE,EAClB,cAAc,EACd,SAAS,EACT,mBAAmB,KAAK,EACT,GAVS;;IAWxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACnC,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,iBAAiB,OAAO,GAAG;0DAAW;oBACpC,SAAS;gBACX;yDAAG,MAAK,aAAa;QACvB;iDAAG;QAAC;KAAS;IAEb,SAAS;IACT,MAAM,kBAAkB,cACrB,MAAM,CAAC,CAAA,OACN,CAAA,iBAAA,2BAAA,KAAM,IAAI,KACV,OAAO,KAAK,IAAI,KAAK,YACrB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAClD,KAAK,IAAI,KAAK,OAEf,KAAK,CAAC,GAAG;IAEZ,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;qDAAgB,CAAC;oBACrB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,EAAE,GAAG;wBACX,KAAK;4BACH,EAAE,cAAc;4BAChB;qEAAoB,CAAA,OAClB,OAAO,gBAAgB,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAEjD;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB;qEAAoB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;;4BACnD;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB,IAAI,oBAAoB,KAAK,eAAe,CAAC,iBAAiB,EAAE;gCAC9D,MAAM,eAAe,eAAe,CAAC,iBAAiB,CAAC,IAAI;gCAC3D,SAAS;gCACT,SAAS;gCACT,UAAU;gCACV,oBAAoB,CAAC;4BACvB,OAAO,IAAI,MAAM,IAAI,IAAI;gCACvB,SAAS,MAAM,IAAI;gCACnB,UAAU;gCACV,oBAAoB,CAAC;4BACvB;4BACA;wBACF,KAAK;gCAGH;4BAFA,UAAU;4BACV,oBAAoB,CAAC;6BACrB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,IAAI;4BACtB;oBACJ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;uCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;8BAAG;QAAC;QAAQ;QAAkB;KAAgB;IAE9C,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;0DAAqB,CAAC;wBAIvB;oBAHH,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,GAAC,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,MAAM,MAAM,IACxC;wBACA,UAAU;wBACV,oBAAoB,CAAC;oBACvB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;uCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,UAAU;oBACV,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,UAAU,SAAS,MAAM,GAAG,KAAK,gBAAgB,MAAM,GAAG;QAC1D,oBAAoB,CAAC;QAErB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,UAAU,MAAM,MAAM,GAAG,KAAK,gBAAgB,MAAM,GAAG;IACzD;IAEA,MAAM,eAAe;QACnB,IAAI,MAAM,IAAI,IAAI;YAChB,SAAS,MAAM,IAAI;YACnB,UAAU;YACV,oBAAoB,CAAC;QACvB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,SAAS;QACT,SAAS;QACT,UAAU;QACV,oBAAoB,CAAC;IACvB;IAEA,MAAM,cAAc;YAIlB;QAHA,SAAS;QACT,UAAU;QACV,oBAAoB,CAAC;SACrB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;wBACH,MAAK;wBACL,WAAU;;;;;;kCAEZ,6LAAC,6HAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAQ,WAAU;;;;;;;;;;;0CAGjC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,UAAU,CAAC,MAAM,IAAI;0CACtB;;;;;;;;;;;;;;;;;;YAOJ,UAAU,CAAC,gBAAgB,MAAM,GAAG,KAAK,cAAc,MAAM,GAAG,CAAC,mBAChE,6LAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;oCAC3D,gCACC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDACV;;;;;;;;;;;;4BAKJ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;oCAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0FACA,qBAAqB,QACjB,iEACA;oCAEN,SAAS,IAAM,uBAAuB,KAAK,IAAI;;sDAE/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAQ,WAAU;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,IAAI;;;;;;;;;;;;sDAE/C,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,KAAK,KAAK;;;;;;;mCAdR,KAAK,EAAE;;;;;;;;;;;oBAqBnB,gBAAgB,MAAM,KAAK,KAAK,uBAC/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAgD;oCACjD;oCAAM;;;;;;;4BAEnB,oBAAoB,gCACnB,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP,eAAe;oCACf,UAAU;gCACZ;;kDAEA,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAS,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GArPgB;KAAA", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst DropdownMenu = DropdownMenuPrimitive.Root;\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n));\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName;\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName;\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className,\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n));\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n));\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName;\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n));\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n));\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  );\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,6JAAA,CAAA,aAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sLACA,4YACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,6JAAA,CAAA,aAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/theme-switcher.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { <PERSON>pt<PERSON>, <PERSON>, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\nimport { useEffect, useState } from \"react\";\n\nconst ThemeSwitcher = () => {\n  const [mounted, setMounted] = useState(false);\n  const { theme, setTheme } = useTheme();\n\n  // useEffect only runs on the client, so now we can safely show the UI\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  const ICON_SIZE = 16;\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size={\"sm\"}>\n          {theme === \"light\" ? (\n            <Sun\n              key=\"light\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          ) : theme === \"dark\" ? (\n            <Moon\n              key=\"dark\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          ) : (\n            <Laptop\n              key=\"system\"\n              size={ICON_SIZE}\n              className={\"text-muted-foreground\"}\n            />\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-content\" align=\"start\">\n        <DropdownMenuRadioGroup\n          value={theme}\n          onValueChange={(e) => setTheme(e)}\n        >\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"light\">\n            <Sun size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>Light</span>\n          </DropdownMenuRadioItem>\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"dark\">\n            <Moon size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>Dark</span>\n          </DropdownMenuRadioItem>\n          <DropdownMenuRadioItem className=\"flex gap-2\" value=\"system\">\n            <Laptop size={ICON_SIZE} className=\"text-muted-foreground\" />{\" \"}\n            <span>System</span>\n          </DropdownMenuRadioItem>\n        </DropdownMenuRadioGroup>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n};\n\nexport { ThemeSwitcher };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;AAcA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;QACb;kCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,YAAY;IAElB,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAM;8BAC3B,UAAU,wBACT,6LAAC,mMAAA,CAAA,MAAG;wBAEF,MAAM;wBACN,WAAW;uBAFP;;;;mEAIJ,UAAU,uBACZ,6LAAC,qMAAA,CAAA,OAAI;wBAEH,MAAM;wBACN,WAAW;uBAFP;;;;iFAKN,6LAAC,yMAAA,CAAA,SAAM;wBAEL,MAAM;wBACN,WAAW;uBAFP;;;;;;;;;;;;;;;0BAOZ,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAY,OAAM;0BAC/C,cAAA,6LAAC,wIAAA,CAAA,yBAAsB;oBACrB,OAAO;oBACP,eAAe,CAAC,IAAM,SAAS;;sCAE/B,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,mMAAA,CAAA,MAAG;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC3D,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC5D,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,wBAAqB;4BAAC,WAAU;4BAAa,OAAM;;8CAClD,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;oCAAW,WAAU;;;;;;gCAA2B;8CAC9D,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GA7DM;;QAEwB,mJAAA,CAAA,WAAQ;;;KAFhC", "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/dashboard-header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { usePathname } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\n\nimport { ThemeSwitcher } from '@/components/theme-switcher'\n\ninterface DashboardHeaderProps {\n  onCreatePrompt?: () => void\n  children?: React.ReactNode\n}\n\nexport function DashboardHeader({ onCreatePrompt, children }: DashboardHeaderProps) {\n  const pathname = usePathname()\n\n  const navItems = [\n    {\n      href: '/dashboard',\n      label: '提示词',\n      icon: 'home' as const,\n      active: pathname === '/dashboard'\n    },\n    {\n      href: '/dashboard/search',\n      label: '搜索',\n      icon: 'search' as const,\n      active: pathname === '/dashboard/search'\n    },\n    {\n      href: '/dashboard/categories',\n      label: '分类管理',\n      icon: 'folder' as const,\n      active: pathname === '/dashboard/categories'\n    },\n    {\n      href: '/dashboard/stats',\n      label: '数据统计',\n      icon: 'chart' as const,\n      active: pathname === '/dashboard/stats'\n    }\n  ]\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 左侧：Logo 和导航 */}\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/dashboard\" className=\"flex items-center gap-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg\">\n                <Icon name=\"lightbulb\" className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                提示词管理\n              </span>\n            </Link>\n\n            <nav className=\"hidden md:flex items-center gap-1\">\n              {navItems.map((item) => (\n                <Link key={item.href} href={item.href}>\n                  <Button\n                    variant={item.active ? \"secondary\" : \"ghost\"}\n                    size=\"sm\"\n                    className=\"gap-2\"\n                  >\n                    <Icon name={item.icon} className=\"h-4 w-4\" />\n                    {item.label}\n                  </Button>\n                </Link>\n              ))}\n            </nav>\n          </div>\n\n          {/* 右侧：操作按钮 */}\n          <div className=\"flex items-center gap-2\">\n            {onCreatePrompt && (\n              <Button\n                variant=\"default\"\n                size=\"sm\"\n                onClick={onCreatePrompt}\n                className=\"hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200\"\n              >\n                <Icon name=\"plus\" className=\"h-4 w-4 mr-2\" />\n                新建提示词\n              </Button>\n            )}\n            \n            {children}\n            <ThemeSwitcher />\n          </div>\n        </div>\n\n        {/* 移动端导航 */}\n        <div className=\"md:hidden border-t border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex items-center gap-1 py-2\">\n            {navItems.map((item) => (\n              <Link key={item.href} href={item.href} className=\"flex-1\">\n                <Button\n                  variant={item.active ? \"secondary\" : \"ghost\"}\n                  size=\"sm\"\n                  className=\"w-full gap-2\"\n                >\n                  <Icon name={item.icon} className=\"h-4 w-4\" />\n                  {item.label}\n                </Button>\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;AAcO,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,cAAc,EAAE,QAAQ,EAAwB,GAAlD;;IAC9B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,QAAQ,aAAa;QACvB;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAY,WAAU;;;;;;;;;;;sDAEnC,6LAAC;4CAAK,WAAU;sDAAkD;;;;;;;;;;;;8CAKpE,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAAiB,MAAM,KAAK,IAAI;sDACnC,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,KAAK,MAAM,GAAG,cAAc;gDACrC,MAAK;gDACL,WAAU;;kEAEV,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;;;;;;oDAChC,KAAK,KAAK;;;;;;;2CAPJ,KAAK,IAAI;;;;;;;;;;;;;;;;sCAe1B,6LAAC;4BAAI,WAAU;;gCACZ,gCACC,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;wCAAiB;;;;;;;gCAKhD;8CACD,6LAAC,mIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;8BAKlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;gCAAE,WAAU;0CAC/C,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS,KAAK,MAAM,GAAG,cAAc;oCACrC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAM,KAAK,IAAI;4CAAE,WAAU;;;;;;wCAChC,KAAK,KAAK;;;;;;;+BAPJ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBlC;GApGgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/utils/format.ts"], "sourcesContent": ["/**\n * 格式化工具函数\n */\n\n/**\n * 格式化日期\n */\nexport function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {\n  const date = new Date(dateString)\n  \n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    ...options\n  }\n  \n  return date.toLocaleDateString('zh-CN', defaultOptions)\n}\n\n/**\n * 格式化相对时间\n */\nexport function formatRelativeTime(dateString: string): string {\n  const date = new Date(dateString)\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return '刚刚'\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}分钟前`\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}小时前`\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}天前`\n  }\n  \n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks}周前`\n  }\n  \n  const diffInMonths = Math.floor(diffInDays / 30)\n  if (diffInMonths < 12) {\n    return `${diffInMonths}个月前`\n  }\n  \n  const diffInYears = Math.floor(diffInDays / 365)\n  return `${diffInYears}年前`\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 B'\n  \n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 格式化数字（添加千分位分隔符）\n */\nexport function formatNumber(num: number): string {\n  // 处理 NaN、undefined、null 等异常值\n  if (typeof num !== 'number' || isNaN(num) || !isFinite(num)) {\n    return '0'\n  }\n  return num.toLocaleString('zh-CN')\n}\n\n/**\n * 截断文本\n */\nexport function truncateText(text: string, maxLength: number, suffix: string = '...'): string {\n  if (text.length <= maxLength) {\n    return text\n  }\n  \n  return text.slice(0, maxLength - suffix.length) + suffix\n}\n\n/**\n * 高亮搜索关键词\n */\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\n  if (!searchTerm.trim()) {\n    return text\n  }\n  \n  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi')\n  return text.replace(regex, '<mark>$1</mark>')\n}\n\n/**\n * 转义正则表达式特殊字符\n */\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')\n}\n\n/**\n * 格式化标签列表\n */\nexport function formatTagList(tags: string[], maxDisplay: number = 3): string {\n  if (tags.length === 0) {\n    return '无标签'\n  }\n  \n  if (tags.length <= maxDisplay) {\n    return tags.join(', ')\n  }\n  \n  const displayTags = tags.slice(0, maxDisplay)\n  const remainingCount = tags.length - maxDisplay\n  \n  return `${displayTags.join(', ')} +${remainingCount}`\n}\n\n/**\n * 格式化使用次数\n */\nexport function formatUsageCount(count: number): string {\n  // 处理 NaN、undefined、null 等异常值\n  if (typeof count !== 'number' || isNaN(count) || !isFinite(count) || count < 0) {\n    return '未使用'\n  }\n\n  if (count === 0) {\n    return '未使用'\n  }\n\n  if (count === 1) {\n    return '使用1次'\n  }\n\n  if (count < 1000) {\n    return `使用${count}次`\n  }\n\n  if (count < 1000000) {\n    const k = Math.floor(count / 100) / 10\n    return `使用${k}k次`\n  }\n\n  const m = Math.floor(count / 100000) / 10\n  return `使用${m}m次`\n}\n\n/**\n * 格式化颜色值\n */\nexport function formatColor(color: string): string {\n  // 确保颜色值以 # 开头\n  if (!color.startsWith('#')) {\n    return `#${color}`\n  }\n  \n  return color.toLowerCase()\n}\n\n/**\n * 生成随机颜色\n */\nexport function generateRandomColor(): string {\n  const colors = [\n    '#ef4444', // red\n    '#f97316', // orange\n    '#f59e0b', // amber\n    '#eab308', // yellow\n    '#84cc16', // lime\n    '#22c55e', // green\n    '#10b981', // emerald\n    '#14b8a6', // teal\n    '#06b6d4', // cyan\n    '#0ea5e9', // sky\n    '#3b82f6', // blue\n    '#6366f1', // indigo\n    '#8b5cf6', // violet\n    '#a855f7', // purple\n    '#d946ef', // fuchsia\n    '#ec4899', // pink\n    '#f43f5e', // rose\n  ]\n  \n  return colors[Math.floor(Math.random() * colors.length)]\n}\n\n/**\n * 验证颜色值\n */\nexport function isValidColor(color: string): boolean {\n  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/\n  return hexColorRegex.test(color)\n}\n\n/**\n * 格式化搜索查询\n */\nexport function formatSearchQuery(query: string): string {\n  return query\n    .trim()\n    .replace(/\\s+/g, ' ') // 合并多个空格\n    .toLowerCase()\n}\n\n/**\n * 提取文本摘要\n */\nexport function extractSummary(text: string, maxLength: number = 150): string {\n  // 移除 Markdown 语法\n  const cleanText = text\n    .replace(/#{1,6}\\s+/g, '') // 移除标题标记\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // 移除粗体标记\n    .replace(/\\*(.*?)\\*/g, '$1') // 移除斜体标记\n    .replace(/`(.*?)`/g, '$1') // 移除代码标记\n    .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1') // 移除链接，保留文本\n    .replace(/\\n+/g, ' ') // 换行符转空格\n    .trim()\n  \n  return truncateText(cleanText, maxLength)\n}\n\n/**\n * 格式化 JSON 数据\n */\nexport function formatJSON(data: any, pretty: boolean = true): string {\n  try {\n    return pretty \n      ? JSON.stringify(data, null, 2)\n      : JSON.stringify(data)\n  } catch (error) {\n    return String(data)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;;;;;;;;;AACM,SAAS,WAAW,UAAkB,EAAE,OAAoC;IACjF,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;QACL,GAAG,OAAO;IACZ;IAEA,OAAO,KAAK,kBAAkB,CAAC,SAAS;AAC1C;AAKO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,AAAC,GAAgB,OAAd,eAAc;IAC1B;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,AAAC,GAAc,OAAZ,aAAY;IACxB;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,AAAC,GAAe,OAAb,cAAa;IACzB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,AAAC,GAAc,OAAZ,aAAY;AACxB;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAK;QAAM;QAAM;QAAM;KAAK;IAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,aAAa,GAAW;IACtC,6BAA6B;IAC7B,IAAI,OAAO,QAAQ,YAAY,MAAM,QAAQ,CAAC,SAAS,MAAM;QAC3D,OAAO;IACT;IACA,OAAO,IAAI,cAAc,CAAC;AAC5B;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;QAAE,SAAA,iEAAiB;IAC7E,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,OAAO;IACT;IAEA,OAAO,KAAK,KAAK,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACpD;AAKO,SAAS,oBAAoB,IAAY,EAAE,UAAkB;IAClE,IAAI,CAAC,WAAW,IAAI,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,QAAQ,IAAI,OAAO,AAAC,IAA4B,OAAzB,aAAa,aAAY,MAAI;IAC1D,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7B;AAEA;;CAEC,GACD,SAAS,aAAa,MAAc;IAClC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C;AAKO,SAAS,cAAc,IAAc;QAAE,aAAA,iEAAqB;IACjE,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,KAAK,MAAM,IAAI,YAAY;QAC7B,OAAO,KAAK,IAAI,CAAC;IACnB;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,GAAG;IAClC,MAAM,iBAAiB,KAAK,MAAM,GAAG;IAErC,OAAO,AAAC,GAA6B,OAA3B,YAAY,IAAI,CAAC,OAAM,MAAmB,OAAf;AACvC;AAKO,SAAS,iBAAiB,KAAa;IAC5C,6BAA6B;IAC7B,IAAI,OAAO,UAAU,YAAY,MAAM,UAAU,CAAC,SAAS,UAAU,QAAQ,GAAG;QAC9E,OAAO;IACT;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,IAAI,QAAQ,MAAM;QAChB,OAAO,AAAC,KAAU,OAAN,OAAM;IACpB;IAEA,IAAI,QAAQ,SAAS;QACnB,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,OAAO;QACpC,OAAO,AAAC,KAAM,OAAF,GAAE;IAChB;IAEA,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,UAAU;IACvC,OAAO,AAAC,KAAM,OAAF,GAAE;AAChB;AAKO,SAAS,YAAY,KAAa;IACvC,cAAc;IACd,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM;QAC1B,OAAO,AAAC,IAAS,OAAN;IACb;IAEA,OAAO,MAAM,WAAW;AAC1B;AAKO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAKO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,WAAW;AAChB;AAKO,SAAS,eAAe,IAAY;QAAE,YAAA,iEAAoB;IAC/D,iBAAiB;IACjB,MAAM,YAAY,KACf,OAAO,CAAC,cAAc,IAAI,SAAS;KACnC,OAAO,CAAC,kBAAkB,MAAM,SAAS;KACzC,OAAO,CAAC,cAAc,MAAM,SAAS;KACrC,OAAO,CAAC,YAAY,MAAM,SAAS;KACnC,OAAO,CAAC,qBAAqB,MAAM,YAAY;KAC/C,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,IAAI;IAEP,OAAO,aAAa,WAAW;AACjC;AAKO,SAAS,WAAW,IAAS;QAAE,SAAA,iEAAkB;IACtD,IAAI;QACF,OAAO,SACH,KAAK,SAAS,CAAC,MAAM,MAAM,KAC3B,KAAK,SAAS,CAAC;IACrB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/copy-history.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { formatRelativeTime, truncateText } from '@/lib/utils/format'\n\ninterface CopyHistoryItem {\n  id: string\n  promptId: string\n  promptTitle: string\n  content: string\n  copiedAt: string\n  category?: {\n    name: string\n    color: string\n  }\n}\n\ninterface CopyHistoryProps {\n  className?: string\n}\n\nexport function CopyHistory({ className }: CopyHistoryProps) {\n  const [history, setHistory] = useState<CopyHistoryItem[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    loadCopyHistory()\n  }, [])\n\n  const loadCopyHistory = () => {\n    try {\n      setIsLoading(true)\n      // 从 localStorage 获取复制历史\n      const stored = localStorage.getItem('prompt-copy-history')\n      if (stored) {\n        const parsed = JSON.parse(stored)\n        setHistory(parsed.slice(0, 20)) // 只保留最近20条\n      }\n    } catch (error) {\n      console.error('加载复制历史失败:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const addCopyRecord = (promptId: string, promptTitle: string, content: string, category?: { name: string; color: string }) => {\n    const newRecord: CopyHistoryItem = {\n      id: Date.now().toString(),\n      promptId,\n      promptTitle,\n      content,\n      copiedAt: new Date().toISOString(),\n      category\n    }\n\n    const updatedHistory = [newRecord, ...history].slice(0, 20)\n    setHistory(updatedHistory)\n    \n    try {\n      localStorage.setItem('prompt-copy-history', JSON.stringify(updatedHistory))\n    } catch (error) {\n      console.error('保存复制历史失败:', error)\n    }\n  }\n\n  const clearHistory = () => {\n    setHistory([])\n    try {\n      localStorage.removeItem('prompt-copy-history')\n    } catch (error) {\n      console.error('清除复制历史失败:', error)\n    }\n  }\n\n  const handleCopyAgain = async (content: string) => {\n    try {\n      await navigator.clipboard.writeText(content)\n      // 可以添加 toast 提示\n    } catch (error) {\n      console.error('复制失败:', error)\n    }\n  }\n\n  // 暴露 addCopyRecord 方法给父组件使用\n  useEffect(() => {\n    // 将方法挂载到全局，供其他组件调用\n    (window as any).addCopyRecord = addCopyRecord\n    \n    return () => {\n      delete (window as any).addCopyRecord\n    }\n  }, [history])\n\n  if (isLoading) {\n    return (\n      <Card className={className}>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Icon name=\"clipboard\" className=\"h-5 w-5\" />\n            复制历史\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            {[...Array(3)].map((_, i) => (\n              <div key={i} className=\"animate-pulse\">\n                <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Icon name=\"clipboard\" className=\"h-5 w-5\" />\n              复制历史\n            </CardTitle>\n            <CardDescription>\n              最近复制的提示词记录\n            </CardDescription>\n          </div>\n          {history.length > 0 && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={clearHistory}\n            >\n              <Icon name=\"trash\" className=\"h-4 w-4 mr-2\" />\n              清除\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n      <CardContent>\n        {history.length > 0 ? (\n          <div className=\"space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto\">\n            {history.map((item) => (\n              <div\n                key={item.id}\n                className=\"group p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n              >\n                <div className=\"flex items-start justify-between gap-3\">\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <h4 className=\"font-medium text-sm truncate\">\n                        {item.promptTitle}\n                      </h4>\n                      {item.category && (\n                        <Badge\n                          variant=\"secondary\"\n                          className=\"text-xs\"\n                          style={{ \n                            backgroundColor: `${item.category.color}20`,\n                            color: item.category.color \n                          }}\n                        >\n                          {item.category.name}\n                        </Badge>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-muted-foreground mb-2\">\n                      {truncateText(item.content, 100)}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {formatRelativeTime(item.copiedAt)}\n                    </p>\n                  </div>\n                  \n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n                    onClick={() => handleCopyAgain(item.content)}\n                  >\n                    <Icon name=\"copy\" className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-8\">\n            <Icon name=\"clipboard\" className=\"h-8 w-8 text-muted-foreground mx-auto mb-2\" />\n            <p className=\"text-sm text-muted-foreground\">\n              暂无复制记录\n            </p>\n            <p className=\"text-xs text-muted-foreground mt-1\">\n              复制提示词后会在这里显示历史记录\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n\n// 导出一个 hook 供其他组件使用\nexport function useCopyHistory() {\n  const addCopyRecord = (promptId: string, promptTitle: string, content: string, category?: { name: string; color: string }) => {\n    if ((window as any).addCopyRecord) {\n      (window as any).addCopyRecord(promptId, promptTitle, content, category)\n    }\n  }\n\n  return { addCopyRecord }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAyBO,SAAS,YAAY,KAA+B;QAA/B,EAAE,SAAS,EAAoB,GAA/B;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,wBAAwB;YACxB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,WAAW,OAAO,KAAK,CAAC,GAAG,MAAK,WAAW;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC,UAAkB,aAAqB,SAAiB;QAC7E,MAAM,YAA6B;YACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA;YACA,UAAU,IAAI,OAAO,WAAW;YAChC;QACF;QAEA,MAAM,iBAAiB;YAAC;eAAc;SAAQ,CAAC,KAAK,CAAC,GAAG;QACxD,WAAW;QAEX,IAAI;YACF,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,eAAe;QACnB,WAAW,EAAE;QACb,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,mBAAmB;YAClB,OAAe,aAAa,GAAG;YAEhC;yCAAO;oBACL,OAAO,AAAC,OAAe,aAAa;gBACtC;;QACF;gCAAG;QAAC;KAAQ;IAEZ,IAAI,WAAW;QACb,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,4HAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,4HAAA,CAAA,OAAI;gCAAC,MAAK;gCAAY,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjD,6LAAC,4HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,4HAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,4HAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAY,WAAU;;;;;;wCAAY;;;;;;;8CAG/C,6LAAC,4HAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;wBAIlB,QAAQ,MAAM,GAAG,mBAChB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;;8CAET,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAQ,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMtD,6LAAC,4HAAA,CAAA,cAAW;0BACT,QAAQ,MAAM,GAAG,kBAChB,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,qBACZ,6LAAC;4BAEC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,WAAW;;;;;;oDAElB,KAAK,QAAQ,kBACZ,6LAAC,6HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;wDACV,OAAO;4DACL,iBAAiB,AAAC,GAAsB,OAApB,KAAK,QAAQ,CAAC,KAAK,EAAC;4DACxC,OAAO,KAAK,QAAQ,CAAC,KAAK;wDAC5B;kEAEC,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;0DAIzB,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,OAAO,EAAE;;;;;;0DAE9B,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,QAAQ;;;;;;;;;;;;kDAIrC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,gBAAgB,KAAK,OAAO;kDAE3C,cAAA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;;;;;;;;;;;;2BApC3B,KAAK,EAAE;;;;;;;;;yCA2ClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAY,WAAU;;;;;;sCACjC,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GArLgB;KAAA;AAwLT,SAAS;IACd,MAAM,gBAAgB,CAAC,UAAkB,aAAqB,SAAiB;QAC7E,IAAI,AAAC,OAAe,aAAa,EAAE;YAChC,OAAe,aAAa,CAAC,UAAU,aAAa,SAAS;QAChE;IACF;IAEA,OAAO;QAAE;IAAc;AACzB", "debugId": null}}, {"offset": {"line": 2425, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/code-block.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON> as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter'\nimport { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { cn } from '@/lib/utils'\n\ninterface CodeBlockProps {\n  children: string\n  className?: string\n  inline?: boolean\n}\n\nexport function CodeBlock({ children, className, inline }: CodeBlockProps) {\n  const [copied, setCopied] = useState(false)\n  const { toast } = useToast()\n  \n  const match = /language-(\\w+)/.exec(className || '')\n  const language = match ? match[1] : ''\n  \n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(children)\n      setCopied(true)\n      toast({\n        title: \"复制成功\",\n        description: \"代码已复制到剪贴板\",\n      })\n      setTimeout(() => setCopied(false), 2000)\n    } catch (error) {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  if (inline) {\n    return (\n      <code className=\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold\">\n        {children}\n      </code>\n    )\n  }\n\n  return (\n    <div className=\"relative group\">\n      <div className=\"absolute right-2 top-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handleCopy}\n          className={cn(\n            \"h-8 px-2 bg-background/80 backdrop-blur-sm border-border/50\",\n            copied && \"bg-green-100 border-green-300 text-green-700\"\n          )}\n        >\n          <Icon \n            name={copied ? \"check\" : \"copy\"} \n            className=\"h-3 w-3 mr-1\" \n          />\n          {copied ? \"已复制\" : \"复制\"}\n        </Button>\n      </div>\n      \n      <SyntaxHighlighter\n        style={tomorrow}\n        language={language}\n        PreTag=\"div\"\n        className=\"rounded-md !mt-0 !mb-0\"\n        customStyle={{\n          margin: 0,\n          borderRadius: '0.375rem',\n          fontSize: '0.875rem',\n          lineHeight: '1.25rem',\n        }}\n      >\n        {children}\n      </SyntaxHighlighter>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAgBO,SAAS,UAAU,KAA+C;QAA/C,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAkB,GAA/C;;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;IACjD,MAAM,WAAW,QAAQ,KAAK,CAAC,EAAE,GAAG;IAEpC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,IAAI,QAAQ;QACV,qBACE,6LAAC;YAAK,WAAU;sBACb;;;;;;IAGP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA,UAAU;;sCAGZ,6LAAC,4HAAA,CAAA,OAAI;4BACH,MAAM,SAAS,UAAU;4BACzB,WAAU;;;;;;wBAEX,SAAS,QAAQ;;;;;;;;;;;;0BAItB,6LAAC,6MAAA,CAAA,QAAiB;gBAChB,OAAO,sOAAA,CAAA,WAAQ;gBACf,UAAU;gBACV,QAAO;gBACP,WAAU;gBACV,aAAa;oBACX,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,YAAY;gBACd;0BAEC;;;;;;;;;;;;AAIT;GArEgB;;QAEI,wHAAA,CAAA,WAAQ;;;KAFZ", "debugId": null}}, {"offset": {"line": 2552, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/markdown-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport ReactMarkdown from 'react-markdown'\nimport { CodeBlock } from './code-block'\n\ninterface MarkdownPreviewProps {\n  content: string\n  className?: string\n  truncate?: boolean\n  maxLines?: number\n}\n\nexport function MarkdownPreview({ \n  content, \n  className = \"\", \n  truncate = false, \n  maxLines = 3 \n}: MarkdownPreviewProps) {\n  return (\n    <div className={`prose prose-sm max-w-none dark:prose-invert ${className}`}>\n      <ReactMarkdown\n        components={{\n          // 简化的代码块渲染（卡片预览中不显示复制按钮）\n          code({ node, inline, className, children, ...props }) {\n            if (inline) {\n              return (\n                <code className=\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold\">\n                  {children}\n                </code>\n              )\n            }\n            \n            // 在卡片预览中，代码块使用简化样式\n            if (truncate) {\n              return (\n                <pre className=\"bg-muted rounded p-2 text-xs overflow-hidden\">\n                  <code>{String(children).replace(/\\n$/, '')}</code>\n                </pre>\n              )\n            }\n            \n            // 完整视图中使用带复制功能的代码块\n            return (\n              <CodeBlock\n                inline={inline}\n                className={className}\n                {...props}\n              >\n                {String(children).replace(/\\n$/, '')}\n              </CodeBlock>\n            )\n          },\n          \n          // 简化其他元素的渲染\n          h1: ({ children }) => truncate ? \n            <span className=\"font-bold text-base\">{children}</span> : \n            <h1 className=\"text-xl font-bold mb-2\">{children}</h1>,\n          h2: ({ children }) => truncate ? \n            <span className=\"font-semibold text-sm\">{children}</span> : \n            <h2 className=\"text-lg font-semibold mb-2\">{children}</h2>,\n          h3: ({ children }) => truncate ? \n            <span className=\"font-medium text-sm\">{children}</span> : \n            <h3 className=\"text-base font-medium mb-1\">{children}</h3>,\n          \n          p: ({ children }) => truncate ? \n            <span className=\"inline\">{children} </span> : \n            <p className=\"mb-2\">{children}</p>,\n          \n          ul: ({ children }) => truncate ? \n            <span className=\"inline\">{children}</span> : \n            <ul className=\"list-disc list-inside mb-2\">{children}</ul>,\n          \n          ol: ({ children }) => truncate ? \n            <span className=\"inline\">{children}</span> : \n            <ol className=\"list-decimal list-inside mb-2\">{children}</ol>,\n          \n          li: ({ children }) => truncate ? \n            <span className=\"inline\">• {children} </span> : \n            <li className=\"mb-1\">{children}</li>,\n          \n          strong: ({ children }) => <strong className=\"font-semibold\">{children}</strong>,\n          em: ({ children }) => <em className=\"italic\">{children}</em>,\n          \n          blockquote: ({ children }) => truncate ? \n            <span className=\"inline italic\">{children}</span> : \n            <blockquote className=\"border-l-4 border-muted pl-4 italic mb-2\">{children}</blockquote>,\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,gBAAgB,KAKT;QALS,EAC9B,OAAO,EACP,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,CAAC,EACS,GALS;IAM9B,qBACE,6LAAC;QAAI,WAAW,AAAC,+CAAwD,OAAV;kBAC7D,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,YAAY;gBACV,yBAAyB;gBACzB,MAAK,KAA+C;wBAA/C,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,GAA/C;oBACH,IAAI,QAAQ;wBACV,qBACE,6LAAC;4BAAK,WAAU;sCACb;;;;;;oBAGP;oBAEA,mBAAmB;oBACnB,IAAI,UAAU;wBACZ,qBACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAM,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;oBAG7C;oBAEA,mBAAmB;oBACnB,qBACE,6LAAC,qIAAA,CAAA,YAAS;wBACR,QAAQ;wBACR,WAAW;wBACV,GAAG,KAAK;kCAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;gBAGvC;gBAEA,YAAY;gBACZ,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAuB;;;;;+CACvC,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;;gBAC1C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAyB;;;;;+CACzC,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAC9C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAuB;;;;;+CACvC,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAE9C,GAAG;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACnB,6LAAC;wBAAK,WAAU;;4BAAU;4BAAS;;;;;;+CACnC,6LAAC;wBAAE,WAAU;kCAAQ;;;;;;;gBAEvB,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;+CAC1B,6LAAC;wBAAG,WAAU;kCAA8B;;;;;;;gBAE9C,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;+CAC1B,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;;gBAEjD,IAAI;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBACpB,6LAAC;wBAAK,WAAU;;4BAAS;4BAAG;4BAAS;;;;;;+CACrC,6LAAC;wBAAG,WAAU;kCAAQ;;;;;;;gBAExB,QAAQ;wBAAC,EAAE,QAAQ,EAAE;yCAAK,6LAAC;wBAAO,WAAU;kCAAiB;;;;;;;gBAC7D,IAAI;wBAAC,EAAE,QAAQ,EAAE;yCAAK,6LAAC;wBAAG,WAAU;kCAAU;;;;;;;gBAE9C,YAAY;wBAAC,EAAE,QAAQ,EAAE;2BAAK,yBAC5B,6LAAC;wBAAK,WAAU;kCAAiB;;;;;+CACjC,6LAAC;wBAAW,WAAU;kCAA4C;;;;;;;YACtE;sBAEC;;;;;;;;;;;AAIT;KAhFgB", "debugId": null}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">关闭</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/utils/clipboard.ts"], "sourcesContent": ["/**\n * 剪贴板操作工具函数\n */\n\n/**\n * 复制文本到剪贴板\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    // 优先使用现代 Clipboard API\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text)\n      return true\n    }\n\n    // 降级到传统方法\n    return fallbackCopyToClipboard(text)\n  } catch (error) {\n    console.error('复制到剪贴板失败:', error)\n    return fallbackCopyToClipboard(text)\n  }\n}\n\n/**\n * 降级复制方法（兼容旧浏览器）\n */\nfunction fallbackCopyToClipboard(text: string): boolean {\n  try {\n    // 创建临时文本区域\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    textArea.style.position = 'fixed'\n    textArea.style.left = '-999999px'\n    textArea.style.top = '-999999px'\n    document.body.appendChild(textArea)\n    \n    // 选择并复制\n    textArea.focus()\n    textArea.select()\n    const successful = document.execCommand('copy')\n    \n    // 清理\n    document.body.removeChild(textArea)\n    \n    return successful\n  } catch (error) {\n    console.error('降级复制方法失败:', error)\n    return false\n  }\n}\n\n/**\n * 检查是否支持剪贴板操作\n */\nexport function isClipboardSupported(): boolean {\n  return !!(navigator.clipboard || document.execCommand)\n}\n\n/**\n * 从剪贴板读取文本\n */\nexport async function readFromClipboard(): Promise<string | null> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      const text = await navigator.clipboard.readText()\n      return text\n    }\n    \n    // 传统方法无法读取剪贴板内容\n    console.warn('当前环境不支持读取剪贴板内容')\n    return null\n  } catch (error) {\n    console.error('读取剪贴板失败:', error)\n    return null\n  }\n}\n\n/**\n * 复制文本并显示反馈\n */\nexport async function copyWithFeedback(\n  text: string,\n  onSuccess?: () => void,\n  onError?: (error: string) => void\n): Promise<boolean> {\n  const success = await copyToClipboard(text)\n  \n  if (success) {\n    onSuccess?.()\n  } else {\n    onError?.('复制失败，请手动复制')\n  }\n  \n  return success\n}\n\n/**\n * 格式化文本用于复制\n */\nexport function formatTextForCopy(text: string): string {\n  // 移除多余的空白字符\n  return text\n    .replace(/\\r\\n/g, '\\n') // 统一换行符\n    .replace(/\\t/g, '  ') // 制表符转换为空格\n    .trim() // 移除首尾空白\n}\n\n/**\n * 复制 JSON 数据\n */\nexport async function copyJSON(data: any, pretty: boolean = true): Promise<boolean> {\n  try {\n    const jsonString = pretty \n      ? JSON.stringify(data, null, 2)\n      : JSON.stringify(data)\n    \n    return await copyToClipboard(jsonString)\n  } catch (error) {\n    console.error('复制JSON失败:', error)\n    return false\n  }\n}\n\n/**\n * 复制 Markdown 格式文本\n */\nexport async function copyAsMarkdown(title: string, content: string): Promise<boolean> {\n  const markdown = `# ${title}\\n\\n${content}`\n  return await copyToClipboard(markdown)\n}\n\n/**\n * 复制为代码块格式\n */\nexport async function copyAsCodeBlock(content: string, language: string = ''): Promise<boolean> {\n  const codeBlock = `\\`\\`\\`${language}\\n${content}\\n\\`\\`\\``\n  return await copyToClipboard(codeBlock)\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;;;AACM,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,uBAAuB;QACvB,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT;QAEA,UAAU;QACV,OAAO,wBAAwB;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,wBAAwB;IACjC;AACF;AAEA;;CAEC,GACD,SAAS,wBAAwB,IAAY;IAC3C,IAAI;QACF,WAAW;QACX,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;QACtB,SAAS,KAAK,CAAC,GAAG,GAAG;QACrB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,QAAQ;QACR,SAAS,KAAK;QACd,SAAS,MAAM;QACf,MAAM,aAAa,SAAS,WAAW,CAAC;QAExC,KAAK;QACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,SAAS;IACd,OAAO,CAAC,CAAC,CAAC,UAAU,SAAS,IAAI,SAAS,WAAW;AACvD;AAKO,eAAe;IACpB,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,OAAO,MAAM,UAAU,SAAS,CAAC,QAAQ;YAC/C,OAAO;QACT;QAEA,gBAAgB;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;IACT;AACF;AAKO,eAAe,iBACpB,IAAY,EACZ,SAAsB,EACtB,OAAiC;IAEjC,MAAM,UAAU,MAAM,gBAAgB;IAEtC,IAAI,SAAS;QACX,sBAAA,gCAAA;IACF,OAAO;QACL,oBAAA,8BAAA,QAAU;IACZ;IAEA,OAAO;AACT;AAKO,SAAS,kBAAkB,IAAY;IAC5C,YAAY;IACZ,OAAO,KACJ,OAAO,CAAC,SAAS,MAAM,QAAQ;KAC/B,OAAO,CAAC,OAAO,MAAM,WAAW;KAChC,IAAI,GAAG,SAAS;;AACrB;AAKO,eAAe,SAAS,IAAS;QAAE,SAAA,iEAAkB;IAC1D,IAAI;QACF,MAAM,aAAa,SACf,KAAK,SAAS,CAAC,MAAM,MAAM,KAC3B,KAAK,SAAS,CAAC;QAEnB,OAAO,MAAM,gBAAgB;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,eAAe,eAAe,KAAa,EAAE,OAAe;IACjE,MAAM,WAAW,AAAC,KAAgB,OAAZ,OAAM,QAAc,OAAR;IAClC,OAAO,MAAM,gBAAgB;AAC/B;AAKO,eAAe,gBAAgB,OAAe;QAAE,WAAA,iEAAmB;IACxE,MAAM,YAAY,AAAC,MAAqB,OAAb,UAAS,MAAY,OAAR,SAAQ;IAChD,OAAO,MAAM,gBAAgB;AAC/B", "debugId": null}}, {"offset": {"line": 3090, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/prompt-detail-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { MarkdownPreview } from '@/components/ui/markdown-preview'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { copyToClipboard } from '@/lib/utils/clipboard'\nimport { formatDate, formatRelativeTime } from '@/lib/utils/format'\nimport type { PromptWithDetails } from '@/types/database'\n\ninterface PromptDetailModalProps {\n  prompt: PromptWithDetails | null\n  isOpen: boolean\n  onClose: () => void\n  onEdit: (promptId: string) => void\n  onDelete: (promptId: string) => void\n  onCopy: (content: string, promptId: string) => void\n}\n\nexport function PromptDetailModal({\n  prompt,\n  isOpen,\n  onClose,\n  onEdit,\n  onDelete,\n  onCopy\n}: PromptDetailModalProps) {\n  const [isMarkdownView, setIsMarkdownView] = useState(true)\n  const { toast } = useToast()\n\n  if (!prompt) return null\n\n  const handleCopy = async () => {\n    const success = await copyToClipboard(prompt.content)\n    if (success) {\n      onCopy(prompt.content, prompt.id)\n      toast({\n        title: \"复制成功\",\n        description: \"提示词已复制到剪贴板\",\n      })\n    } else {\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板，请手动复制\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handleEdit = () => {\n    onEdit(prompt.id)\n    onClose()\n  }\n\n  const handleDelete = () => {\n    onDelete(prompt.id)\n    onClose()\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <div className=\"flex items-start justify-between pr-8\">\n            <div className=\"flex-1 min-w-0\">\n              <DialogTitle className=\"text-xl font-semibold mb-2\">\n                {prompt.title}\n              </DialogTitle>\n              {prompt.description && (\n                <DialogDescription className=\"text-base\">\n                  {prompt.description}\n                </DialogDescription>\n              )}\n            </div>\n\n            {/* 操作按钮 - 添加右边距避免与关闭按钮重叠 */}\n            <div className=\"flex items-center gap-2 ml-4 flex-wrap\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleCopy}\n                className=\"flex-shrink-0\"\n              >\n                <Icon name=\"copy\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">复制</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleEdit}\n                className=\"flex-shrink-0\"\n              >\n                <Icon name=\"edit\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">编辑</span>\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleDelete}\n                className=\"text-red-600 hover:text-red-700 flex-shrink-0\"\n              >\n                <Icon name=\"trash\" className=\"h-4 w-4 sm:mr-2\" />\n                <span className=\"hidden sm:inline\">删除</span>\n              </Button>\n            </div>\n          </div>\n\n          {/* 元信息 */}\n          <div className=\"flex flex-wrap items-center gap-4 pt-4 border-t\">\n            {/* 分类 */}\n            {prompt.category && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"folder\" className=\"h-4 w-4 text-muted-foreground\" />\n                <Badge\n                  variant=\"secondary\"\n                  style={{ \n                    backgroundColor: `${prompt.category.color}20`,\n                    color: prompt.category.color \n                  }}\n                >\n                  <Icon name={prompt.category.icon as any} className=\"h-3 w-3 mr-1\" />\n                  {prompt.category.name}\n                </Badge>\n              </div>\n            )}\n\n            {/* 标签 */}\n            {prompt.tags && prompt.tags.length > 0 && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"tags\" className=\"h-4 w-4 text-muted-foreground\" />\n                <div className=\"flex flex-wrap gap-1\">\n                  {prompt.tags.map((tag) => (\n                    <Badge\n                      key={tag.id}\n                      variant=\"outline\"\n                      className=\"text-xs\"\n                      style={{ \n                        backgroundColor: `${tag.color}20`,\n                        color: tag.color,\n                        borderColor: `${tag.color}40`\n                      }}\n                    >\n                      {tag.name}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* 使用次数 */}\n            <div className=\"flex items-center gap-2\">\n              <Icon name=\"eye\" className=\"h-4 w-4 text-muted-foreground\" />\n              <span className=\"text-sm text-muted-foreground\">\n                使用 {prompt.usage_count} 次\n              </span>\n            </div>\n\n            {/* 时间信息 */}\n            <div className=\"flex items-center gap-2\">\n              <Icon name=\"clock\" className=\"h-4 w-4 text-muted-foreground\" />\n              <span className=\"text-sm text-muted-foreground\">\n                创建于 {formatDate(prompt.created_at)}\n              </span>\n            </div>\n\n            {prompt.updated_at !== prompt.created_at && (\n              <div className=\"flex items-center gap-2\">\n                <Icon name=\"refresh\" className=\"h-4 w-4 text-muted-foreground\" />\n                <span className=\"text-sm text-muted-foreground\">\n                  更新于 {formatRelativeTime(prompt.updated_at)}\n                </span>\n              </div>\n            )}\n          </div>\n        </DialogHeader>\n\n        {/* 内容区域 */}\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          {/* 视图切换 */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={!isMarkdownView ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setIsMarkdownView(false)}\n                className=\"relative\"\n              >\n                <Icon name=\"file-text\" className=\"h-4 w-4 mr-2\" />\n                原始文本\n              </Button>\n              <Button\n                variant={isMarkdownView ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setIsMarkdownView(true)}\n                className=\"relative\"\n              >\n                <Icon name=\"eye\" className=\"h-4 w-4 mr-2\" />\n                Markdown 预览\n                {isMarkdownView && (\n                  <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse\" />\n                )}\n              </Button>\n\n              {/* Markdown 功能提示 */}\n              <div className=\"flex items-center gap-1 text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950 px-2 py-1 rounded-md\">\n                <Icon name=\"circle-info\" className=\"h-3 w-3\" />\n                <span>支持 Markdown 格式</span>\n              </div>\n            </div>\n\n            <div className=\"text-sm text-muted-foreground\">\n              {prompt.content.length} 字符\n            </div>\n          </div>\n\n          {/* 内容显示 */}\n          <div className=\"flex-1 overflow-y-auto border rounded-lg p-4 bg-gray-50 dark:bg-gray-900\">\n            {isMarkdownView ? (\n              <div className=\"relative\">\n                {/* Markdown 渲染指示器 */}\n                <div className=\"absolute top-0 right-0 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md\">\n                  <Icon name=\"eye\" className=\"h-3 w-3 inline mr-1\" />\n                  Markdown 渲染\n                </div>\n                <MarkdownPreview\n                  content={prompt.content}\n                  className=\"pt-6\"\n                />\n              </div>\n            ) : (\n              <div className=\"relative\">\n                {/* 原始文本指示器 */}\n                <div className=\"absolute top-0 right-0 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-bl-md rounded-tr-md\">\n                  <Icon name=\"file-text\" className=\"h-3 w-3 inline mr-1\" />\n                  原始文本\n                </div>\n                <pre className=\"whitespace-pre-wrap text-sm font-mono pt-6\">\n                  {prompt.content}\n                </pre>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 底部操作栏 */}\n        <div className=\"flex items-center justify-between pt-4 border-t\">\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // TODO: 实现导出功能\n                console.log('导出提示词')\n              }}\n            >\n              <Icon name=\"download\" className=\"h-4 w-4 mr-2\" />\n              导出\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // TODO: 实现分享功能\n                console.log('分享提示词')\n              }}\n            >\n              <Icon name=\"share\" className=\"h-4 w-4 mr-2\" />\n              分享\n            </Button>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"outline\" onClick={onClose}>\n              关闭\n            </Button>\n            <Button onClick={handleCopy}>\n              <Icon name=\"copy\" className=\"h-4 w-4 mr-2\" />\n              复制内容\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;AA4BO,SAAS,kBAAkB,KAOT;QAPS,EAChC,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,EACiB,GAPS;;IAQhC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEzB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO;QACpD,IAAI,SAAS;YACX,OAAO,OAAO,OAAO,EAAE,OAAO,EAAE;YAChC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,OAAO;YACL,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,aAAa;QACjB,OAAO,OAAO,EAAE;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,OAAO,EAAE;QAClB;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,OAAO,KAAK;;;;;;wCAEd,OAAO,WAAW,kBACjB,6LAAC,8HAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC1B,OAAO,WAAW;;;;;;;;;;;;8CAMzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAO,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAErC,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAO,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAErC,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAQ,WAAU;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;;gCAEZ,OAAO,QAAQ,kBACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAS,WAAU;;;;;;sDAC9B,6LAAC,6HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,OAAO;gDACL,iBAAiB,AAAC,GAAwB,OAAtB,OAAO,QAAQ,CAAC,KAAK,EAAC;gDAC1C,OAAO,OAAO,QAAQ,CAAC,KAAK;4CAC9B;;8DAEA,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAM,OAAO,QAAQ,CAAC,IAAI;oDAAS,WAAU;;;;;;gDAClD,OAAO,QAAQ,CAAC,IAAI;;;;;;;;;;;;;gCAM1B,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wDAC9B,OAAO,IAAI,KAAK;wDAChB,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;oDAC5B;8DAEC,IAAI,IAAI;mDATJ,IAAI,EAAE;;;;;;;;;;;;;;;;8CAiBrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAM,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;;gDAAgC;gDAC1C,OAAO,WAAW;gDAAC;;;;;;;;;;;;;8CAK3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;sDAC7B,6LAAC;4CAAK,WAAU;;gDAAgC;gDACzC,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;gCAIpC,OAAO,UAAU,KAAK,OAAO,UAAU,kBACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAU,WAAU;;;;;;sDAC/B,6LAAC;4CAAK,WAAU;;gDAAgC;gDACzC,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,CAAC,iBAAiB,YAAY;4CACvC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAY,WAAU;;;;;;gDAAiB;;;;;;;sDAGpD,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,YAAY;4CACtC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAM,WAAU;;;;;;gDAAiB;gDAE3C,gCACC,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,OAAI;oDAAC,MAAK;oDAAc,WAAU;;;;;;8DACnC,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,OAAO,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;sCACZ,+BACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAM,WAAU;;;;;;4CAAwB;;;;;;;kDAGrD,6LAAC,2IAAA,CAAA,kBAAe;wCACd,SAAS,OAAO,OAAO;wCACvB,WAAU;;;;;;;;;;;qDAId,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAY,WAAU;;;;;;4CAAwB;;;;;;;kDAG3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;8BAQzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,eAAe;wCACf,QAAQ,GAAG,CAAC;oCACd;;sDAEA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAW,WAAU;;;;;;wCAAiB;;;;;;;8CAGnD,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,eAAe;wCACf,QAAQ,GAAG,CAAC;oCACd;;sDAEA,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAQ,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG5C,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAK;4CAAO,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAzQgB;;QASI,wHAAA,CAAA,WAAQ;;;KATZ", "debugId": null}}, {"offset": {"line": 3773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3851, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/api/client.ts"], "sourcesContent": ["import type {\n  PromptWithDetails,\n  PromptInsert,\n  PromptUpdate,\n  SearchParams,\n  PaginatedResponse,\n  Category,\n  CategoryWithCount,\n  CategoryInsert,\n  CategoryUpdate,\n  Tag,\n  TagInsert,\n  TagUpdate,\n  AppPreferences,\n  AppPreferencesUpdate\n} from '@/types/database'\n\nconst API_BASE = process.env.NEXT_PUBLIC_APP_URL || (typeof window !== 'undefined' ? window.location.origin : '')\n\n// 通用 API 调用函数\nasync function apiCall<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE}/api${endpoint}`\n  \n  const response = await fetch(url, {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  })\n\n  if (!response.ok) {\n    const error = await response.json().catch(() => ({ error: 'Network error' }))\n    throw new Error(error.error || `HTTP ${response.status}`)\n  }\n\n  return response.json()\n}\n\n// ===== 提示词 API =====\n\nexport async function getPrompts(params: SearchParams = {}): Promise<PaginatedResponse<PromptWithDetails>> {\n  const searchParams = new URLSearchParams()\n  \n  if (params.query) searchParams.set('query', params.query)\n  if (params.categoryId) searchParams.set('categoryId', params.categoryId)\n  if (params.tagIds?.length) searchParams.set('tagIds', params.tagIds.join(','))\n  if (params.sortBy) searchParams.set('sortBy', params.sortBy)\n  if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder)\n  if (params.limit) searchParams.set('limit', params.limit.toString())\n  if (params.offset) searchParams.set('offset', params.offset.toString())\n\n  const queryString = searchParams.toString()\n  return apiCall<PaginatedResponse<PromptWithDetails>>(\n    `/prompts${queryString ? `?${queryString}` : ''}`\n  )\n}\n\nexport async function getPromptById(id: string): Promise<PromptWithDetails> {\n  return apiCall<PromptWithDetails>(`/prompts/${id}`)\n}\n\nexport async function createPrompt(data: PromptInsert, tagIds?: string[]): Promise<PromptWithDetails> {\n  const payload = tagIds ? { ...data, tagIds } : data\n  return apiCall<PromptWithDetails>('/prompts', {\n    method: 'POST',\n    body: JSON.stringify(payload),\n  })\n}\n\nexport async function updatePrompt(id: string, data: PromptUpdate, tagIds?: string[]): Promise<PromptWithDetails> {\n  const payload = tagIds ? { ...data, tagIds } : data\n  return apiCall<PromptWithDetails>(`/prompts/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(payload),\n  })\n}\n\nexport async function deletePrompt(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/prompts/${id}`, {\n    method: 'DELETE',\n  })\n}\n\nexport async function incrementUsageCount(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/prompts/${id}/usage`, {\n    method: 'POST',\n  })\n}\n\n// ===== 分类 API =====\n\nexport async function getCategories(): Promise<CategoryWithCount[]> {\n  return apiCall<CategoryWithCount[]>('/categories')\n}\n\nexport async function getCategoryById(id: string): Promise<Category> {\n  return apiCall<Category>(`/categories/${id}`)\n}\n\nexport async function createCategory(data: CategoryInsert): Promise<Category> {\n  return apiCall<Category>('/categories', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function updateCategory(id: string, data: CategoryUpdate): Promise<Category> {\n  return apiCall<Category>(`/categories/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function deleteCategory(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/categories/${id}`, {\n    method: 'DELETE',\n  })\n}\n\nexport async function updateCategoriesOrder(data: { id: string; sortOrder: number }[]): Promise<void> {\n  await apiCall<{ success: boolean }>('/categories', {\n    method: 'PATCH',\n    body: JSON.stringify(data),\n  })\n}\n\n// ===== 标签 API =====\n\nexport async function getTags(): Promise<Tag[]> {\n  return apiCall<Tag[]>('/tags')\n}\n\nexport async function getTagById(id: string): Promise<Tag> {\n  return apiCall<Tag>(`/tags/${id}`)\n}\n\nexport async function createTag(data: TagInsert): Promise<Tag> {\n  return apiCall<Tag>('/tags', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function createTags(data: TagInsert[]): Promise<Tag[]> {\n  return apiCall<Tag[]>('/tags', {\n    method: 'POST',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function updateTag(id: string, data: TagUpdate): Promise<Tag> {\n  return apiCall<Tag>(`/tags/${id}`, {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\nexport async function deleteTag(id: string): Promise<void> {\n  await apiCall<{ success: boolean }>(`/tags/${id}`, {\n    method: 'DELETE',\n  })\n}\n\n// ===== 应用偏好设置 API =====\n\nexport async function getAppPreferences(): Promise<AppPreferences> {\n  return apiCall<AppPreferences>('/preferences')\n}\n\nexport async function updateAppPreferences(data: AppPreferencesUpdate): Promise<AppPreferences> {\n  return apiCall<AppPreferences>('/preferences', {\n    method: 'PUT',\n    body: JSON.stringify(data),\n  })\n}\n\n// ===== 搜索历史 API =====\n// 简化版本：由于移除了用户认证，搜索历史功能暂时返回空数据\n\nexport async function getSearchHistory(limit: number = 10): Promise<any[]> {\n  // 返回空数组，搜索历史功能暂时禁用\n  return []\n}\n\nexport async function addSearchHistory(searchTerm: string): Promise<void> {\n  // 搜索历史功能暂时禁用，不执行任何操作\n  console.log('搜索历史功能已禁用:', searchTerm)\n}\n\nexport async function clearSearchHistory(): Promise<void> {\n  // 搜索历史功能暂时禁用，不执行任何操作\n  console.log('搜索历史功能已禁用')\n}\n\nexport async function advancedSearch(params: any): Promise<any[]> {\n  // 使用基本的提示词搜索功能\n  const searchParams = {\n    query: params.query,\n    categoryId: params.categoryId,\n    tagIds: params.tagIds,\n    sortBy: params.sortBy || 'updated_at',\n    sortOrder: params.sortOrder || 'desc',\n    limit: params.limit || 20,\n    offset: params.offset || 0\n  }\n\n  const result = await getPrompts(searchParams)\n  return result.data\n}\n\n// ===== 分类名称检查 API =====\n\nexport async function checkCategoryNameExists(name: string, excludeId?: string): Promise<boolean> {\n  try {\n    const categories = await getCategories()\n    return categories.some(cat =>\n      cat.name.toLowerCase() === name.toLowerCase() &&\n      (!excludeId || cat.id !== excludeId)\n    )\n  } catch (error) {\n    console.error('检查分类名称失败:', error)\n    return false\n  }\n}\n\n// ===== 兼容性别名 =====\n\n// 为了保持与现有代码的兼容性，提供一些别名\nexport const incrementPromptUsage = incrementUsageCount\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBiB;AAAjB,MAAM,WAAW,6DAAmC,CAAC,uCAAgC,OAAO,QAAQ,CAAC,MAAM,GAAG,uBAAE;AAEhH,cAAc;AACd,eAAe,QACb,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,UAAS,QAAe,OAAT;IAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;gBAAE,OAAO;YAAgB,CAAC;QAC3E,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;IACxD;IAEA,OAAO,SAAS,IAAI;AACtB;AAIO,eAAe;QAAW,SAAA,iEAAuB,CAAC;QAKnD;IAJJ,MAAM,eAAe,IAAI;IAEzB,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK;IACxD,IAAI,OAAO,UAAU,EAAE,aAAa,GAAG,CAAC,cAAc,OAAO,UAAU;IACvE,KAAI,iBAAA,OAAO,MAAM,cAAb,qCAAA,eAAe,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC;IACzE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;IAC3D,IAAI,OAAO,SAAS,EAAE,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;IACpE,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACjE,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;IAEpE,MAAM,cAAc,aAAa,QAAQ;IACzC,OAAO,QACL,AAAC,WAA+C,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;AAEjD;AAEO,eAAe,cAAc,EAAU;IAC5C,OAAO,QAA2B,AAAC,YAAc,OAAH;AAChD;AAEO,eAAe,aAAa,IAAkB,EAAE,MAAiB;IACtE,MAAM,UAAU,SAAS;QAAE,GAAG,IAAI;QAAE;IAAO,IAAI;IAC/C,OAAO,QAA2B,YAAY;QAC5C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,aAAa,EAAU,EAAE,IAAkB,EAAE,MAAiB;IAClF,MAAM,UAAU,SAAS;QAAE,GAAG,IAAI;QAAE;IAAO,IAAI;IAC/C,OAAO,QAA2B,AAAC,YAAc,OAAH,KAAM;QAClD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,QAA8B,AAAC,YAAc,OAAH,KAAM;QACpD,QAAQ;IACV;AACF;AAEO,eAAe,oBAAoB,EAAU;IAClD,MAAM,QAA8B,AAAC,YAAc,OAAH,IAAG,WAAS;QAC1D,QAAQ;IACV;AACF;AAIO,eAAe;IACpB,OAAO,QAA6B;AACtC;AAEO,eAAe,gBAAgB,EAAU;IAC9C,OAAO,QAAkB,AAAC,eAAiB,OAAH;AAC1C;AAEO,eAAe,eAAe,IAAoB;IACvD,OAAO,QAAkB,eAAe;QACtC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAU,EAAE,IAAoB;IACnE,OAAO,QAAkB,AAAC,eAAiB,OAAH,KAAM;QAC5C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,QAA8B,AAAC,eAAiB,OAAH,KAAM;QACvD,QAAQ;IACV;AACF;AAEO,eAAe,sBAAsB,IAAyC;IACnF,MAAM,QAA8B,eAAe;QACjD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAIO,eAAe;IACpB,OAAO,QAAe;AACxB;AAEO,eAAe,WAAW,EAAU;IACzC,OAAO,QAAa,AAAC,SAAW,OAAH;AAC/B;AAEO,eAAe,UAAU,IAAe;IAC7C,OAAO,QAAa,SAAS;QAC3B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,WAAW,IAAiB;IAChD,OAAO,QAAe,SAAS;QAC7B,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,UAAU,EAAU,EAAE,IAAe;IACzD,OAAO,QAAa,AAAC,SAAW,OAAH,KAAM;QACjC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEO,eAAe,UAAU,EAAU;IACxC,MAAM,QAA8B,AAAC,SAAW,OAAH,KAAM;QACjD,QAAQ;IACV;AACF;AAIO,eAAe;IACpB,OAAO,QAAwB;AACjC;AAEO,eAAe,qBAAqB,IAA0B;IACnE,OAAO,QAAwB,gBAAgB;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe;QAAiB,QAAA,iEAAgB;IACrD,mBAAmB;IACnB,OAAO,EAAE;AACX;AAEO,eAAe,iBAAiB,UAAkB;IACvD,qBAAqB;IACrB,QAAQ,GAAG,CAAC,cAAc;AAC5B;AAEO,eAAe;IACpB,qBAAqB;IACrB,QAAQ,GAAG,CAAC;AACd;AAEO,eAAe,eAAe,MAAW;IAC9C,eAAe;IACf,MAAM,eAAe;QACnB,OAAO,OAAO,KAAK;QACnB,YAAY,OAAO,UAAU;QAC7B,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM,IAAI;QACzB,WAAW,OAAO,SAAS,IAAI;QAC/B,OAAO,OAAO,KAAK,IAAI;QACvB,QAAQ,OAAO,MAAM,IAAI;IAC3B;IAEA,MAAM,SAAS,MAAM,WAAW;IAChC,OAAO,OAAO,IAAI;AACpB;AAIO,eAAe,wBAAwB,IAAY,EAAE,SAAkB;IAC5E,IAAI;QACF,MAAM,aAAa,MAAM;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,MACrB,IAAI,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,MAC3C,CAAC,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS;IAEvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;IACT;AACF;AAKO,MAAM,uBAAuB", "debugId": null}}, {"offset": {"line": 4060, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/prompt-form-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\n// 直接使用 API 客户端\nimport * as api from '@/lib/api/client'\n// 移除旧的数据库导入，直接使用 API\nimport type { \n  PromptWithDetails, \n  Category, \n  Tag,\n  PromptInsert,\n  PromptUpdate \n} from '@/types/database'\n\ninterface PromptFormModalProps {\n  prompt?: PromptWithDetails | null\n  isOpen: boolean\n  onClose: () => void\n  onSuccess: () => void\n}\n\nexport function PromptFormModal({\n  prompt,\n  isOpen,\n  onClose,\n  onSuccess\n}: PromptFormModalProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [content, setContent] = useState('')\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('')\n  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])\n  const [newTagName, setNewTagName] = useState('')\n  const [categories, setCategories] = useState<Category[]>([])\n  const [tags, setTags] = useState<Tag[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [isLoadingData, setIsLoadingData] = useState(false)\n\n  const { toast } = useToast()\n  const isEditing = !!prompt\n\n  // 加载基础数据\n  useEffect(() => {\n    if (isOpen) {\n      loadData()\n    }\n  }, [isOpen])\n\n  // 填充编辑数据\n  useEffect(() => {\n    if (prompt) {\n      setTitle(prompt.title)\n      setDescription(prompt.description || '')\n      setContent(prompt.content)\n      setSelectedCategoryId(prompt.category_id || '')\n      setSelectedTagIds(prompt.tags?.map(tag => tag.id) || [])\n    } else {\n      resetForm()\n    }\n  }, [prompt])\n\n  const loadData = async () => {\n    try {\n      setIsLoadingData(true)\n      const [categoriesData, tagsData] = await Promise.all([\n        api.getCategories(),\n        api.getTags()\n      ])\n      setCategories(categoriesData)\n      setTags(tagsData)\n    } catch (error) {\n      console.error('加载数据失败:', error)\n      toast({\n        title: \"加载失败\",\n        description: \"无法加载分类和标签数据\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoadingData(false)\n    }\n  }\n\n  const resetForm = () => {\n    setTitle('')\n    setDescription('')\n    setContent('')\n    setSelectedCategoryId('')\n    setSelectedTagIds([])\n    setNewTagName('')\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !content.trim()) {\n      toast({\n        title: \"表单验证失败\",\n        description: \"标题和内容不能为空\",\n        variant: \"destructive\",\n      })\n      return\n    }\n\n    try {\n      setIsLoading(true)\n\n      const promptData = {\n        title: title.trim(),\n        description: description.trim() || undefined,\n        content: content.trim(),\n        category_id: selectedCategoryId || undefined,\n      }\n\n      if (isEditing && prompt) {\n        await api.updatePrompt(prompt.id, promptData as PromptUpdate, selectedTagIds)\n        toast({\n          title: \"更新成功\",\n          description: \"提示词已成功更新\",\n        })\n      } else {\n        // 直接调用 API 创建提示词\n        console.log('🚀 直接创建提示词')\n        const newPrompt = await api.createPrompt(promptData as PromptInsert, selectedTagIds)\n\n        // 立即通知父组件更新\n        if (onSuccess) {\n          onSuccess(newPrompt)\n        }\n        toast({\n          title: \"创建成功\",\n          description: \"提示词已成功创建\",\n        })\n      }\n\n      onSuccess()\n      onClose()\n      resetForm()\n    } catch (error) {\n      console.error('保存提示词失败:', error)\n      toast({\n        title: \"保存失败\",\n        description: isEditing ? \"更新提示词时出现错误\" : \"创建提示词时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleAddTag = async () => {\n    if (!newTagName.trim()) return\n\n    try {\n      const newTag = await api.createTag({\n        name: newTagName.trim(),\n        color: '#6366f1' // 默认颜色\n      })\n      \n      setTags(prev => [...prev, newTag])\n      setSelectedTagIds(prev => [...prev, newTag.id])\n      setNewTagName('')\n      \n      toast({\n        title: \"标签创建成功\",\n        description: `标签 \"${newTag.name}\" 已创建并添加`,\n      })\n    } catch (error) {\n      console.error('创建标签失败:', error)\n      toast({\n        title: \"创建标签失败\",\n        description: \"无法创建新标签\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const toggleTag = (tagId: string) => {\n    setSelectedTagIds(prev => \n      prev.includes(tagId) \n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    )\n  }\n\n  const selectedTags = tags.filter(tag => selectedTagIds.includes(tag.id))\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] w-[95vw] sm:w-full overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <DialogTitle>\n            {isEditing ? '编辑提示词' : '创建新提示词'}\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing ? '修改提示词的信息和内容' : '填写提示词的基本信息和内容'}\n          </DialogDescription>\n        </DialogHeader>\n\n        {isLoadingData ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <Icon name=\"spinner\" className=\"h-6 w-6 animate-spin mr-2\" />\n            <span>加载中...</span>\n          </div>\n        ) : (\n          <form onSubmit={handleSubmit} className=\"flex-1 overflow-hidden flex flex-col\">\n            <div className=\"flex-1 overflow-y-auto space-y-4\">\n              {/* 标题 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"title\">标题 *</Label>\n                <Input\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"输入提示词标题\"\n                  required\n                />\n              </div>\n\n              {/* 描述 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">描述</Label>\n                <Input\n                  id=\"description\"\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  placeholder=\"输入提示词的简短描述（可选）\"\n                />\n              </div>\n\n              {/* 分类 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"category\">分类</Label>\n                <select\n                  id=\"category\"\n                  value={selectedCategoryId}\n                  onChange={(e) => setSelectedCategoryId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-input rounded-md bg-background\"\n                >\n                  <option value=\"\">选择分类（可选）</option>\n                  {categories.map((category) => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* 标签 */}\n              <div className=\"space-y-2\">\n                <Label>标签</Label>\n                \n                {/* 已选标签 */}\n                {selectedTags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mb-2\">\n                    {selectedTags.map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"secondary\"\n                        className=\"cursor-pointer\"\n                        style={{ \n                          backgroundColor: `${tag.color}20`,\n                          color: tag.color \n                        }}\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        {tag.name}\n                        <Icon name=\"times\" className=\"h-3 w-3 ml-1\" />\n                      </Badge>\n                    ))}\n                  </div>\n                )}\n\n                {/* 可选标签 */}\n                <div className=\"flex flex-wrap gap-2 mb-2\">\n                  {tags\n                    .filter(tag => !selectedTagIds.includes(tag.id))\n                    .map((tag) => (\n                      <Badge\n                        key={tag.id}\n                        variant=\"outline\"\n                        className=\"cursor-pointer hover:bg-gray-100\"\n                        onClick={() => toggleTag(tag.id)}\n                      >\n                        <Icon name=\"plus\" className=\"h-3 w-3 mr-1\" />\n                        {tag.name}\n                      </Badge>\n                    ))}\n                </div>\n\n                {/* 新建标签 */}\n                <div className=\"flex gap-2\">\n                  <Input\n                    value={newTagName}\n                    onChange={(e) => setNewTagName(e.target.value)}\n                    placeholder=\"创建新标签\"\n                    className=\"flex-1\"\n                    onKeyPress={(e) => {\n                      if (e.key === 'Enter') {\n                        e.preventDefault()\n                        handleAddTag()\n                      }\n                    }}\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleAddTag}\n                    disabled={!newTagName.trim()}\n                  >\n                    <Icon name=\"plus\" className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              {/* 内容 */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"content\">内容 *</Label>\n                <Textarea\n                  id=\"content\"\n                  value={content}\n                  onChange={(e) => setContent(e.target.value)}\n                  placeholder=\"输入提示词内容\"\n                  className=\"min-h-[200px] font-mono\"\n                  required\n                />\n                <div className=\"text-sm text-muted-foreground\">\n                  {content.length} 字符\n                </div>\n              </div>\n            </div>\n\n            {/* 底部按钮 */}\n            <div className=\"flex items-center justify-end gap-2 pt-4 border-t\">\n              <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={isLoading}>\n                {isLoading ? (\n                  <>\n                    <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                    {isEditing ? '更新中...' : '创建中...'}\n                  </>\n                ) : (\n                  <>\n                    <Icon name=\"save\" className=\"h-4 w-4 mr-2\" />\n                    {isEditing ? '更新' : '创建'}\n                  </>\n                )}\n              </Button>\n            </div>\n          </form>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;;AAlBA;;;;;;;;;;;AAmCO,SAAS,gBAAgB,KAKT;QALS,EAC9B,MAAM,EACN,MAAM,EACN,OAAO,EACP,SAAS,EACY,GALS;;IAM9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,CAAC,CAAC;IAEpB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;oBAKQ;gBAJlB,SAAS,OAAO,KAAK;gBACrB,eAAe,OAAO,WAAW,IAAI;gBACrC,WAAW,OAAO,OAAO;gBACzB,sBAAsB,OAAO,WAAW,IAAI;gBAC5C,kBAAkB,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,GAAG;iDAAC,CAAA,MAAO,IAAI,EAAE;oDAAK,EAAE;YACzD,OAAO;gBACL;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,IAAI;YACF,iBAAiB;YACjB,MAAM,CAAC,gBAAgB,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,uHAAA,CAAA,gBAAiB;gBACjB,uHAAA,CAAA,UAAW;aACZ;YACD,cAAc;YACd,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,eAAe;QACf,WAAW;QACX,sBAAsB;QACtB,kBAAkB,EAAE;QACpB,cAAc;IAChB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YACpC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,aAAa;YAEb,MAAM,aAAa;gBACjB,OAAO,MAAM,IAAI;gBACjB,aAAa,YAAY,IAAI,MAAM;gBACnC,SAAS,QAAQ,IAAI;gBACrB,aAAa,sBAAsB;YACrC;YAEA,IAAI,aAAa,QAAQ;gBACvB,MAAM,uHAAA,CAAA,eAAgB,CAAC,OAAO,EAAE,EAAE,YAA4B;gBAC9D,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,iBAAiB;gBACjB,QAAQ,GAAG,CAAC;gBACZ,MAAM,YAAY,MAAM,uHAAA,CAAA,eAAgB,CAAC,YAA4B;gBAErE,YAAY;gBACZ,IAAI,WAAW;oBACb,UAAU;gBACZ;gBACA,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;YAEA;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa,YAAY,eAAe;gBACxC,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,IAAI;YACF,MAAM,SAAS,MAAM,uHAAA,CAAA,YAAa,CAAC;gBACjC,MAAM,WAAW,IAAI;gBACrB,OAAO,UAAU,OAAO;YAC1B;YAEA,QAAQ,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;YACjC,kBAAkB,CAAA,OAAQ;uBAAI;oBAAM,OAAO,EAAE;iBAAC;YAC9C,cAAc;YAEd,MAAM;gBACJ,OAAO;gBACP,aAAa,AAAC,OAAkB,OAAZ,OAAO,IAAI,EAAC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,SACzB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAAO,eAAe,QAAQ,CAAC,IAAI,EAAE;IAEtE,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCACT,YAAY,UAAU;;;;;;sCAEzB,6LAAC,8HAAA,CAAA,oBAAiB;sCACf,YAAY,gBAAgB;;;;;;;;;;;;gBAIhC,8BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAU,WAAU;;;;;;sCAC/B,6LAAC;sCAAK;;;;;;;;;;;yCAGR,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACrD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAQ9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;sDAAC;;;;;;wCAGN,aAAa,MAAM,GAAG,mBACrB,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wDAC9B,OAAO,IAAI,KAAK;oDAClB;oDACA,SAAS,IAAM,UAAU,IAAI,EAAE;;wDAE9B,IAAI,IAAI;sEACT,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAQ,WAAU;;;;;;;mDAVxB,IAAI,EAAE;;;;;;;;;;sDAiBnB,6LAAC;4CAAI,WAAU;sDACZ,KACE,MAAM,CAAC,CAAA,MAAO,CAAC,eAAe,QAAQ,CAAC,IAAI,EAAE,GAC7C,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,UAAU,IAAI,EAAE;;sEAE/B,6LAAC,4HAAA,CAAA,OAAI;4DAAC,MAAK;4DAAO,WAAU;;;;;;wDAC3B,IAAI,IAAI;;mDANJ,IAAI,EAAE;;;;;;;;;;sDAYnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;oDACV,YAAY,CAAC;wDACX,IAAI,EAAE,GAAG,KAAK,SAAS;4DACrB,EAAE,cAAc;4DAChB;wDACF;oDACF;;;;;;8DAEF,6LAAC,8HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI;8DAE1B,cAAA,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAK;wDAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG1D,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,0BACC;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAU,WAAU;;;;;;4CAC9B,YAAY,WAAW;;qEAG1B;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAO,WAAU;;;;;;4CAC3B,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC;GA/UgB;;QAiBI,wHAAA,CAAA,WAAQ;;;KAjBZ", "debugId": null}}, {"offset": {"line": 4656, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/delete-confirm-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\n\ninterface DeleteConfirmDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  description: string\n  itemName?: string\n  isLoading?: boolean\n}\n\nexport function DeleteConfirmDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  itemName,\n  isLoading = false\n}: DeleteConfirmDialogProps) {\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center justify-center w-10 h-10 bg-red-100 rounded-full\">\n              <Icon name=\"exclamation-triangle\" className=\"h-5 w-5 text-red-600\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-lg font-semibold\">\n                {title}\n              </DialogTitle>\n              <DialogDescription className=\"mt-1\">\n                {description}\n              </DialogDescription>\n            </div>\n          </div>\n        </DialogHeader>\n\n        {itemName && (\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-3 my-4\">\n            <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n              即将删除：\n            </p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\n              {itemName}\n            </p>\n          </div>\n        )}\n\n        <div className=\"flex items-center justify-end gap-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n          >\n            取消\n          </Button>\n          <Button\n            variant=\"destructive\"\n            onClick={onConfirm}\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <>\n                <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                删除中...\n              </>\n            ) : (\n              <>\n                <Icon name=\"trash\" className=\"h-4 w-4 mr-2\" />\n                确认删除\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AAVA;;;;;AAsBO,SAAS,oBAAoB,KAQT;QARS,EAClC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,KAAK,EACQ,GARS;IASlC,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,MAAK;oCAAuB,WAAU;;;;;;;;;;;0CAE9C,6LAAC;;kDACC,6LAAC,8HAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB;;;;;;kDAEH,6LAAC,8HAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;gBAMR,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAGjE,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCACX;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;sCAET,0BACC;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAU,WAAU;;;;;;oCAA8B;;6DAI/D;;kDACE,6LAAC,4HAAA,CAAA,OAAI;wCAAC,MAAK;wCAAQ,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D;KApEgB", "debugId": null}}, {"offset": {"line": 4832, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/category-form-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Icon, IconName } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\n// 直接使用 API 客户端\nimport * as api from '@/lib/api/client'\nimport { generateRandomColor, isValidColor } from '@/lib/utils/format'\nimport type { \n  Category, \n  CategoryInsert,\n  CategoryUpdate \n} from '@/types/database'\n\ninterface CategoryFormModalProps {\n  category?: Category | null\n  isOpen: boolean\n  onClose: () => void\n  onSuccess: () => void\n}\n\n// 可选的图标列表\nconst availableIcons: { name: IconName; label: string }[] = [\n  { name: 'folder', label: '文件夹' },\n  { name: 'code', label: '代码' },\n  { name: 'pen-to-square', label: '写作' },\n  { name: 'bullhorn', label: '营销' },\n  { name: 'rocket', label: '效率' },\n  { name: 'graduation-cap', label: '学习' },\n  { name: 'lightbulb', label: '创意' },\n  { name: 'cog', label: '工具' },\n  { name: 'heart', label: '收藏' },\n  { name: 'star', label: '重要' },\n  { name: 'fire', label: '热门' },\n  { name: 'gem', label: '精选' },\n  { name: 'bullseye', label: '目标' },\n  { name: 'flag', label: '标记' },\n  { name: 'bookmark', label: '书签' },\n  { name: 'database', label: '数据' },\n  { name: 'cloud', label: '云端' },\n  { name: 'mobile', label: '移动' },\n  { name: 'desktop', label: '桌面' },\n  { name: 'palette', label: '设计' }\n]\n\n// 预设颜色\nconst presetColors = [\n  '#ef4444', // red\n  '#f97316', // orange\n  '#f59e0b', // amber\n  '#eab308', // yellow\n  '#84cc16', // lime\n  '#22c55e', // green\n  '#10b981', // emerald\n  '#14b8a6', // teal\n  '#06b6d4', // cyan\n  '#0ea5e9', // sky\n  '#3b82f6', // blue\n  '#6366f1', // indigo\n  '#8b5cf6', // violet\n  '#a855f7', // purple\n  '#d946ef', // fuchsia\n  '#ec4899', // pink\n]\n\nexport function CategoryFormModal({\n  category,\n  isOpen,\n  onClose,\n  onSuccess\n}: CategoryFormModalProps) {\n  const [name, setName] = useState('')\n  const [description, setDescription] = useState('')\n  const [color, setColor] = useState('#6366f1')\n  const [icon, setIcon] = useState<IconName>('folder')\n  const [customColor, setCustomColor] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [nameError, setNameError] = useState('')\n\n  const { toast } = useToast()\n  const isEditing = !!category\n\n  // 填充编辑数据\n  useEffect(() => {\n    if (category) {\n      setName(category.name)\n      setDescription(category.description || '')\n      setColor(category.color)\n      setIcon(category.icon as IconName)\n      setCustomColor('')\n    } else {\n      resetForm()\n    }\n  }, [category])\n\n  const resetForm = () => {\n    setName('')\n    setDescription('')\n    setColor('#6366f1')\n    setIcon('folder')\n    setCustomColor('')\n    setNameError('')\n  }\n\n  const validateName = async (nameValue: string) => {\n    if (!nameValue.trim()) {\n      setNameError('分类名称不能为空')\n      return false\n    }\n\n    if (nameValue.length > 50) {\n      setNameError('分类名称不能超过50个字符')\n      return false\n    }\n\n    try {\n      const exists = await api.checkCategoryNameExists(\n        nameValue.trim(),\n        isEditing ? category?.id : undefined\n      )\n      \n      if (exists) {\n        setNameError('分类名称已存在')\n        return false\n      }\n    } catch (error) {\n      console.error('检查分类名称失败:', error)\n    }\n\n    setNameError('')\n    return true\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    const isNameValid = await validateName(name)\n    if (!isNameValid) return\n\n    const finalColor = customColor && isValidColor(customColor) ? customColor : color\n\n    try {\n      setIsLoading(true)\n\n      const categoryData = {\n        name: name.trim(),\n        description: description.trim() || undefined,\n        color: finalColor,\n        icon: icon,\n      }\n\n      if (isEditing && category) {\n        console.log('🚀 直接更新分类')\n        await api.updateCategory(category.id, categoryData as CategoryUpdate)\n        toast({\n          title: \"更新成功\",\n          description: \"分类已成功更新\",\n        })\n      } else {\n        console.log('🚀 直接创建分类')\n        const newCategory = await api.createCategory(categoryData as CategoryInsert)\n        toast({\n          title: \"创建成功\",\n          description: \"分类已成功创建\",\n        })\n        console.log('✅ 分类创建成功:', newCategory)\n      }\n\n      onSuccess()\n      onClose()\n      resetForm()\n    } catch (error) {\n      console.error('保存分类失败:', error)\n      toast({\n        title: \"保存失败\",\n        description: isEditing ? \"更新分类时出现错误\" : \"创建分类时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleColorSelect = (selectedColor: string) => {\n    setColor(selectedColor)\n    setCustomColor('')\n  }\n\n  const handleCustomColorChange = (value: string) => {\n    setCustomColor(value)\n    if (isValidColor(value)) {\n      setColor(value)\n    }\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle>\n            {isEditing ? '编辑分类' : '创建新分类'}\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing ? '修改分类的信息和外观' : '创建一个新的提示词分类'}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* 分类名称 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"name\">分类名称 *</Label>\n            <Input\n              id=\"name\"\n              value={name}\n              onChange={(e) => {\n                setName(e.target.value)\n                setNameError('')\n              }}\n              onBlur={() => validateName(name)}\n              placeholder=\"输入分类名称\"\n              className={nameError ? 'border-red-500' : ''}\n              required\n            />\n            {nameError && (\n              <p className=\"text-sm text-red-600\">{nameError}</p>\n            )}\n          </div>\n\n          {/* 分类描述 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"description\">描述</Label>\n            <Textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"输入分类描述（可选）\"\n              className=\"min-h-[80px]\"\n            />\n          </div>\n\n          {/* 图标选择 */}\n          <div className=\"space-y-2\">\n            <Label>图标</Label>\n            <div className=\"grid grid-cols-5 gap-2\">\n              {availableIcons.map((iconOption) => (\n                <button\n                  key={iconOption.name}\n                  type=\"button\"\n                  className={`\n                    flex items-center justify-center w-10 h-10 rounded-lg border-2 transition-colors\n                    ${icon === iconOption.name \n                      ? 'border-blue-500 bg-blue-50' \n                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                    }\n                  `}\n                  onClick={() => setIcon(iconOption.name)}\n                  title={iconOption.label}\n                >\n                  <Icon name={iconOption.name} className=\"h-5 w-5\" />\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* 颜色选择 */}\n          <div className=\"space-y-2\">\n            <Label>颜色</Label>\n            \n            {/* 预设颜色 */}\n            <div className=\"grid grid-cols-8 gap-2\">\n              {presetColors.map((presetColor) => (\n                <button\n                  key={presetColor}\n                  type=\"button\"\n                  className={`\n                    w-8 h-8 rounded-lg border-2 transition-all\n                    ${color === presetColor \n                      ? 'border-gray-400 scale-110' \n                      : 'border-gray-200 hover:scale-105'\n                    }\n                  `}\n                  style={{ backgroundColor: presetColor }}\n                  onClick={() => handleColorSelect(presetColor)}\n                />\n              ))}\n            </div>\n\n            {/* 自定义颜色 */}\n            <div className=\"flex items-center gap-2\">\n              <Input\n                type=\"text\"\n                value={customColor}\n                onChange={(e) => handleCustomColorChange(e.target.value)}\n                placeholder=\"#6366f1\"\n                className=\"flex-1\"\n              />\n              <div \n                className=\"w-8 h-8 rounded border border-gray-200\"\n                style={{ backgroundColor: color }}\n              />\n            </div>\n          </div>\n\n          {/* 预览 */}\n          <div className=\"space-y-2\">\n            <Label>预览</Label>\n            <div className=\"flex items-center gap-2 p-3 border rounded-lg bg-gray-50\">\n              <Icon name={icon} className=\"h-5 w-5\" color={color} />\n              <span className=\"font-medium\">{name || '分类名称'}</span>\n              {description && (\n                <span className=\"text-sm text-muted-foreground\">\n                  - {description}\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* 底部按钮 */}\n          <div className=\"flex items-center justify-end gap-2 pt-4\">\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={isLoading || !!nameError}>\n              {isLoading ? (\n                <>\n                  <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                  {isEditing ? '更新中...' : '创建中...'}\n                </>\n              ) : (\n                <>\n                  <Icon name=\"save\" className=\"h-4 w-4 mr-2\" />\n                  {isEditing ? '更新' : '创建'}\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;;;AAlBA;;;;;;;;;;;AAgCA,UAAU;AACV,MAAM,iBAAsD;IAC1D;QAAE,MAAM;QAAU,OAAO;IAAM;IAC/B;QAAE,MAAM;QAAQ,OAAO;IAAK;IAC5B;QAAE,MAAM;QAAiB,OAAO;IAAK;IACrC;QAAE,MAAM;QAAY,OAAO;IAAK;IAChC;QAAE,MAAM;QAAU,OAAO;IAAK;IAC9B;QAAE,MAAM;QAAkB,OAAO;IAAK;IACtC;QAAE,MAAM;QAAa,OAAO;IAAK;IACjC;QAAE,MAAM;QAAO,OAAO;IAAK;IAC3B;QAAE,MAAM;QAAS,OAAO;IAAK;IAC7B;QAAE,MAAM;QAAQ,OAAO;IAAK;IAC5B;QAAE,MAAM;QAAQ,OAAO;IAAK;IAC5B;QAAE,MAAM;QAAO,OAAO;IAAK;IAC3B;QAAE,MAAM;QAAY,OAAO;IAAK;IAChC;QAAE,MAAM;QAAQ,OAAO;IAAK;IAC5B;QAAE,MAAM;QAAY,OAAO;IAAK;IAChC;QAAE,MAAM;QAAY,OAAO;IAAK;IAChC;QAAE,MAAM;QAAS,OAAO;IAAK;IAC7B;QAAE,MAAM;QAAU,OAAO;IAAK;IAC9B;QAAE,MAAM;QAAW,OAAO;IAAK;IAC/B;QAAE,MAAM;QAAW,OAAO;IAAK;CAChC;AAED,OAAO;AACP,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,kBAAkB,KAKT;QALS,EAChC,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACc,GALS;;IAMhC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,CAAC,CAAC;IAEpB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU;gBACZ,QAAQ,SAAS,IAAI;gBACrB,eAAe,SAAS,WAAW,IAAI;gBACvC,SAAS,SAAS,KAAK;gBACvB,QAAQ,SAAS,IAAI;gBACrB,eAAe;YACjB,OAAO;gBACL;YACF;QACF;sCAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QAChB,QAAQ;QACR,eAAe;QACf,SAAS;QACT,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,aAAa;YACb,OAAO;QACT;QAEA,IAAI,UAAU,MAAM,GAAG,IAAI;YACzB,aAAa;YACb,OAAO;QACT;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,uHAAA,CAAA,0BAA2B,CAC9C,UAAU,IAAI,IACd,YAAY,qBAAA,+BAAA,SAAU,EAAE,GAAG;YAG7B,IAAI,QAAQ;gBACV,aAAa;gBACb,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;QAEA,aAAa;QACb,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,cAAc,MAAM,aAAa;QACvC,IAAI,CAAC,aAAa;QAElB,MAAM,aAAa,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,eAAe,cAAc;QAE5E,IAAI;YACF,aAAa;YAEb,MAAM,eAAe;gBACnB,MAAM,KAAK,IAAI;gBACf,aAAa,YAAY,IAAI,MAAM;gBACnC,OAAO;gBACP,MAAM;YACR;YAEA,IAAI,aAAa,UAAU;gBACzB,QAAQ,GAAG,CAAC;gBACZ,MAAM,uHAAA,CAAA,iBAAkB,CAAC,SAAS,EAAE,EAAE;gBACtC,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,MAAM,uHAAA,CAAA,iBAAkB,CAAC;gBAC7C,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,QAAQ,GAAG,CAAC,aAAa;YAC3B;YAEA;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa,YAAY,cAAc;gBACvC,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,eAAe;IACjB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,eAAe;QACf,IAAI,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;YACvB,SAAS;QACX;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;sCACT,YAAY,SAAS;;;;;;sCAExB,6LAAC,8HAAA,CAAA,oBAAiB;sCACf,YAAY,eAAe;;;;;;;;;;;;8BAIhC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,6LAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC;wCACT,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACtB,aAAa;oCACf;oCACA,QAAQ,IAAM,aAAa;oCAC3B,aAAY;oCACZ,WAAW,YAAY,mBAAmB;oCAC1C,QAAQ;;;;;;gCAET,2BACC,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,gIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,2BACnB,6LAAC;4CAEC,MAAK;4CACL,WAAW,AAAC,+HAKT,OAHC,SAAS,WAAW,IAAI,GACtB,+BACA,0DACH;4CAEH,SAAS,IAAM,QAAQ,WAAW,IAAI;4CACtC,OAAO,WAAW,KAAK;sDAEvB,cAAA,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAM,WAAW,IAAI;gDAAE,WAAU;;;;;;2CAZlC,WAAW,IAAI;;;;;;;;;;;;;;;;sCAmB5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;8CAAC;;;;;;8CAGP,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;4CAEC,MAAK;4CACL,WAAW,AAAC,yFAKT,OAHC,UAAU,cACR,8BACA,mCACH;4CAEH,OAAO;gDAAE,iBAAiB;4CAAY;4CACtC,SAAS,IAAM,kBAAkB;2CAV5B;;;;;;;;;;8CAgBX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;4CACvD,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB;4CAAM;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4HAAA,CAAA,OAAI;4CAAC,MAAM;4CAAM,WAAU;4CAAU,OAAO;;;;;;sDAC7C,6LAAC;4CAAK,WAAU;sDAAe,QAAQ;;;;;;wCACtC,6BACC,6LAAC;4CAAK,WAAU;;gDAAgC;gDAC3C;;;;;;;;;;;;;;;;;;;sCAOX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAS;;;;;;8CAG1D,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU,aAAa,CAAC,CAAC;8CAC5C,0BACC;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAU,WAAU;;;;;;4CAC9B,YAAY,WAAW;;qEAG1B;;0DACE,6LAAC,4HAAA,CAAA,OAAI;gDAAC,MAAK;gDAAO,WAAU;;;;;;4CAC3B,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GAnRgB;;QAcI,wHAAA,CAAA,WAAQ;;;KAdZ", "debugId": null}}, {"offset": {"line": 5419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5494, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5543, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/data-export.ts"], "sourcesContent": ["/**\n * 数据导入/导出工具\n * 支持 JSON 和 CSV 格式的数据导入导出\n */\n\nimport * as api from '@/lib/api/client'\n\n// 导出数据类型定义\nexport interface ExportData {\n  prompts: any[]\n  categories: any[]\n  tags: any[]\n  exportTime: string\n  version: string\n}\n\n// 导入结果类型\nexport interface ImportResult {\n  success: boolean\n  imported: {\n    prompts: number\n    categories: number\n    tags: number\n  }\n  errors: string[]\n  warnings: string[]\n}\n\n/**\n * 导出所有数据为 JSON 格式\n */\nexport async function exportDataAsJSON(): Promise<string> {\n  try {\n    console.log('🚀 开始导出数据...')\n    \n    // 并行获取所有数据\n    const [prompts, categories, tags] = await Promise.all([\n      api.getPrompts({ limit: 10000 }), // 获取所有提示词\n      api.getCategories(),\n      api.getTags()\n    ])\n\n    const exportData: ExportData = {\n      prompts: prompts.data,\n      categories,\n      tags,\n      exportTime: new Date().toISOString(),\n      version: '1.0.0'\n    }\n\n    console.log(`✅ 导出完成: ${prompts.data.length} 个提示词, ${categories.length} 个分类, ${tags.length} 个标签`)\n    \n    return JSON.stringify(exportData, null, 2)\n  } catch (error) {\n    console.error('导出数据失败:', error)\n    throw new Error('导出数据失败: ' + (error as Error).message)\n  }\n}\n\n/**\n * 导出提示词为 CSV 格式\n */\nexport async function exportPromptsAsCSV(): Promise<string> {\n  try {\n    console.log('🚀 开始导出提示词为 CSV...')\n    \n    const prompts = await api.getPrompts({ limit: 10000 })\n    const categories = await api.getCategories()\n    \n    // 创建分类映射\n    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]))\n    \n    // CSV 头部\n    const headers = [\n      'ID',\n      '标题',\n      '内容',\n      '分类',\n      '使用次数',\n      '创建时间',\n      '更新时间'\n    ]\n    \n    // CSV 数据行\n    const rows = prompts.data.map(prompt => [\n      prompt.id,\n      `\"${prompt.title.replace(/\"/g, '\"\"')}\"`, // 转义双引号\n      `\"${prompt.content.replace(/\"/g, '\"\"')}\"`,\n      categoryMap.get(prompt.category_id) || '未分类',\n      prompt.usage_count || 0,\n      prompt.created_at,\n      prompt.updated_at\n    ])\n    \n    // 组合 CSV 内容\n    const csvContent = [\n      headers.join(','),\n      ...rows.map(row => row.join(','))\n    ].join('\\n')\n    \n    console.log(`✅ CSV 导出完成: ${prompts.data.length} 个提示词`)\n    \n    return csvContent\n  } catch (error) {\n    console.error('导出 CSV 失败:', error)\n    throw new Error('导出 CSV 失败: ' + (error as Error).message)\n  }\n}\n\n/**\n * 下载文件到本地（传统方式）\n */\nexport function downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {\n  const blob = new Blob([content], { type: mimeType })\n  const url = URL.createObjectURL(blob)\n\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  link.style.display = 'none'\n\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n\n  // 清理 URL 对象\n  setTimeout(() => URL.revokeObjectURL(url), 100)\n}\n\n/**\n * 使用文件保存对话框保存文件（现代浏览器）\n */\nexport async function saveFileWithDialog(content: string, filename: string, mimeType: string = 'text/plain'): Promise<{ success: boolean; filename?: string; error?: string }> {\n  try {\n    // 检查是否支持 File System Access API\n    if ('showSaveFilePicker' in window) {\n      const fileExtension = filename.split('.').pop() || ''\n      const fileHandle = await (window as any).showSaveFilePicker({\n        suggestedName: filename,\n        types: [{\n          description: `${fileExtension.toUpperCase()} files`,\n          accept: { [mimeType]: [`.${fileExtension}`] }\n        }]\n      })\n\n      const writable = await fileHandle.createWritable()\n      await writable.write(content)\n      await writable.close()\n\n      return { success: true, filename: fileHandle.name }\n    } else {\n      // 回退到传统下载方式\n      downloadFile(content, filename, mimeType)\n      return { success: true, filename }\n    }\n  } catch (error) {\n    // 用户取消保存或其他错误\n    if ((error as Error).name === 'AbortError') {\n      return { success: false, error: '用户取消保存' }\n    }\n    return { success: false, error: (error as Error).message }\n  }\n}\n\n/**\n * 导出并保存 JSON 文件（支持文件保存对话框）\n */\nexport async function exportAndDownloadJSON() {\n  try {\n    const jsonData = await exportDataAsJSON()\n    const filename = `prompts-export-${new Date().toISOString().split('T')[0]}.json`\n\n    const result = await saveFileWithDialog(jsonData, filename, 'application/json')\n    if (result.success) {\n      return { success: true, filename: result.filename || filename }\n    } else {\n      return { success: false, error: result.error }\n    }\n  } catch (error) {\n    console.error('导出 JSON 文件失败:', error)\n    return { success: false, error: (error as Error).message }\n  }\n}\n\n/**\n * 导出并保存 CSV 文件（支持文件保存对话框）\n */\nexport async function exportAndDownloadCSV() {\n  try {\n    const csvData = await exportPromptsAsCSV()\n    const filename = `prompts-export-${new Date().toISOString().split('T')[0]}.csv`\n\n    const result = await saveFileWithDialog(csvData, filename, 'text/csv')\n    if (result.success) {\n      return { success: true, filename: result.filename || filename }\n    } else {\n      return { success: false, error: result.error }\n    }\n  } catch (error) {\n    console.error('导出 CSV 文件失败:', error)\n    return { success: false, error: (error as Error).message }\n  }\n}\n\n/**\n * 验证导入数据格式\n */\nexport function validateImportData(data: any): { valid: boolean; errors: string[] } {\n  const errors: string[] = []\n  \n  if (!data || typeof data !== 'object') {\n    errors.push('数据格式无效')\n    return { valid: false, errors }\n  }\n  \n  // 检查必需字段\n  if (!Array.isArray(data.prompts)) {\n    errors.push('缺少提示词数据或格式错误')\n  }\n  \n  if (!Array.isArray(data.categories)) {\n    errors.push('缺少分类数据或格式错误')\n  }\n  \n  // 检查提示词数据结构\n  if (data.prompts && data.prompts.length > 0) {\n    const firstPrompt = data.prompts[0]\n    if (!firstPrompt.title || !firstPrompt.content) {\n      errors.push('提示词数据缺少必需字段 (title, content)')\n    }\n  }\n  \n  return { valid: errors.length === 0, errors }\n}\n\n/**\n * 从 JSON 数据导入\n */\nexport async function importFromJSON(jsonData: string): Promise<ImportResult> {\n  const result: ImportResult = {\n    success: false,\n    imported: { prompts: 0, categories: 0, tags: 0 },\n    errors: [],\n    warnings: []\n  }\n  \n  try {\n    // 解析 JSON\n    let data: any\n    try {\n      data = JSON.parse(jsonData)\n    } catch (error) {\n      result.errors.push('JSON 格式错误')\n      return result\n    }\n    \n    // 验证数据格式\n    const validation = validateImportData(data)\n    if (!validation.valid) {\n      result.errors.push(...validation.errors)\n      return result\n    }\n    \n    console.log('🚀 开始导入数据...')\n    \n    // 导入分类\n    if (data.categories && data.categories.length > 0) {\n      for (const category of data.categories) {\n        try {\n          // 检查分类是否已存在\n          const exists = await api.checkCategoryNameExists(category.name)\n          if (!exists) {\n            await api.createCategory({\n              name: category.name,\n              description: category.description || '',\n              color: category.color || '#6366f1',\n              icon: category.icon || 'folder',\n              sortOrder: category.sortOrder || 0\n            })\n            result.imported.categories++\n          } else {\n            result.warnings.push(`分类 \"${category.name}\" 已存在，跳过`)\n          }\n        } catch (error) {\n          result.errors.push(`导入分类 \"${category.name}\" 失败: ${(error as Error).message}`)\n        }\n      }\n    }\n    \n    // 重新获取分类以建立映射\n    const categories = await api.getCategories()\n    const categoryMap = new Map(categories.map(cat => [cat.name, cat.id]))\n    \n    // 导入提示词\n    if (data.prompts && data.prompts.length > 0) {\n      for (const prompt of data.prompts) {\n        try {\n          // 查找对应的分类ID\n          let categoryId = null\n          if (prompt.categoryId) {\n            // 如果有分类信息，尝试匹配\n            const category = data.categories?.find((cat: any) => cat.id === prompt.categoryId)\n            if (category) {\n              categoryId = categoryMap.get(category.name) || null\n            }\n          }\n          \n          await api.createPrompt({\n            title: prompt.title,\n            content: prompt.content,\n            categoryId,\n            description: prompt.description || ''\n          })\n          result.imported.prompts++\n        } catch (error) {\n          result.errors.push(`导入提示词 \"${prompt.title}\" 失败: ${(error as Error).message}`)\n        }\n      }\n    }\n    \n    result.success = result.imported.prompts > 0 || result.imported.categories > 0\n    console.log(`✅ 导入完成: ${result.imported.prompts} 个提示词, ${result.imported.categories} 个分类`)\n    \n  } catch (error) {\n    result.errors.push('导入过程中发生错误: ' + (error as Error).message)\n  }\n  \n  return result\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;AA0BO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,WAAW;QACX,MAAM,CAAC,SAAS,YAAY,KAAK,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpD,uHAAA,CAAA,aAAc,CAAC;gBAAE,OAAO;YAAM;YAC9B,uHAAA,CAAA,gBAAiB;YACjB,uHAAA,CAAA,UAAW;SACZ;QAED,MAAM,aAAyB;YAC7B,SAAS,QAAQ,IAAI;YACrB;YACA;YACA,YAAY,IAAI,OAAO,WAAW;YAClC,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,AAAC,WAAuC,OAA7B,QAAQ,IAAI,CAAC,MAAM,EAAC,WAAmC,OAA1B,WAAW,MAAM,EAAC,UAAoB,OAAZ,KAAK,MAAM,EAAC;QAE1F,OAAO,KAAK,SAAS,CAAC,YAAY,MAAM;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM,IAAI,MAAM,aAAa,AAAC,MAAgB,OAAO;IACvD;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,UAAU,MAAM,uHAAA,CAAA,aAAc,CAAC;YAAE,OAAO;QAAM;QACpD,MAAM,aAAa,MAAM,uHAAA,CAAA,gBAAiB;QAE1C,SAAS;QACT,MAAM,cAAc,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,MAAO;gBAAC,IAAI,EAAE;gBAAE,IAAI,IAAI;aAAC;QAEpE,SAAS;QACT,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,UAAU;QACV,MAAM,OAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAA,SAAU;gBACtC,OAAO,EAAE;gBACR,IAAoC,OAAjC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,OAAM;gBACpC,IAAsC,OAAnC,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,OAAM;gBACvC,YAAY,GAAG,CAAC,OAAO,WAAW,KAAK;gBACvC,OAAO,WAAW,IAAI;gBACtB,OAAO,UAAU;gBACjB,OAAO,UAAU;aAClB;QAED,YAAY;QACZ,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC;SAC7B,CAAC,IAAI,CAAC;QAEP,QAAQ,GAAG,CAAC,AAAC,eAAkC,OAApB,QAAQ,IAAI,CAAC,MAAM,EAAC;QAE/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM,gBAAgB,AAAC,MAAgB,OAAO;IAC1D;AACF;AAKO,SAAS,aAAa,OAAe,EAAE,QAAgB;QAAE,WAAA,iEAAmB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,KAAK,KAAK,CAAC,OAAO,GAAG;IAErB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,YAAY;IACZ,WAAW,IAAM,IAAI,eAAe,CAAC,MAAM;AAC7C;AAKO,eAAe,mBAAmB,OAAe,EAAE,QAAgB;QAAE,WAAA,iEAAmB;IAC7F,IAAI;QACF,gCAAgC;QAChC,IAAI,wBAAwB,QAAQ;YAClC,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;YACnD,MAAM,aAAa,MAAM,AAAC,OAAe,kBAAkB,CAAC;gBAC1D,eAAe;gBACf,OAAO;oBAAC;wBACN,aAAa,AAAC,GAA8B,OAA5B,cAAc,WAAW,IAAG;wBAC5C,QAAQ;4BAAE,CAAC,SAAS,EAAE;gCAAE,IAAiB,OAAd;6BAAgB;wBAAC;oBAC9C;iBAAE;YACJ;YAEA,MAAM,WAAW,MAAM,WAAW,cAAc;YAChD,MAAM,SAAS,KAAK,CAAC;YACrB,MAAM,SAAS,KAAK;YAEpB,OAAO;gBAAE,SAAS;gBAAM,UAAU,WAAW,IAAI;YAAC;QACpD,OAAO;YACL,YAAY;YACZ,aAAa,SAAS,UAAU;YAChC,OAAO;gBAAE,SAAS;gBAAM;YAAS;QACnC;IACF,EAAE,OAAO,OAAO;QACd,cAAc;QACd,IAAI,AAAC,MAAgB,IAAI,KAAK,cAAc;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAS;QAC3C;QACA,OAAO;YAAE,SAAS;YAAO,OAAO,AAAC,MAAgB,OAAO;QAAC;IAC3D;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM;QACvB,MAAM,WAAW,AAAC,kBAAwD,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;QAE1E,MAAM,SAAS,MAAM,mBAAmB,UAAU,UAAU;QAC5D,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,UAAU,OAAO,QAAQ,IAAI;YAAS;QAChE,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAK;YAAC;QAC/C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO,AAAC,MAAgB,OAAO;QAAC;IAC3D;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM;QACtB,MAAM,WAAW,AAAC,kBAAwD,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;QAE1E,MAAM,SAAS,MAAM,mBAAmB,SAAS,UAAU;QAC3D,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,UAAU,OAAO,QAAQ,IAAI;YAAS;QAChE,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAK;YAAC;QAC/C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO,AAAC,MAAgB,OAAO;QAAC;IAC3D;AACF;AAKO,SAAS,mBAAmB,IAAS;IAC1C,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO,IAAI,CAAC;QACZ,OAAO;YAAE,OAAO;YAAO;QAAO;IAChC;IAEA,SAAS;IACT,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,UAAU,GAAG;QACnC,OAAO,IAAI,CAAC;IACd;IAEA,YAAY;IACZ,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;QAC3C,MAAM,cAAc,KAAK,OAAO,CAAC,EAAE;QACnC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,OAAO,EAAE;YAC9C,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QAAE,OAAO,OAAO,MAAM,KAAK;QAAG;IAAO;AAC9C;AAKO,eAAe,eAAe,QAAgB;IACnD,MAAM,SAAuB;QAC3B,SAAS;QACT,UAAU;YAAE,SAAS;YAAG,YAAY;YAAG,MAAM;QAAE;QAC/C,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,IAAI;QACF,UAAU;QACV,IAAI;QACJ,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO,MAAM,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,SAAS;QACT,MAAM,aAAa,mBAAmB;QACtC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,IAAI,WAAW,MAAM;YACvC,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QAEZ,OAAO;QACP,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,GAAG,GAAG;YACjD,KAAK,MAAM,YAAY,KAAK,UAAU,CAAE;gBACtC,IAAI;oBACF,YAAY;oBACZ,MAAM,SAAS,MAAM,uHAAA,CAAA,0BAA2B,CAAC,SAAS,IAAI;oBAC9D,IAAI,CAAC,QAAQ;wBACX,MAAM,uHAAA,CAAA,iBAAkB,CAAC;4BACvB,MAAM,SAAS,IAAI;4BACnB,aAAa,SAAS,WAAW,IAAI;4BACrC,OAAO,SAAS,KAAK,IAAI;4BACzB,MAAM,SAAS,IAAI,IAAI;4BACvB,WAAW,SAAS,SAAS,IAAI;wBACnC;wBACA,OAAO,QAAQ,CAAC,UAAU;oBAC5B,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,CAAC,AAAC,OAAoB,OAAd,SAAS,IAAI,EAAC;oBAC5C;gBACF,EAAE,OAAO,OAAO;oBACd,OAAO,MAAM,CAAC,IAAI,CAAC,AAAC,SAA8B,OAAtB,SAAS,IAAI,EAAC,UAAiC,OAAzB,AAAC,MAAgB,OAAO;gBAC5E;YACF;QACF;QAEA,cAAc;QACd,MAAM,aAAa,MAAM,uHAAA,CAAA,gBAAiB;QAC1C,MAAM,cAAc,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,MAAO;gBAAC,IAAI,IAAI;gBAAE,IAAI,EAAE;aAAC;QAEpE,QAAQ;QACR,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;YAC3C,KAAK,MAAM,UAAU,KAAK,OAAO,CAAE;gBACjC,IAAI;oBACF,YAAY;oBACZ,IAAI,aAAa;oBACjB,IAAI,OAAO,UAAU,EAAE;4BAEJ;wBADjB,eAAe;wBACf,MAAM,YAAW,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,IAAI,CAAC,CAAC,MAAa,IAAI,EAAE,KAAK,OAAO,UAAU;wBACjF,IAAI,UAAU;4BACZ,aAAa,YAAY,GAAG,CAAC,SAAS,IAAI,KAAK;wBACjD;oBACF;oBAEA,MAAM,uHAAA,CAAA,eAAgB,CAAC;wBACrB,OAAO,OAAO,KAAK;wBACnB,SAAS,OAAO,OAAO;wBACvB;wBACA,aAAa,OAAO,WAAW,IAAI;oBACrC;oBACA,OAAO,QAAQ,CAAC,OAAO;gBACzB,EAAE,OAAO,OAAO;oBACd,OAAO,MAAM,CAAC,IAAI,CAAC,AAAC,UAA8B,OAArB,OAAO,KAAK,EAAC,UAAiC,OAAzB,AAAC,MAAgB,OAAO;gBAC5E;YACF;QACF;QAEA,OAAO,OAAO,GAAG,OAAO,QAAQ,CAAC,OAAO,GAAG,KAAK,OAAO,QAAQ,CAAC,UAAU,GAAG;QAC7E,QAAQ,GAAG,CAAC,AAAC,WAA2C,OAAjC,OAAO,QAAQ,CAAC,OAAO,EAAC,WAAoC,OAA3B,OAAO,QAAQ,CAAC,UAAU,EAAC;IAErF,EAAE,OAAO,OAAO;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,AAAC,MAAgB,OAAO;IAC7D;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/data-import-export-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useToast } from '@/hooks/use-toast'\nimport { \n  Download, \n  Upload, \n  FileText, \n  Database,\n  CheckCircle,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\nimport { \n  exportAndDownloadJSON, \n  exportAndDownloadCSV, \n  importFromJSON,\n  type ImportResult \n} from '@/lib/data-export'\n\ninterface DataImportExportModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onImportComplete?: () => void\n}\n\nexport function DataImportExportModal({\n  open,\n  onOpenChange,\n  onImportComplete\n}: DataImportExportModalProps) {\n  const { toast } = useToast()\n  const [isExporting, setIsExporting] = useState(false)\n  const [isImporting, setIsImporting] = useState(false)\n  const [importResult, setImportResult] = useState<ImportResult | null>(null)\n  const [importProgress, setImportProgress] = useState(0)\n  const [isDragOver, setIsDragOver] = useState(false)\n\n  // 导出 JSON\n  const handleExportJSON = async () => {\n    setIsExporting(true)\n    try {\n      const result = await exportAndDownloadJSON()\n      if (result.success) {\n        toast({\n          title: \"导出成功\",\n          description: result.filename ? `数据已保存到 ${result.filename}` : \"数据已下载到默认目录\",\n        })\n      } else {\n        // 如果用户取消保存，不显示错误提示\n        if (result.error !== '用户取消保存') {\n          toast({\n            title: \"导出失败\",\n            description: result.error,\n            variant: \"destructive\",\n          })\n        }\n      }\n    } catch (error) {\n      toast({\n        title: \"导出失败\",\n        description: \"导出过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsExporting(false)\n    }\n  }\n\n  // 导出 CSV\n  const handleExportCSV = async () => {\n    setIsExporting(true)\n    try {\n      const result = await exportAndDownloadCSV()\n      if (result.success) {\n        toast({\n          title: \"导出成功\",\n          description: result.filename ? `提示词已保存到 ${result.filename}` : \"提示词已下载到默认目录\",\n        })\n      } else {\n        // 如果用户取消保存，不显示错误提示\n        if (result.error !== '用户取消保存') {\n          toast({\n            title: \"导出失败\",\n            description: result.error,\n            variant: \"destructive\",\n          })\n        }\n      }\n    } catch (error) {\n      toast({\n        title: \"导出失败\",\n        description: \"导出过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsExporting(false)\n    }\n  }\n\n  // 处理拖拽事件\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    e.dataTransfer.dropEffect = 'copy'\n    setIsDragOver(true)\n  }\n\n  const handleDragEnter = (e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsDragOver(true)\n  }\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    // 只有当离开整个拖拽区域时才设置为 false\n    if (!e.currentTarget.contains(e.relatedTarget as Node)) {\n      setIsDragOver(false)\n    }\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsDragOver(false)\n\n    const files = Array.from(e.dataTransfer.files)\n    if (files.length > 0) {\n      const file = files[0]\n      handleFileImport(file)\n    }\n  }\n\n  // 处理文件导入（支持拖拽和点击）\n  const handleFileImport = async (file: File) => {\n    if (!file) return\n\n    if (!file.name.endsWith('.json')) {\n      toast({\n        title: \"文件格式错误\",\n        description: \"请选择 JSON 格式的文件\",\n        variant: \"destructive\",\n      })\n      return\n    }\n\n    setIsImporting(true)\n    setImportProgress(0)\n    setImportResult(null)\n\n    try {\n      const fileContent = await file.text()\n\n      // 模拟进度更新\n      const progressInterval = setInterval(() => {\n        setImportProgress(prev => Math.min(prev + 10, 90))\n      }, 100)\n\n      const result = await importFromJSON(fileContent)\n\n      clearInterval(progressInterval)\n      setImportProgress(100)\n      setImportResult(result)\n\n      if (result.success) {\n        toast({\n          title: \"导入成功\",\n          description: `成功导入 ${result.imported.prompts} 个提示词和 ${result.imported.categories} 个分类`,\n        })\n\n        // 通知父组件刷新数据\n        if (onImportComplete) {\n          onImportComplete()\n        }\n      } else {\n        toast({\n          title: \"导入失败\",\n          description: \"导入过程中发生错误，请查看详细信息\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      toast({\n        title: \"导入失败\",\n        description: \"读取文件失败\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsImporting(false)\n    }\n  }\n\n  // 处理文件输入变化\n  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      handleFileImport(file)\n    }\n    // 清空文件输入以允许重复选择同一文件\n    event.target.value = ''\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            数据导入/导出\n          </DialogTitle>\n        </DialogHeader>\n\n        <Tabs defaultValue=\"export\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"export\" className=\"flex items-center gap-2\">\n              <Download className=\"h-4 w-4\" />\n              导出数据\n            </TabsTrigger>\n            <TabsTrigger value=\"import\" className=\"flex items-center gap-2\">\n              <Upload className=\"h-4 w-4\" />\n              导入数据\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"export\" className=\"space-y-4\">\n            <div className=\"grid gap-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Database className=\"h-5 w-5\" />\n                    完整数据导出 (JSON)\n                  </CardTitle>\n                  <CardDescription>\n                    导出所有提示词、分类和标签数据，可用于备份或迁移到其他设备\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <Button \n                    onClick={handleExportJSON}\n                    disabled={isExporting}\n                    className=\"w-full\"\n                  >\n                    {isExporting ? (\n                      <>\n                        <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                        导出中...\n                      </>\n                    ) : (\n                      <>\n                        <Download className=\"mr-2 h-4 w-4\" />\n                        导出 JSON 文件\n                      </>\n                    )}\n                  </Button>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <FileText className=\"h-5 w-5\" />\n                    提示词导出 (CSV)\n                  </CardTitle>\n                  <CardDescription>\n                    仅导出提示词数据为 CSV 格式，可在 Excel 等软件中查看和编辑\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <Button \n                    onClick={handleExportCSV}\n                    disabled={isExporting}\n                    variant=\"outline\"\n                    className=\"w-full\"\n                  >\n                    {isExporting ? (\n                      <>\n                        <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                        导出中...\n                      </>\n                    ) : (\n                      <>\n                        <FileText className=\"mr-2 h-4 w-4\" />\n                        导出 CSV 文件\n                      </>\n                    )}\n                  </Button>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"import\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Upload className=\"h-5 w-5\" />\n                  从 JSON 文件导入\n                </CardTitle>\n                <CardDescription>\n                  选择之前导出的 JSON 文件来导入数据。重复的分类将被跳过，提示词将被添加。\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center justify-center w-full\">\n                  <label\n                    className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${\n                      isDragOver\n                        ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-500'\n                        : 'border-gray-300 bg-gray-50 hover:bg-gray-100 dark:hover:bg-gray-800 dark:bg-gray-700 dark:border-gray-600'\n                    } ${isImporting ? 'opacity-50 cursor-not-allowed' : ''}`}\n                    onDragOver={handleDragOver}\n                    onDragEnter={handleDragEnter}\n                    onDragLeave={handleDragLeave}\n                    onDrop={handleDrop}\n                  >\n                    <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                      <Upload className={`w-8 h-8 mb-4 ${\n                        isDragOver\n                          ? 'text-blue-500 dark:text-blue-400'\n                          : 'text-gray-500 dark:text-gray-400'\n                      }`} />\n                      <p className=\"mb-2 text-sm text-gray-500 dark:text-gray-400\">\n                        <span className=\"font-semibold\">\n                          {isDragOver ? '释放文件到此处' : '点击选择文件'}\n                        </span>\n                        {!isDragOver && ' 或拖拽到此处'}\n                      </p>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        仅支持 JSON 格式文件\n                      </p>\n                    </div>\n                    <input\n                      type=\"file\"\n                      className=\"hidden\"\n                      accept=\".json\"\n                      onChange={handleFileInputChange}\n                      disabled={isImporting}\n                    />\n                  </label>\n                </div>\n\n                {isImporting && (\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <Loader2 className=\"h-4 w-4 animate-spin\" />\n                      <span className=\"text-sm\">导入中...</span>\n                    </div>\n                    <Progress value={importProgress} className=\"w-full\" />\n                  </div>\n                )}\n\n                {importResult && (\n                  <div className=\"space-y-3\">\n                    <Alert className={importResult.success ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\"}>\n                      <div className=\"flex items-center gap-2\">\n                        {importResult.success ? (\n                          <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                        ) : (\n                          <AlertCircle className=\"h-4 w-4 text-red-600\" />\n                        )}\n                        <AlertDescription className={importResult.success ? \"text-green-800\" : \"text-red-800\"}>\n                          {importResult.success ? \"导入成功！\" : \"导入失败\"}\n                        </AlertDescription>\n                      </div>\n                    </Alert>\n\n                    {importResult.success && (\n                      <div className=\"text-sm space-y-1\">\n                        <p>✅ 导入了 {importResult.imported.prompts} 个提示词</p>\n                        <p>✅ 导入了 {importResult.imported.categories} 个分类</p>\n                      </div>\n                    )}\n\n                    {importResult.warnings.length > 0 && (\n                      <div className=\"text-sm space-y-1\">\n                        <p className=\"font-medium text-yellow-600\">警告:</p>\n                        {importResult.warnings.map((warning, index) => (\n                          <p key={index} className=\"text-yellow-600\">⚠️ {warning}</p>\n                        ))}\n                      </div>\n                    )}\n\n                    {importResult.errors.length > 0 && (\n                      <div className=\"text-sm space-y-1\">\n                        <p className=\"font-medium text-red-600\">错误:</p>\n                        {importResult.errors.map((error, index) => (\n                          <p key={index} className=\"text-red-600\">❌ {error}</p>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAnBA;;;;;;;;;;;AAgCO,SAAS,sBAAsB,KAIT;QAJS,EACpC,IAAI,EACJ,YAAY,EACZ,gBAAgB,EACW,GAJS;;IAKpC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,UAAU;IACV,MAAM,mBAAmB;QACvB,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,QAAQ,GAAG,AAAC,UAAyB,OAAhB,OAAO,QAAQ,IAAK;gBAC/D;YACF,OAAO;gBACL,mBAAmB;gBACnB,IAAI,OAAO,KAAK,KAAK,UAAU;oBAC7B,MAAM;wBACJ,OAAO;wBACP,aAAa,OAAO,KAAK;wBACzB,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,QAAQ,GAAG,AAAC,WAA0B,OAAhB,OAAO,QAAQ,IAAK;gBAChE;YACF,OAAO;gBACL,mBAAmB;gBACnB,IAAI,OAAO,KAAK,KAAK,UAAU;oBAC7B,MAAM;wBACJ,OAAO;wBACP,aAAa,OAAO,KAAK;wBACzB,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,EAAE,YAAY,CAAC,UAAU,GAAG;QAC5B,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,yBAAyB;QACzB,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,GAAW;YACtD,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,iBAAiB;QACnB;IACF;IAEA,kBAAkB;IAClB,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;YAChC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAEhB,IAAI;YACF,MAAM,cAAc,MAAM,KAAK,IAAI;YAEnC,SAAS;YACT,MAAM,mBAAmB,YAAY;gBACnC,kBAAkB,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,IAAI;YAChD,GAAG;YAEH,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;YAEpC,cAAc;YACd,kBAAkB;YAClB,gBAAgB;YAEhB,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,AAAC,QAAwC,OAAjC,OAAO,QAAQ,CAAC,OAAO,EAAC,WAAoC,OAA3B,OAAO,QAAQ,CAAC,UAAU,EAAC;gBACnF;gBAEA,YAAY;gBACZ,IAAI,kBAAkB;oBACpB;gBACF;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;YAChB;QAAb,MAAM,QAAO,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;QACA,oBAAoB;QACpB,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAKpC,6LAAC,4HAAA,CAAA,OAAI;oBAAC,cAAa;oBAAS,WAAU;;sCACpC,6LAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAKlC,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4HAAA,CAAA,OAAI;;0DACH,6LAAC,4HAAA,CAAA,aAAU;;kEACT,6LAAC,4HAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC,4HAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,4BACC;;0EACE,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAA8B;;qFAInD;;0EACE,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAQ/C,6LAAC,4HAAA,CAAA,OAAI;;0DACH,6LAAC,4HAAA,CAAA,aAAU;;kEACT,6LAAC,4HAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC,4HAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,SAAQ;oDACR,WAAU;8DAET,4BACC;;0EACE,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAA8B;;qFAInD;;0EACE,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUnD,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6LAAC,4HAAA,CAAA,OAAI;;kDACH,6LAAC,4HAAA,CAAA,aAAU;;0DACT,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGhC,6LAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAW,AAAC,4HAIR,OAHF,aACI,wEACA,6GACL,KAAsD,OAAnD,cAAc,kCAAkC;oDACpD,YAAY;oDACZ,aAAa;oDACb,aAAa;oDACb,QAAQ;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAW,AAAC,gBAInB,OAHC,aACI,qCACA;;;;;;8EAEN,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAU;sFACb,aAAa,YAAY;;;;;;wEAE3B,CAAC,cAAc;;;;;;;8EAElB,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;sEAI1D,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,QAAO;4DACP,UAAU;4DACV,UAAU;;;;;;;;;;;;;;;;;4CAKf,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC,gIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAgB,WAAU;;;;;;;;;;;;4CAI9C,8BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,WAAW,aAAa,OAAO,GAAG,iCAAiC;kEACxE,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,aAAa,OAAO,iBACnB,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EAEzB,6LAAC,6HAAA,CAAA,mBAAgB;oEAAC,WAAW,aAAa,OAAO,GAAG,mBAAmB;8EACpE,aAAa,OAAO,GAAG,UAAU;;;;;;;;;;;;;;;;;oDAKvC,aAAa,OAAO,kBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAE;oEAAO,aAAa,QAAQ,CAAC,OAAO;oEAAC;;;;;;;0EACxC,6LAAC;;oEAAE;oEAAO,aAAa,QAAQ,CAAC,UAAU;oEAAC;;;;;;;;;;;;;oDAI9C,aAAa,QAAQ,CAAC,MAAM,GAAG,mBAC9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;4DAC1C,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;oEAAc,WAAU;;wEAAkB;wEAAI;;mEAAvC;;;;;;;;;;;oDAKb,aAAa,MAAM,CAAC,MAAM,GAAG,mBAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA2B;;;;;;4DACvC,aAAa,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,6LAAC;oEAAc,WAAU;;wEAAe;wEAAG;;mEAAnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalC;GAvXgB;;QAKI,wHAAA,CAAA,WAAQ;;;KALZ", "debugId": null}}, {"offset": {"line": 6736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\nimport { Check } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className,\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n));\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\n\nexport { Checkbox };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sQACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6791, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/batch-operations.ts"], "sourcesContent": ["/**\n * 批量操作工具\n * 支持批量选择、删除、移动分类等操作\n */\n\nimport * as api from '@/lib/api/client'\n\n// 批量操作结果类型\nexport interface BatchOperationResult {\n  success: boolean\n  total: number\n  successful: number\n  failed: number\n  errors: string[]\n  warnings: string[]\n}\n\n// 批量选择状态管理\nexport class BatchSelectionManager {\n  private selectedIds = new Set<string>()\n  private callbacks: Array<(selectedIds: Set<string>) => void> = []\n\n  // 添加选择变化监听器\n  onSelectionChange(callback: (selectedIds: Set<string>) => void) {\n    this.callbacks.push(callback)\n  }\n\n  // 移除监听器\n  removeListener(callback: (selectedIds: Set<string>) => void) {\n    this.callbacks = this.callbacks.filter(cb => cb !== callback)\n  }\n\n  // 通知选择变化\n  private notifyChange() {\n    this.callbacks.forEach(callback => callback(new Set(this.selectedIds)))\n  }\n\n  // 选择单个项目\n  select(id: string) {\n    this.selectedIds.add(id)\n    this.notifyChange()\n  }\n\n  // 取消选择单个项目\n  deselect(id: string) {\n    this.selectedIds.delete(id)\n    this.notifyChange()\n  }\n\n  // 切换选择状态\n  toggle(id: string) {\n    if (this.selectedIds.has(id)) {\n      this.deselect(id)\n    } else {\n      this.select(id)\n    }\n  }\n\n  // 全选\n  selectAll(ids: string[]) {\n    ids.forEach(id => this.selectedIds.add(id))\n    this.notifyChange()\n  }\n\n  // 取消全选\n  deselectAll() {\n    this.selectedIds.clear()\n    this.notifyChange()\n  }\n\n  // 反选\n  invertSelection(allIds: string[]) {\n    const newSelection = new Set<string>()\n    allIds.forEach(id => {\n      if (!this.selectedIds.has(id)) {\n        newSelection.add(id)\n      }\n    })\n    this.selectedIds = newSelection\n    this.notifyChange()\n  }\n\n  // 获取选中的项目\n  getSelected(): string[] {\n    return Array.from(this.selectedIds)\n  }\n\n  // 获取选中数量\n  getSelectedCount(): number {\n    return this.selectedIds.size\n  }\n\n  // 检查是否选中\n  isSelected(id: string): boolean {\n    return this.selectedIds.has(id)\n  }\n\n  // 检查是否有选中项目\n  hasSelection(): boolean {\n    return this.selectedIds.size > 0\n  }\n\n  // 检查是否全选\n  isAllSelected(allIds: string[]): boolean {\n    return allIds.length > 0 && allIds.every(id => this.selectedIds.has(id))\n  }\n\n  // 检查是否部分选中\n  isPartiallySelected(allIds: string[]): boolean {\n    const selectedCount = allIds.filter(id => this.selectedIds.has(id)).length\n    return selectedCount > 0 && selectedCount < allIds.length\n  }\n}\n\n/**\n * 批量删除提示词\n */\nexport async function batchDeletePrompts(promptIds: string[]): Promise<BatchOperationResult> {\n  const result: BatchOperationResult = {\n    success: false,\n    total: promptIds.length,\n    successful: 0,\n    failed: 0,\n    errors: [],\n    warnings: []\n  }\n\n  if (promptIds.length === 0) {\n    result.warnings.push('没有选择要删除的提示词')\n    return result\n  }\n\n  console.log(`🚀 开始批量删除 ${promptIds.length} 个提示词...`)\n\n  for (const promptId of promptIds) {\n    try {\n      await api.deletePrompt(promptId)\n      result.successful++\n      console.log(`✅ 删除提示词 ${promptId} 成功`)\n    } catch (error) {\n      result.failed++\n      const errorMessage = `删除提示词 ${promptId} 失败: ${(error as Error).message}`\n      result.errors.push(errorMessage)\n      console.error(`❌ ${errorMessage}`)\n    }\n  }\n\n  result.success = result.successful > 0\n  console.log(`✅ 批量删除完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)\n\n  return result\n}\n\n/**\n * 批量移动提示词到指定分类\n */\nexport async function batchMovePrompts(promptIds: string[], targetCategoryId: string | null): Promise<BatchOperationResult> {\n  const result: BatchOperationResult = {\n    success: false,\n    total: promptIds.length,\n    successful: 0,\n    failed: 0,\n    errors: [],\n    warnings: []\n  }\n\n  if (promptIds.length === 0) {\n    result.warnings.push('没有选择要移动的提示词')\n    return result\n  }\n\n  const categoryName = targetCategoryId ? '指定分类' : '未分类'\n  console.log(`🚀 开始批量移动 ${promptIds.length} 个提示词到 ${categoryName}...`)\n\n  for (const promptId of promptIds) {\n    try {\n      // 获取提示词详情\n      const prompt = await api.getPromptById(promptId)\n\n      // 更新分类\n      await api.updatePrompt(promptId, {\n        title: prompt.title,\n        content: prompt.content,\n        categoryId: targetCategoryId,\n        description: prompt.description\n      })\n      \n      result.successful++\n      console.log(`✅ 移动提示词 ${promptId} 成功`)\n    } catch (error) {\n      result.failed++\n      const errorMessage = `移动提示词 ${promptId} 失败: ${(error as Error).message}`\n      result.errors.push(errorMessage)\n      console.error(`❌ ${errorMessage}`)\n    }\n  }\n\n  result.success = result.successful > 0\n  console.log(`✅ 批量移动完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)\n\n  return result\n}\n\n/**\n * 批量复制提示词\n */\nexport async function batchCopyPrompts(promptIds: string[]): Promise<BatchOperationResult> {\n  const result: BatchOperationResult = {\n    success: false,\n    total: promptIds.length,\n    successful: 0,\n    failed: 0,\n    errors: [],\n    warnings: []\n  }\n\n  if (promptIds.length === 0) {\n    result.warnings.push('没有选择要复制的提示词')\n    return result\n  }\n\n  console.log(`🚀 开始批量复制 ${promptIds.length} 个提示词...`)\n\n  for (const promptId of promptIds) {\n    try {\n      // 获取提示词详情\n      const prompt = await api.getPromptById(promptId)\n\n      // 创建副本\n      await api.createPrompt({\n        title: `${prompt.title} (副本)`,\n        content: prompt.content,\n        categoryId: prompt.categoryId,\n        description: prompt.description\n      })\n      \n      result.successful++\n      console.log(`✅ 复制提示词 ${promptId} 成功`)\n    } catch (error) {\n      result.failed++\n      const errorMessage = `复制提示词 ${promptId} 失败: ${(error as Error).message}`\n      result.errors.push(errorMessage)\n      console.error(`❌ ${errorMessage}`)\n    }\n  }\n\n  result.success = result.successful > 0\n  console.log(`✅ 批量复制完成: 成功 ${result.successful} 个, 失败 ${result.failed} 个`)\n\n  return result\n}\n\n/**\n * 批量导出选中的提示词\n */\nexport async function batchExportPrompts(promptIds: string[]): Promise<{ success: boolean; data?: string; error?: string }> {\n  if (promptIds.length === 0) {\n    return { success: false, error: '没有选择要导出的提示词' }\n  }\n\n  try {\n    console.log(`🚀 开始批量导出 ${promptIds.length} 个提示词...`)\n\n    // 获取所有选中的提示词详情\n    const prompts = await Promise.all(\n      promptIds.map(id => api.getPromptById(id))\n    )\n\n    // 获取分类信息用于映射\n    const categories = await api.getCategories()\n    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]))\n\n    // 构建导出数据\n    const exportData = {\n      prompts: prompts.map(prompt => ({\n        ...prompt,\n        categoryName: categoryMap.get(prompt.categoryId) || '未分类'\n      })),\n      exportTime: new Date().toISOString(),\n      version: '1.0.0',\n      type: 'batch_export'\n    }\n\n    const jsonData = JSON.stringify(exportData, null, 2)\n    console.log(`✅ 批量导出完成: ${prompts.length} 个提示词`)\n\n    return { success: true, data: jsonData }\n  } catch (error) {\n    const errorMessage = `批量导出失败: ${(error as Error).message}`\n    console.error(`❌ ${errorMessage}`)\n    return { success: false, error: errorMessage }\n  }\n}\n\n/**\n * 获取批量操作的确认消息\n */\nexport function getBatchOperationConfirmMessage(operation: string, count: number, details?: string): string {\n  const messages = {\n    delete: `确定要删除选中的 ${count} 个提示词吗？此操作不可撤销。`,\n    move: `确定要将选中的 ${count} 个提示词移动到${details || '指定分类'}吗？`,\n    copy: `确定要复制选中的 ${count} 个提示词吗？将创建 ${count} 个副本。`,\n    export: `确定要导出选中的 ${count} 个提示词吗？`\n  }\n\n  return messages[operation as keyof typeof messages] || `确定要对选中的 ${count} 个项目执行 ${operation} 操作吗？`\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;;;AAaO,MAAM;IAIX,YAAY;IACZ,kBAAkB,QAA4C,EAAE;QAC9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACtB;IAEA,QAAQ;IACR,eAAe,QAA4C,EAAE;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IACtD;IAEA,SAAS;IACD,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS,IAAI,IAAI,IAAI,CAAC,WAAW;IACtE;IAEA,SAAS;IACT,OAAO,EAAU,EAAE;QACjB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,YAAY;IACnB;IAEA,WAAW;IACX,SAAS,EAAU,EAAE;QACnB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACxB,IAAI,CAAC,YAAY;IACnB;IAEA,SAAS;IACT,OAAO,EAAU,EAAE;QACjB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK;YAC5B,IAAI,CAAC,QAAQ,CAAC;QAChB,OAAO;YACL,IAAI,CAAC,MAAM,CAAC;QACd;IACF;IAEA,KAAK;IACL,UAAU,GAAa,EAAE;QACvB,IAAI,OAAO,CAAC,CAAA,KAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;IACnB;IAEA,OAAO;IACP,cAAc;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK;QACtB,IAAI,CAAC,YAAY;IACnB;IAEA,KAAK;IACL,gBAAgB,MAAgB,EAAE;QAChC,MAAM,eAAe,IAAI;QACzB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK;gBAC7B,aAAa,GAAG,CAAC;YACnB;QACF;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY;IACnB;IAEA,UAAU;IACV,cAAwB;QACtB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW;IACpC;IAEA,SAAS;IACT,mBAA2B;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IAEA,SAAS;IACT,WAAW,EAAU,EAAW;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IAC9B;IAEA,YAAY;IACZ,eAAwB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG;IACjC;IAEA,SAAS;IACT,cAAc,MAAgB,EAAW;QACvC,OAAO,OAAO,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC,CAAA,KAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IACtE;IAEA,WAAW;IACX,oBAAoB,MAAgB,EAAW;QAC7C,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,KAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,MAAM;QAC1E,OAAO,gBAAgB,KAAK,gBAAgB,OAAO,MAAM;IAC3D;;QA5FA,+KAAQ,eAAc,IAAI;QAC1B,+KAAQ,aAAuD,EAAE;;AA4FnE;AAKO,eAAe,mBAAmB,SAAmB;IAC1D,MAAM,SAA+B;QACnC,SAAS;QACT,OAAO,UAAU,MAAM;QACvB,YAAY;QACZ,QAAQ;QACR,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,QAAQ,CAAC,IAAI,CAAC;QACrB,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,AAAC,aAA6B,OAAjB,UAAU,MAAM,EAAC;IAE1C,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI;YACF,MAAM,uHAAA,CAAA,eAAgB,CAAC;YACvB,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC,AAAC,WAAmB,OAAT,UAAS;QAClC,EAAE,OAAO,OAAO;YACd,OAAO,MAAM;YACb,MAAM,eAAe,AAAC,SAAwB,OAAhB,UAAS,SAAgC,OAAzB,AAAC,MAAgB,OAAO;YACtE,OAAO,MAAM,CAAC,IAAI,CAAC;YACnB,QAAQ,KAAK,CAAC,AAAC,KAAiB,OAAb;QACrB;IACF;IAEA,OAAO,OAAO,GAAG,OAAO,UAAU,GAAG;IACrC,QAAQ,GAAG,CAAC,AAAC,gBAA0C,OAA3B,OAAO,UAAU,EAAC,WAAuB,OAAd,OAAO,MAAM,EAAC;IAErE,OAAO;AACT;AAKO,eAAe,iBAAiB,SAAmB,EAAE,gBAA+B;IACzF,MAAM,SAA+B;QACnC,SAAS;QACT,OAAO,UAAU,MAAM;QACvB,YAAY;QACZ,QAAQ;QACR,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,QAAQ,CAAC,IAAI,CAAC;QACrB,OAAO;IACT;IAEA,MAAM,eAAe,mBAAmB,SAAS;IACjD,QAAQ,GAAG,CAAC,AAAC,aAAsC,OAA1B,UAAU,MAAM,EAAC,WAAsB,OAAb,cAAa;IAEhE,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI;YACF,UAAU;YACV,MAAM,SAAS,MAAM,uHAAA,CAAA,gBAAiB,CAAC;YAEvC,OAAO;YACP,MAAM,uHAAA,CAAA,eAAgB,CAAC,UAAU;gBAC/B,OAAO,OAAO,KAAK;gBACnB,SAAS,OAAO,OAAO;gBACvB,YAAY;gBACZ,aAAa,OAAO,WAAW;YACjC;YAEA,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC,AAAC,WAAmB,OAAT,UAAS;QAClC,EAAE,OAAO,OAAO;YACd,OAAO,MAAM;YACb,MAAM,eAAe,AAAC,SAAwB,OAAhB,UAAS,SAAgC,OAAzB,AAAC,MAAgB,OAAO;YACtE,OAAO,MAAM,CAAC,IAAI,CAAC;YACnB,QAAQ,KAAK,CAAC,AAAC,KAAiB,OAAb;QACrB;IACF;IAEA,OAAO,OAAO,GAAG,OAAO,UAAU,GAAG;IACrC,QAAQ,GAAG,CAAC,AAAC,gBAA0C,OAA3B,OAAO,UAAU,EAAC,WAAuB,OAAd,OAAO,MAAM,EAAC;IAErE,OAAO;AACT;AAKO,eAAe,iBAAiB,SAAmB;IACxD,MAAM,SAA+B;QACnC,SAAS;QACT,OAAO,UAAU,MAAM;QACvB,YAAY;QACZ,QAAQ;QACR,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,QAAQ,CAAC,IAAI,CAAC;QACrB,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,AAAC,aAA6B,OAAjB,UAAU,MAAM,EAAC;IAE1C,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI;YACF,UAAU;YACV,MAAM,SAAS,MAAM,uHAAA,CAAA,gBAAiB,CAAC;YAEvC,OAAO;YACP,MAAM,uHAAA,CAAA,eAAgB,CAAC;gBACrB,OAAO,AAAC,GAAe,OAAb,OAAO,KAAK,EAAC;gBACvB,SAAS,OAAO,OAAO;gBACvB,YAAY,OAAO,UAAU;gBAC7B,aAAa,OAAO,WAAW;YACjC;YAEA,OAAO,UAAU;YACjB,QAAQ,GAAG,CAAC,AAAC,WAAmB,OAAT,UAAS;QAClC,EAAE,OAAO,OAAO;YACd,OAAO,MAAM;YACb,MAAM,eAAe,AAAC,SAAwB,OAAhB,UAAS,SAAgC,OAAzB,AAAC,MAAgB,OAAO;YACtE,OAAO,MAAM,CAAC,IAAI,CAAC;YACnB,QAAQ,KAAK,CAAC,AAAC,KAAiB,OAAb;QACrB;IACF;IAEA,OAAO,OAAO,GAAG,OAAO,UAAU,GAAG;IACrC,QAAQ,GAAG,CAAC,AAAC,gBAA0C,OAA3B,OAAO,UAAU,EAAC,WAAuB,OAAd,OAAO,MAAM,EAAC;IAErE,OAAO;AACT;AAKO,eAAe,mBAAmB,SAAmB;IAC1D,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAc;IAChD;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,AAAC,aAA6B,OAAjB,UAAU,MAAM,EAAC;QAE1C,eAAe;QACf,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,UAAU,GAAG,CAAC,CAAA,KAAM,uHAAA,CAAA,gBAAiB,CAAC;QAGxC,aAAa;QACb,MAAM,aAAa,MAAM,uHAAA,CAAA,gBAAiB;QAC1C,MAAM,cAAc,IAAI,IAAI,WAAW,GAAG,CAAC,CAAA,MAAO;gBAAC,IAAI,EAAE;gBAAE,IAAI,IAAI;aAAC;QAEpE,SAAS;QACT,MAAM,aAAa;YACjB,SAAS,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC9B,GAAG,MAAM;oBACT,cAAc,YAAY,GAAG,CAAC,OAAO,UAAU,KAAK;gBACtD,CAAC;YACD,YAAY,IAAI,OAAO,WAAW;YAClC,SAAS;YACT,MAAM;QACR;QAEA,MAAM,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;QAClD,QAAQ,GAAG,CAAC,AAAC,aAA2B,OAAf,QAAQ,MAAM,EAAC;QAExC,OAAO;YAAE,SAAS;YAAM,MAAM;QAAS;IACzC,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,AAAC,WAAmC,OAAzB,AAAC,MAAgB,OAAO;QACxD,QAAQ,KAAK,CAAC,AAAC,KAAiB,OAAb;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAKO,SAAS,gCAAgC,SAAiB,EAAE,KAAa,EAAE,OAAgB;IAChG,MAAM,WAAW;QACf,QAAQ,AAAC,YAAiB,OAAN,OAAM;QAC1B,MAAM,AAAC,WAA0B,OAAhB,OAAM,YAA4B,OAAlB,WAAW,QAAO;QACnD,MAAM,AAAC,YAA8B,OAAnB,OAAM,eAAmB,OAAN,OAAM;QAC3C,QAAQ,AAAC,YAAiB,OAAN,OAAM;IAC5B;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI,AAAC,WAAyB,OAAf,OAAM,WAAmB,OAAV,WAAU;AAC7F", "debugId": null}}, {"offset": {"line": 7055, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/batch-operations-toolbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuSeparator, \n  DropdownMenuTrigger \n} from '@/components/ui/dropdown-menu'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { useToast } from '@/hooks/use-toast'\nimport { \n  Trash2, \n  Copy, \n  Move, \n  Download, \n  ChevronDown,\n  X,\n  CheckSquare,\n  Square,\n  Minus\n} from 'lucide-react'\nimport { \n  batchDeletePrompts, \n  batchMovePrompts, \n  batchCopyPrompts, \n  batchExportPrompts,\n  getBatchOperationConfirmMessage,\n  type BatchSelectionManager \n} from '@/lib/batch-operations'\nimport { downloadFile } from '@/lib/data-export'\n\ninterface BatchOperationsToolbarProps {\n  selectionManager: BatchSelectionManager\n  selectedIds: string[]\n  allIds: string[]\n  categories: Array<{ id: string; name: string }>\n  onOperationComplete: () => void\n  className?: string\n}\n\nexport function BatchOperationsToolbar({\n  selectionManager,\n  selectedIds,\n  allIds,\n  categories,\n  onOperationComplete,\n  className = ''\n}: BatchOperationsToolbarProps) {\n  const { toast } = useToast()\n  const [isOperating, setIsOperating] = useState(false)\n\n  const selectedCount = selectedIds.length\n  const totalCount = allIds.length\n  const hasSelection = selectedCount > 0\n  const isAllSelected = selectionManager.isAllSelected(allIds)\n  const isPartiallySelected = selectionManager.isPartiallySelected(allIds)\n\n  // 处理全选/取消全选\n  const handleSelectAll = () => {\n    if (isAllSelected) {\n      selectionManager.deselectAll()\n    } else {\n      selectionManager.selectAll(allIds)\n    }\n  }\n\n  // 处理批量删除\n  const handleBatchDelete = async () => {\n    if (!hasSelection) return\n\n    const confirmMessage = getBatchOperationConfirmMessage('delete', selectedCount)\n    if (!confirm(confirmMessage)) return\n\n    setIsOperating(true)\n    try {\n      const result = await batchDeletePrompts(selectedIds)\n      \n      if (result.success) {\n        toast({\n          title: \"批量删除成功\",\n          description: `成功删除 ${result.successful} 个提示词${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,\n        })\n        \n        // 清空选择\n        selectionManager.deselectAll()\n        onOperationComplete()\n      } else {\n        toast({\n          title: \"批量删除失败\",\n          description: result.errors[0] || \"删除过程中发生错误\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      toast({\n        title: \"批量删除失败\",\n        description: \"操作过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsOperating(false)\n    }\n  }\n\n  // 处理批量移动\n  const handleBatchMove = async (targetCategoryId: string) => {\n    if (!hasSelection) return\n\n    const targetCategory = categories.find(cat => cat.id === targetCategoryId)\n    const categoryName = targetCategory?.name || '未分类'\n    \n    const confirmMessage = getBatchOperationConfirmMessage('move', selectedCount, categoryName)\n    if (!confirm(confirmMessage)) return\n\n    setIsOperating(true)\n    try {\n      const result = await batchMovePrompts(selectedIds, targetCategoryId === 'none' ? null : targetCategoryId)\n      \n      if (result.success) {\n        toast({\n          title: \"批量移动成功\",\n          description: `成功移动 ${result.successful} 个提示词到 ${categoryName}${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,\n        })\n        \n        // 清空选择\n        selectionManager.deselectAll()\n        onOperationComplete()\n      } else {\n        toast({\n          title: \"批量移动失败\",\n          description: result.errors[0] || \"移动过程中发生错误\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      toast({\n        title: \"批量移动失败\",\n        description: \"操作过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsOperating(false)\n    }\n  }\n\n  // 处理批量复制\n  const handleBatchCopy = async () => {\n    if (!hasSelection) return\n\n    const confirmMessage = getBatchOperationConfirmMessage('copy', selectedCount)\n    if (!confirm(confirmMessage)) return\n\n    setIsOperating(true)\n    try {\n      const result = await batchCopyPrompts(selectedIds)\n      \n      if (result.success) {\n        toast({\n          title: \"批量复制成功\",\n          description: `成功复制 ${result.successful} 个提示词${result.failed > 0 ? `，失败 ${result.failed} 个` : ''}`,\n        })\n        \n        // 清空选择\n        selectionManager.deselectAll()\n        onOperationComplete()\n      } else {\n        toast({\n          title: \"批量复制失败\",\n          description: result.errors[0] || \"复制过程中发生错误\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      toast({\n        title: \"批量复制失败\",\n        description: \"操作过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsOperating(false)\n    }\n  }\n\n  // 处理批量导出\n  const handleBatchExport = async () => {\n    if (!hasSelection) return\n\n    const confirmMessage = getBatchOperationConfirmMessage('export', selectedCount)\n    if (!confirm(confirmMessage)) return\n\n    setIsOperating(true)\n    try {\n      const result = await batchExportPrompts(selectedIds)\n      \n      if (result.success && result.data) {\n        const filename = `batch-export-${selectedCount}-prompts-${new Date().toISOString().split('T')[0]}.json`\n        downloadFile(result.data, filename, 'application/json')\n        \n        toast({\n          title: \"批量导出成功\",\n          description: `成功导出 ${selectedCount} 个提示词到 ${filename}`,\n        })\n      } else {\n        toast({\n          title: \"批量导出失败\",\n          description: result.error || \"导出过程中发生错误\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      toast({\n        title: \"批量导出失败\",\n        description: \"操作过程中发生错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsOperating(false)\n    }\n  }\n\n  // 如果没有项目，不显示工具栏\n  if (totalCount === 0) {\n    return null\n  }\n\n  return (\n    <div className={`flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border ${className}`}>\n      {/* 全选复选框 */}\n      <div className=\"flex items-center gap-2\">\n        <Checkbox\n          checked={isAllSelected}\n          ref={(el) => {\n            if (el) {\n              el.indeterminate = isPartiallySelected\n            }\n          }}\n          onCheckedChange={handleSelectAll}\n          disabled={isOperating}\n        />\n        <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {hasSelection ? `已选择 ${selectedCount} / ${totalCount}` : `全选 (${totalCount})`}\n        </span>\n      </div>\n\n      {/* 批量操作按钮 */}\n      {hasSelection && (\n        <>\n          <div className=\"h-4 w-px bg-gray-300 dark:bg-gray-600\" />\n          \n          {/* 批量删除 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleBatchDelete}\n            disabled={isOperating}\n            className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n          >\n            <Trash2 className=\"h-4 w-4 mr-1\" />\n            删除 ({selectedCount})\n          </Button>\n\n          {/* 批量移动 */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={isOperating}\n              >\n                <Move className=\"h-4 w-4 mr-1\" />\n                移动到\n                <ChevronDown className=\"h-3 w-3 ml-1\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent>\n              <DropdownMenuItem onClick={() => handleBatchMove('none')}>\n                未分类\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              {categories.map(category => (\n                <DropdownMenuItem \n                  key={category.id}\n                  onClick={() => handleBatchMove(category.id)}\n                >\n                  {category.name}\n                </DropdownMenuItem>\n              ))}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* 批量复制 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleBatchCopy}\n            disabled={isOperating}\n          >\n            <Copy className=\"h-4 w-4 mr-1\" />\n            复制\n          </Button>\n\n          {/* 批量导出 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleBatchExport}\n            disabled={isOperating}\n          >\n            <Download className=\"h-4 w-4 mr-1\" />\n            导出\n          </Button>\n\n          {/* 取消选择 */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => selectionManager.deselectAll()}\n            disabled={isOperating}\n          >\n            <X className=\"h-4 w-4 mr-1\" />\n            取消选择\n          </Button>\n        </>\n      )}\n\n      {/* 操作状态指示 */}\n      {isOperating && (\n        <div className=\"flex items-center gap-2 text-sm text-blue-600\">\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent\" />\n          操作中...\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAcA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAQA;;;AAvCA;;;;;;;;;AAkDO,SAAS,uBAAuB,KAOT;QAPS,EACrC,gBAAgB,EAChB,WAAW,EACX,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,YAAY,EAAE,EACc,GAPS;;IAQrC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,YAAY,MAAM;IACxC,MAAM,aAAa,OAAO,MAAM;IAChC,MAAM,eAAe,gBAAgB;IACrC,MAAM,gBAAgB,iBAAiB,aAAa,CAAC;IACrD,MAAM,sBAAsB,iBAAiB,mBAAmB,CAAC;IAEjE,YAAY;IACZ,MAAM,kBAAkB;QACtB,IAAI,eAAe;YACjB,iBAAiB,WAAW;QAC9B,OAAO;YACL,iBAAiB,SAAS,CAAC;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,kCAA+B,AAAD,EAAE,UAAU;QACjE,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,AAAC,QAAgC,OAAzB,OAAO,UAAU,EAAC,SAAyD,OAAlD,OAAO,MAAM,GAAG,IAAI,AAAC,OAAoB,OAAd,OAAO,MAAM,EAAC,QAAM;gBAC/F;gBAEA,OAAO;gBACP,iBAAiB,WAAW;gBAC5B;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,MAAM,CAAC,EAAE,IAAI;oBACjC,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,cAAc;QAEnB,MAAM,iBAAiB,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACzD,MAAM,eAAe,CAAA,2BAAA,qCAAA,eAAgB,IAAI,KAAI;QAE7C,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,kCAA+B,AAAD,EAAE,QAAQ,eAAe;QAC9E,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,qBAAqB,SAAS,OAAO;YAExF,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,AAAC,QAAkC,OAA3B,OAAO,UAAU,EAAC,WAAwB,OAAf,cAAiE,OAAlD,OAAO,MAAM,GAAG,IAAI,AAAC,OAAoB,OAAd,OAAO,MAAM,EAAC,QAAM;gBAChH;gBAEA,OAAO;gBACP,iBAAiB,WAAW;gBAC5B;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,MAAM,CAAC,EAAE,IAAI;oBACjC,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QAEnB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,kCAA+B,AAAD,EAAE,QAAQ;QAC/D,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE;YAEtC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,AAAC,QAAgC,OAAzB,OAAO,UAAU,EAAC,SAAyD,OAAlD,OAAO,MAAM,GAAG,IAAI,AAAC,OAAoB,OAAd,OAAO,MAAM,EAAC,QAAM;gBAC/F;gBAEA,OAAO;gBACP,iBAAiB,WAAW;gBAC5B;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,MAAM,CAAC,EAAE,IAAI;oBACjC,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,kCAA+B,AAAD,EAAE,UAAU;QACjE,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,MAAM,WAAW,AAAC,gBAAwC,OAAzB,eAAc,aAAkD,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;gBACjG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI,EAAE,UAAU;gBAEpC,MAAM;oBACJ,OAAO;oBACP,aAAa,AAAC,QAA8B,OAAvB,eAAc,WAAkB,OAAT;gBAC9C;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa,OAAO,KAAK,IAAI;oBAC7B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,GAAG;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,6EAAsF,OAAV;;0BAE3F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBACP,SAAS;wBACT,KAAK,CAAC;4BACJ,IAAI,IAAI;gCACN,GAAG,aAAa,GAAG;4BACrB;wBACF;wBACA,iBAAiB;wBACjB,UAAU;;;;;;kCAEZ,6LAAC;wBAAK,WAAU;kCACb,eAAe,AAAC,OAAyB,OAAnB,eAAc,OAAgB,OAAX,cAAe,AAAC,OAAiB,OAAX,YAAW;;;;;;;;;;;;YAK9E,8BACC;;kCACE,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;4BAC9B;4BAAc;;;;;;;kCAIrB,6LAAC,wIAAA,CAAA,eAAY;;0CACX,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;sDAEjC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG3B,6LAAC,wIAAA,CAAA,sBAAmB;;kDAClB,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,gBAAgB;kDAAS;;;;;;kDAG1D,6LAAC,wIAAA,CAAA,wBAAqB;;;;;oCACrB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,wIAAA,CAAA,mBAAgB;4CAEf,SAAS,IAAM,gBAAgB,SAAS,EAAE;sDAEzC,SAAS,IAAI;2CAHT,SAAS,EAAE;;;;;;;;;;;;;;;;;kCAUxB,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAKnC,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAKvC,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,WAAW;wBAC3C,UAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;YAOnC,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;oBAAoF;;;;;;;;;;;;;AAM7G;GAtSgB;;QAQI,wHAAA,CAAA,WAAQ;;;KARZ", "debugId": null}}, {"offset": {"line": 7477, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/selectable-prompt-card.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Badge } from '@/components/ui/badge'\nimport { useToast } from '@/hooks/use-toast'\nimport { useCopyHistory } from '@/components/copy-history'\nimport {\n  Copy,\n  Edit,\n  Trash2,\n  Eye,\n  Calendar,\n  BarChart3,\n  Tag\n} from 'lucide-react'\nimport { formatDistanceToNow } from 'date-fns'\nimport { zhCN } from 'date-fns/locale'\nimport * as api from '@/lib/api/client'\nimport type { PromptWithDetails } from '@/lib/types'\nimport type { BatchSelectionManager } from '@/lib/batch-operations'\n\ninterface SelectablePromptCardProps {\n  prompt: PromptWithDetails\n  categoryName?: string\n  categoryColor?: string\n  tags?: Array<{ id: string; name: string; color: string }>\n  selectionManager: BatchSelectionManager\n  isSelected: boolean\n  isSelectionMode: boolean\n  onEdit?: (prompt: PromptWithDetails) => void\n  onDelete?: (prompt: PromptWithDetails) => void\n  onView?: (prompt: PromptWithDetails) => void\n  onCopySuccess?: () => void\n}\n\nexport function SelectablePromptCard({\n  prompt,\n  categoryName,\n  categoryColor,\n  tags = [],\n  selectionManager,\n  isSelected,\n  isSelectionMode,\n  onEdit,\n  onDelete,\n  onView,\n  onCopySuccess\n}: SelectablePromptCardProps) {\n  const { toast } = useToast()\n  const { addCopyRecord } = useCopyHistory()\n  const [isCopying, setIsCopying] = useState(false)\n\n  // 处理复制\n  const handleCopy = async (e: React.MouseEvent) => {\n    e.stopPropagation()\n\n    if (isCopying) return\n\n    setIsCopying(true)\n    try {\n      // 复制到剪贴板\n      await navigator.clipboard.writeText(prompt.content)\n\n      // 增加使用次数\n      await api.incrementUsageCount(prompt.id)\n\n      // 添加到复制历史\n      addCopyRecord(\n        prompt.id,\n        prompt.title,\n        prompt.content,\n        categoryName && categoryColor ? {\n          name: categoryName,\n          color: categoryColor\n        } : undefined\n      )\n\n      toast({\n        title: \"复制成功\",\n        description: \"提示词已复制到剪贴板\",\n      })\n\n      if (onCopySuccess) {\n        onCopySuccess()\n      }\n    } catch (error) {\n      console.error('复制失败:', error)\n      toast({\n        title: \"复制失败\",\n        description: \"无法复制到剪贴板\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsCopying(false)\n    }\n  }\n\n  // 处理选择\n  const handleSelect = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    selectionManager.toggle(prompt.id)\n  }\n\n  // 处理卡片点击\n  const handleCardClick = () => {\n    if (isSelectionMode) {\n      selectionManager.toggle(prompt.id)\n    } else if (onView) {\n      onView(prompt)\n    }\n  }\n\n  // 处理编辑\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    if (onEdit) {\n      onEdit(prompt)\n    }\n  }\n\n  // 处理删除\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    if (onDelete) {\n      onDelete(prompt)\n    }\n  }\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    try {\n      return formatDistanceToNow(new Date(dateString), { \n        addSuffix: true, \n        locale: zhCN \n      })\n    } catch {\n      return '未知时间'\n    }\n  }\n\n  return (\n    <Card \n      className={`group cursor-pointer transition-all duration-200 hover:shadow-md ${\n        isSelected ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' : ''\n      } ${isSelectionMode ? 'hover:bg-gray-50 dark:hover:bg-gray-800' : ''}`}\n      onClick={handleCardClick}\n    >\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start gap-3 flex-1 min-w-0\">\n            {/* 选择复选框 */}\n            {isSelectionMode && (\n              <Checkbox\n                checked={isSelected}\n                onCheckedChange={() => selectionManager.toggle(prompt.id)}\n                onClick={handleSelect}\n                className=\"mt-1\"\n              />\n            )}\n            \n            {/* 标题和分类 */}\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"font-semibold text-lg mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors\">\n                {prompt.title}\n              </h3>\n              \n              {/* 分类标签 */}\n              {categoryName && (\n                <div className=\"flex items-center gap-1 mb-2\">\n                  <Tag className=\"h-3 w-3 text-gray-500\" />\n                  <Badge\n                    variant=\"secondary\"\n                    className=\"text-xs\"\n                    style={categoryColor ? {\n                      backgroundColor: `${categoryColor}20`,\n                      color: categoryColor,\n                      borderColor: `${categoryColor}40`\n                    } : undefined}\n                  >\n                    {categoryName}\n                  </Badge>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          {!isSelectionMode && (\n            <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n              {onView && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    onView(prompt)\n                  }}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <Eye className=\"h-4 w-4\" />\n                </Button>\n              )}\n              \n              {onEdit && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleEdit}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <Edit className=\"h-4 w-4\" />\n                </Button>\n              )}\n              \n              {onDelete && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleDelete}\n                  className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          )}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"pt-0\">\n        {/* 内容预览 */}\n        <div className=\"mb-4\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 line-clamp-3 leading-relaxed\">\n            {prompt.content}\n          </p>\n        </div>\n\n        {/* 标签 */}\n        {tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-1 mb-4\">\n            {tags.slice(0, 4).map(tag => (\n              <Badge \n                key={tag.id} \n                variant=\"outline\" \n                className=\"text-xs\"\n                style={{ borderColor: tag.color, color: tag.color }}\n              >\n                {tag.name}\n              </Badge>\n            ))}\n            {tags.length > 4 && (\n              <Badge variant=\"outline\" className=\"text-xs text-gray-500\">\n                +{tags.length - 4}\n              </Badge>\n            )}\n          </div>\n        )}\n\n        {/* 底部信息和操作 */}\n        <div className=\"flex items-center justify-between\">\n          {/* 统计信息 */}\n          <div className=\"flex items-center gap-4 text-xs text-gray-500\">\n            <div className=\"flex items-center gap-1\">\n              <BarChart3 className=\"h-3 w-3\" />\n              <span>{prompt.usage_count || 0} 次使用</span>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Calendar className=\"h-3 w-3\" />\n              <span>{formatDate(prompt.updatedAt)}</span>\n            </div>\n          </div>\n\n          {/* 复制按钮 */}\n          {!isSelectionMode && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleCopy}\n              disabled={isCopying}\n              className=\"h-8\"\n            >\n              <Copy className=\"h-3 w-3 mr-1\" />\n              {isCopying ? '复制中...' : '复制'}\n            </Button>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AApBA;;;;;;;;;;;;AAsCO,SAAS,qBAAqB,KAYT;QAZS,EACnC,MAAM,EACN,YAAY,EACZ,aAAa,EACb,OAAO,EAAE,EACT,gBAAgB,EAChB,UAAU,EACV,eAAe,EACf,MAAM,EACN,QAAQ,EACR,MAAM,EACN,aAAa,EACa,GAZS;;IAanC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,OAAO;IACP,MAAM,aAAa,OAAO;QACxB,EAAE,eAAe;QAEjB,IAAI,WAAW;QAEf,aAAa;QACb,IAAI;YACF,SAAS;YACT,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YAElD,SAAS;YACT,MAAM,uHAAA,CAAA,sBAAuB,CAAC,OAAO,EAAE;YAEvC,UAAU;YACV,cACE,OAAO,EAAE,EACT,OAAO,KAAK,EACZ,OAAO,OAAO,EACd,gBAAgB,gBAAgB;gBAC9B,MAAM;gBACN,OAAO;YACT,IAAI;YAGN,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,IAAI,eAAe;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,iBAAiB,MAAM,CAAC,OAAO,EAAE;IACnC;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,IAAI,iBAAiB;YACnB,iBAAiB,MAAM,CAAC,OAAO,EAAE;QACnC,OAAO,IAAI,QAAQ;YACjB,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,EAAE,eAAe;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa;gBAC/C,WAAW;gBACX,QAAQ,oJAAA,CAAA,OAAI;YACd;QACF,EAAE,UAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QACH,WAAW,AAAC,oEAER,OADF,aAAa,qDAAqD,IACnE,KAAoE,OAAjE,kBAAkB,4CAA4C;QAClE,SAAS;;0BAET,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAEZ,iCACC,6LAAC,gIAAA,CAAA,WAAQ;oCACP,SAAS;oCACT,iBAAiB,IAAM,iBAAiB,MAAM,CAAC,OAAO,EAAE;oCACxD,SAAS;oCACT,WAAU;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;wCAId,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC,6HAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;oDACV,OAAO,gBAAgB;wDACrB,iBAAiB,AAAC,GAAgB,OAAd,eAAc;wDAClC,OAAO;wDACP,aAAa,AAAC,GAAgB,OAAd,eAAc;oDAChC,IAAI;8DAEH;;;;;;;;;;;;;;;;;;;;;;;;wBAQV,CAAC,iCACA,6LAAC;4BAAI,WAAU;;gCACZ,wBACC,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,OAAO;oCACT;oCACA,WAAU;8CAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;gCAIlB,wBACC,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAInB,0BACC,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV,OAAO,OAAO;;;;;;;;;;;oBAKlB,KAAK,MAAM,GAAG,mBACb,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,oBACpB,6LAAC,6HAAA,CAAA,QAAK;oCAEJ,SAAQ;oCACR,WAAU;oCACV,OAAO;wCAAE,aAAa,IAAI,KAAK;wCAAE,OAAO,IAAI,KAAK;oCAAC;8CAEjD,IAAI,IAAI;mCALJ,IAAI,EAAE;;;;;4BAQd,KAAK,MAAM,GAAG,mBACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAwB;oCACvD,KAAK,MAAM,GAAG;;;;;;;;;;;;;kCAOxB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;;oDAAM,OAAO,WAAW,IAAI;oDAAE;;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAM,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;4BAKrC,CAAC,iCACA,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA9PgB;;QAaI,wHAAA,CAAA,WAAQ;QACA,iIAAA,CAAA,iBAAc;;;KAd1B", "debugId": null}}, {"offset": {"line": 7909, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/components/ui/skeleton.tsx"], "sourcesContent": ["/**\n * 骨架屏组件\n * 用于在数据加载时显示占位符，改善用户体验\n */\n\nimport { cn } from '@/lib/utils'\n\ninterface SkeletonProps {\n  className?: string\n  children?: React.ReactNode\n}\n\nexport function Skeleton({ className, children, ...props }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-md bg-gray-200 dark:bg-gray-700\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n}\n\n// 预定义的骨架屏组件\nexport function PromptCardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6 space-y-4\">\n      {/* 标题和操作按钮 */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"space-y-2 flex-1\">\n          <Skeleton className=\"h-6 w-3/4\" />\n          <div className=\"flex items-center space-x-2\">\n            <Skeleton className=\"h-4 w-4 rounded-full\" />\n            <Skeleton className=\"h-4 w-16\" />\n          </div>\n        </div>\n        <div className=\"flex space-x-2\">\n          <Skeleton className=\"h-8 w-8 rounded\" />\n          <Skeleton className=\"h-8 w-8 rounded\" />\n          <Skeleton className=\"h-8 w-8 rounded\" />\n        </div>\n      </div>\n\n      {/* 内容预览 */}\n      <div className=\"space-y-2\">\n        <Skeleton className=\"h-4 w-full\" />\n        <Skeleton className=\"h-4 w-5/6\" />\n        <Skeleton className=\"h-4 w-4/5\" />\n      </div>\n\n      {/* 底部信息 */}\n      <div className=\"flex items-center justify-between pt-4\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-1\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-12\" />\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <Skeleton className=\"h-4 w-4\" />\n            <Skeleton className=\"h-4 w-16\" />\n          </div>\n        </div>\n        <Skeleton className=\"h-8 w-16 rounded\" />\n      </div>\n\n      {/* 标签 */}\n      <div className=\"flex flex-wrap gap-2\">\n        <Skeleton className=\"h-6 w-12 rounded-full\" />\n        <Skeleton className=\"h-6 w-16 rounded-full\" />\n      </div>\n    </div>\n  )\n}\n\nexport function CategoryCardSkeleton() {\n  return (\n    <div className=\"flex items-center space-x-3 p-3 rounded-lg border border-gray-200\">\n      <Skeleton className=\"h-8 w-8 rounded\" />\n      <div className=\"flex-1 space-y-1\">\n        <Skeleton className=\"h-4 w-20\" />\n      </div>\n      <Skeleton className=\"h-5 w-8 rounded-full\" />\n    </div>\n  )\n}\n\nexport function SearchResultSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索结果头部 */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-6 w-32\" />\n          <Skeleton className=\"h-4 w-48\" />\n        </div>\n        <Skeleton className=\"h-8 w-24 rounded\" />\n      </div>\n\n      {/* 搜索结果列表 */}\n      <div className=\"space-y-4\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <PromptCardSkeleton key={i} />\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function DashboardSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      {/* 分类区域 */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"space-y-2\">\n            <Skeleton className=\"h-6 w-16\" />\n            <Skeleton className=\"h-4 w-24\" />\n          </div>\n          <Skeleton className=\"h-8 w-8 rounded\" />\n        </div>\n\n        {/* 分类列表 */}\n        <div className=\"space-y-2\">\n          <CategoryCardSkeleton />\n          {Array.from({ length: 4 }).map((_, i) => (\n            <CategoryCardSkeleton key={i} />\n          ))}\n        </div>\n\n        <Skeleton className=\"h-10 w-full rounded\" />\n      </div>\n\n      {/* 提示词区域 */}\n      <div className=\"space-y-4\">\n        {/* 搜索框 */}\n        <div className=\"flex items-center space-x-2\">\n          <Skeleton className=\"h-4 w-4\" />\n          <Skeleton className=\"h-10 flex-1 rounded\" />\n          <Skeleton className=\"h-10 w-20 rounded\" />\n        </div>\n\n        {/* 提示词列表 */}\n        <div className=\"space-y-2\">\n          <div className=\"space-y-2\">\n            <Skeleton className=\"h-6 w-32\" />\n            <Skeleton className=\"h-4 w-24\" />\n          </div>\n\n          <div className=\"grid gap-4\">\n            {Array.from({ length: 4 }).map((_, i) => (\n              <PromptCardSkeleton key={i} />\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 复制历史区域 */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center space-x-2\">\n              <Skeleton className=\"h-4 w-4\" />\n              <Skeleton className=\"h-5 w-20\" />\n            </div>\n            <Skeleton className=\"h-4 w-40\" />\n          </div>\n          <Skeleton className=\"h-8 w-16 rounded\" />\n        </div>\n\n        <div className=\"space-y-3\">\n          {Array.from({ length: 2 }).map((_, i) => (\n            <div key={i} className=\"flex items-start space-x-3 p-3 rounded-lg border border-gray-200\">\n              <div className=\"flex-1 space-y-2\">\n                <Skeleton className=\"h-4 w-32\" />\n                <Skeleton className=\"h-3 w-full\" />\n                <Skeleton className=\"h-3 w-24\" />\n              </div>\n              <Skeleton className=\"h-6 w-6 rounded\" />\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// 通用加载状态组件\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-6 w-6',\n    lg: 'h-8 w-8'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={cn(\n        \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\",\n        sizeClasses[size]\n      )} />\n    </div>\n  )\n}\n\nexport function LoadingPage() {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px]\">\n      <div className=\"text-center space-y-4\">\n        <LoadingSpinner size=\"lg\" />\n        <p className=\"text-gray-500\">加载中...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;;AAOO,SAAS,SAAS,KAAgD;QAAhD,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAsB,GAAhD;IACvB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAZgB;AAeT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;;;;;;;kCAGxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;0CAEtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;;;;;;;kCAGxB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;;;;;;;AAI5B;MAhDgB;AAkDT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAS,WAAU;;;;;;0BACpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAS,WAAU;;;;;;;;;;;0BAEtB,6LAAC;gBAAS,WAAU;;;;;;;;;;;;AAG1B;MAVgB;AAYT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;kCAEtB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,wBAAwB;;;;;;;;;;;;;;;;AAKnC;MApBgB;AAsBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;0CAEtB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;;;;4BACA,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,0BAA0B;;;;;;;;;;;kCAI/B,6LAAC;wBAAS,WAAU;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,wBAAwB;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAS,WAAU;;;;;;0DACpB,6LAAC;gDAAS,WAAU;;;;;;;;;;;;kDAEtB,6LAAC;wCAAS,WAAU;;;;;;;;;;;;0CAEtB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAS,WAAU;;;;;;0DACpB,6LAAC;gDAAS,WAAU;;;;;;0DACpB,6LAAC;gDAAS,WAAU;;;;;;;;;;;;kDAEtB,6LAAC;wCAAS,WAAU;;;;;;;+BANZ;;;;;;;;;;;;;;;;;;;;;;AAatB;MA5EgB;AA+ET,SAAS,eAAe,KAA8C;QAA9C,EAAE,OAAO,IAAI,EAAiC,GAA9C;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAIzB;MAfgB;AAiBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;;;;;;8BACrB,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;MATgB", "debugId": null}}, {"offset": {"line": 8623, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useCallback } from 'react'\n\nimport { Sidebar } from '@/components/sidebar'\nimport { SearchBar } from '@/components/search-bar'\nimport { PromptCard } from '@/components/prompt-card'\nimport { DashboardHeader } from '@/components/dashboard-header'\nimport { CopyHistory, useCopyHistory } from '@/components/copy-history'\nimport { PromptDetailModal } from '@/components/prompt-detail-modal'\nimport { PromptFormModal } from '@/components/prompt-form-modal'\nimport { DeleteConfirmDialog } from '@/components/delete-confirm-dialog'\nimport { CategoryFormModal } from '@/components/category-form-modal'\nimport { DataImportExportModal } from '@/components/data-import-export-modal'\nimport { BatchOperationsToolbar } from '@/components/batch-operations-toolbar'\nimport { SelectablePromptCard } from '@/components/selectable-prompt-card'\nimport { BatchSelectionManager } from '@/lib/batch-operations'\n// 移除冲突解决模态框，不再需要\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { useToast } from '@/hooks/use-toast'\nimport { DashboardSkeleton, LoadingPage } from '@/components/ui/skeleton'\n// 移除本地优先存储依赖，直接使用 API\n\n// 直接使用 API 客户端\nimport * as api from '@/lib/api/client'\nimport type { \n  PromptWithDetails, \n  CategoryWithCount, \n  SearchHistory,\n  SearchParams \n} from '@/types/database'\n\nexport default function DashboardPage() {\n  const [prompts, setPrompts] = useState<PromptWithDetails[]>([])\n  const [allPrompts, setAllPrompts] = useState<PromptWithDetails[]>([]) // 本地缓存所有数据\n  const [categories, setCategories] = useState<CategoryWithCount[]>([])\n  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])\n  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>()\n  const [searchQuery, setSearchQuery] = useState('')\n  const [isLoading, setIsLoading] = useState(true)\n  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false)\n  const [isLoadingCategories, setIsLoadingCategories] = useState(false)\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [hasMore, setHasMore] = useState(false)\n  const [total, setTotal] = useState(0)\n  const [isLocalSearch, setIsLocalSearch] = useState(true) // 标记是否使用本地搜索\n\n  // 模态框状态\n  const [selectedPrompt, setSelectedPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)\n  const [isFormModalOpen, setIsFormModalOpen] = useState(false)\n  const [editingPrompt, setEditingPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)\n  const [deletingPrompt, setDeletingPrompt] = useState<PromptWithDetails | null>(null)\n  const [isDeleting, setIsDeleting] = useState(false)\n\n  // 分类管理状态\n  const [isCategoryFormModalOpen, setIsCategoryFormModalOpen] = useState(false)\n  const [editingCategory, setEditingCategory] = useState<CategoryWithCount | null>(null)\n  const [isCategoryDeleteDialogOpen, setIsCategoryDeleteDialogOpen] = useState(false)\n  const [deletingCategory, setDeletingCategory] = useState<CategoryWithCount | null>(null)\n\n  // 数据导入/导出状态\n  const [isImportExportModalOpen, setIsImportExportModalOpen] = useState(false)\n\n  // 批量操作状态\n  const [selectionManager] = useState(() => new BatchSelectionManager())\n  const [selectedIds, setSelectedIds] = useState<string[]>([])\n  const [isSelectionMode, setIsSelectionMode] = useState(false)\n  const [isDeletingCategory, setIsDeletingCategory] = useState(false)\n  const [copyHistoryWidth, setCopyHistoryWidth] = useState(400) // 复制历史面板宽度\n  const [isResizing, setIsResizing] = useState(false)\n\n  // 冲突解决状态\n  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false)\n  const [hasConflicts, setHasConflicts] = useState(false)\n\n  const { toast } = useToast()\n  const { addCopyRecord } = useCopyHistory()\n\n  // 初始化数据\n  useEffect(() => {\n    initializeApp()\n  }, [])\n\n  // 监听批量选择变化\n  useEffect(() => {\n    const handleSelectionChange = (selectedIds: Set<string>) => {\n      setSelectedIds(Array.from(selectedIds))\n      setIsSelectionMode(selectedIds.size > 0)\n    }\n\n    selectionManager.onSelectionChange(handleSelectionChange)\n\n    return () => {\n      selectionManager.removeListener(handleSelectionChange)\n    }\n  }, [selectionManager])\n\n  // 简化版本：不再检查冲突，因为新版本没有复杂的同步机制\n  useEffect(() => {\n    setHasConflicts(false)\n  }, [])\n\n  // 本地搜索函数\n  const performLocalSearch = useCallback((query: string, categoryId?: string) => {\n    let filteredPrompts = allPrompts\n\n    // 按分类过滤\n    if (categoryId) {\n      filteredPrompts = filteredPrompts.filter(prompt => prompt.category_id === categoryId)\n    }\n\n    // 按搜索词过滤\n    if (query.trim()) {\n      const searchTerm = query.toLowerCase()\n      filteredPrompts = filteredPrompts.filter(prompt =>\n        prompt.title.toLowerCase().includes(searchTerm) ||\n        prompt.content.toLowerCase().includes(searchTerm) ||\n        prompt.tags?.some(tag => tag.name.toLowerCase().includes(searchTerm))\n      )\n    }\n\n    // 按更新时间排序\n    filteredPrompts.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())\n\n    setPrompts(filteredPrompts)\n    setTotal(filteredPrompts.length)\n    setHasMore(false) // 本地搜索不需要分页\n  }, [allPrompts])\n\n  // 监听搜索和分类变化\n  useEffect(() => {\n    if (isLocalSearch && allPrompts.length > 0) {\n      // 使用本地搜索\n      performLocalSearch(searchQuery, selectedCategoryId)\n    } else {\n      // 使用远程搜索\n      loadPrompts()\n    }\n  }, [selectedCategoryId, searchQuery, currentPage, isLocalSearch, allPrompts.length, performLocalSearch])\n\n  const initializeApp = async () => {\n    try {\n      setIsLoading(true)\n\n      console.log('🚀 开始初始化应用 - 直接 API 模式')\n\n      // 并行加载所有数据\n      await Promise.all([\n        loadAllPrompts(),\n        loadCategories(),\n        loadSearchHistory()\n      ])\n\n      console.log('✅ 应用初始化完成')\n    } catch (error) {\n      console.error('初始化应用失败:', error)\n      toast({\n        title: \"初始化失败\",\n        description: \"应用初始化时出现错误，请刷新页面重试\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // 直接从 API 加载所有提示词\n  const loadAllPrompts = async () => {\n    try {\n      console.log('🚀 直接从 API 加载提示词')\n      const response = await api.getPrompts({\n        sortBy: 'updated_at',\n        sortOrder: 'desc',\n        limit: 1000 // 设置一个较大的限制，获取所有数据\n      })\n\n      setAllPrompts(response.data)\n\n      // 初始显示（无搜索条件时）\n      if (!searchQuery && !selectedCategoryId) {\n        setPrompts(response.data.slice(0, 12)) // 只显示前12个\n        setTotal(response.data.length)\n        setHasMore(response.data.length > 12)\n      }\n\n      console.log(`✅ 加载了 ${response.data.length} 个提示词`)\n    } catch (error) {\n      console.error('加载所有提示词失败:', error)\n      // 如果加载失败，回退到远程搜索模式\n      setIsLocalSearch(false)\n    }\n  }\n\n  const loadPrompts = async () => {\n    try {\n      setIsLoadingPrompts(true)\n      const params: SearchParams = {\n        query: searchQuery || undefined,\n        categoryId: selectedCategoryId,\n        sortBy: 'updated_at',\n        sortOrder: 'desc',\n        limit: 12,\n        offset: (currentPage - 1) * 12\n      }\n\n      const response = await api.getPrompts(params)\n\n      if (currentPage === 1) {\n        setPrompts(response.data)\n      } else {\n        setPrompts(prev => [...prev, ...response.data])\n      }\n\n      setHasMore(response.hasMore)\n      setTotal(response.total)\n    } catch (error) {\n      console.error('加载提示词失败:', error)\n      toast({\n        title: \"加载失败\",\n        description: \"无法加载提示词列表\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsLoadingPrompts(false)\n    }\n  }\n\n  const loadCategories = async () => {\n    try {\n      setIsLoadingCategories(true)\n      console.log('🚀 直接从 API 加载分类')\n      const data = await api.getCategories()\n      setCategories(data)\n      console.log(`✅ 加载了 ${data.length} 个分类`)\n    } catch (error) {\n      console.error('加载分类失败:', error)\n    } finally {\n      setIsLoadingCategories(false)\n    }\n  }\n\n  const loadSearchHistory = async () => {\n    try {\n      const data = await api.getSearchHistory(10)\n      setSearchHistory(data)\n    } catch (error) {\n      console.error('加载搜索历史失败:', error)\n    }\n  }\n\n  const handleSearch = useCallback(async (query: string) => {\n    // 实时搜索：直接在当前页面进行搜索\n    setSearchQuery(query)\n    setCurrentPage(1)\n\n    // 如果有搜索词，添加到搜索历史\n    if (query.trim()) {\n      try {\n        await api.addSearchHistory(query.trim())\n        // 重新加载搜索历史\n        loadSearchHistory()\n      } catch (error) {\n        console.error('添加搜索历史失败:', error)\n      }\n    }\n  }, [])\n\n  // 处理远程搜索\n  const handleRemoteSearch = useCallback(async (query: string) => {\n    try {\n      setIsLocalSearch(false) // 切换到远程搜索模式\n      setSearchQuery(query)\n      setCurrentPage(1)\n\n      // 添加到搜索历史\n      if (query.trim()) {\n        try {\n          await api.addSearchHistory(query.trim())\n          loadSearchHistory()\n        } catch (error) {\n          console.error('添加搜索历史失败:', error)\n        }\n      }\n    } catch (error) {\n      console.error('远程搜索失败:', error)\n    }\n  }, [])\n\n  const handleCategorySelect = (categoryId: string | undefined) => {\n    setSelectedCategoryId(categoryId)\n    setCurrentPage(1)\n  }\n\n  // 处理复制历史面板宽度调整\n  const handleResizeStart = (e: React.MouseEvent) => {\n    setIsResizing(true)\n    e.preventDefault()\n\n    const startX = e.clientX\n    const startWidth = copyHistoryWidth\n\n    const handleMouseMove = (e: MouseEvent) => {\n      const deltaX = startX - e.clientX\n      const newWidth = Math.max(300, Math.min(600, startWidth + deltaX))\n      setCopyHistoryWidth(newWidth)\n    }\n\n    const handleMouseUp = () => {\n      setIsResizing(false)\n      document.removeEventListener('mousemove', handleMouseMove)\n      document.removeEventListener('mouseup', handleMouseUp)\n    }\n\n    document.addEventListener('mousemove', handleMouseMove)\n    document.addEventListener('mouseup', handleMouseUp)\n  }\n\n  const handlePromptCopy = async (content: string, promptId: string) => {\n    try {\n      // 增加使用次数\n      await api.incrementPromptUsage(promptId)\n\n      // 添加到复制历史\n      const prompt = prompts.find(p => p.id === promptId)\n      if (prompt) {\n        addCopyRecord(\n          promptId,\n          prompt.title,\n          content,\n          prompt.category ? {\n            name: prompt.category.name,\n            color: prompt.category.color\n          } : undefined\n        )\n      }\n\n      // 更新本地状态中的使用次数，避免重新加载\n      setPrompts(prevPrompts =>\n        prevPrompts.map(p =>\n          p.id === promptId\n            ? { ...p, usage_count: p.usage_count + 1 }\n            : p\n        )\n      )\n    } catch (error) {\n      console.error('更新使用次数失败:', error)\n    }\n  }\n\n  const handlePromptView = async (promptOrId: string | PromptWithDetails) => {\n    // 处理参数，支持传递字符串ID或完整的提示词对象\n    const promptId = typeof promptOrId === 'string' ? promptOrId : promptOrId.id\n\n    // 如果传递的是完整对象，直接使用\n    if (typeof promptOrId === 'object' && promptOrId.id) {\n      setSelectedPrompt(promptOrId)\n      setIsDetailModalOpen(true)\n      return\n    }\n\n    // 首先从 allPrompts 数组查找数据\n    let prompt = allPrompts.find(p => p.id === promptId)\n\n    // 如果在本地数组中找不到，直接从API获取最新数据\n    if (!prompt) {\n      try {\n        const response = await fetch('/api/prompts')\n        const result = await response.json()\n        const latestPrompts = result.data\n        prompt = latestPrompts.find(p => p.id === promptId)\n\n        // 同时更新本地数据\n        if (prompt) {\n          setAllPrompts(latestPrompts)\n        }\n      } catch (error) {\n        console.error('获取最新数据失败:', error)\n      }\n    }\n\n    if (prompt) {\n      setSelectedPrompt(prompt)\n      setIsDetailModalOpen(true)\n    } else {\n      console.error('未找到提示词:', promptId)\n      toast({\n        title: \"加载失败\",\n        description: \"未找到指定的提示词\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handlePromptEdit = async (promptOrId: string | PromptWithDetails) => {\n    // 处理参数，支持传递字符串ID或完整的提示词对象\n    const promptId = typeof promptOrId === 'string' ? promptOrId : promptOrId.id\n\n    // 如果传递的是完整对象，直接使用\n    if (typeof promptOrId === 'object' && promptOrId.id) {\n      setEditingPrompt(promptOrId)\n      setIsFormModalOpen(true)\n      return\n    }\n\n    // 首先从 allPrompts 数组查找数据\n    let prompt = allPrompts.find(p => p.id === promptId)\n\n    // 如果在本地数组中找不到，直接从API获取最新数据\n    if (!prompt) {\n      try {\n        const response = await fetch('/api/prompts')\n        const result = await response.json()\n        const latestPrompts = result.data\n        prompt = latestPrompts.find(p => p.id === promptId)\n\n        // 同时更新本地数据\n        if (prompt) {\n          setAllPrompts(latestPrompts)\n        }\n      } catch (error) {\n        console.error('获取最新数据失败:', error)\n      }\n    }\n\n    if (prompt) {\n      setEditingPrompt(prompt)\n      setIsFormModalOpen(true)\n    } else {\n      console.error('未找到提示词:', promptId)\n      toast({\n        title: \"加载失败\",\n        description: \"未找到指定的提示词\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handlePromptDelete = async (promptOrId: string | PromptWithDetails) => {\n    // 处理参数，支持传递字符串ID或完整的提示词对象\n    const promptId = typeof promptOrId === 'string' ? promptOrId : promptOrId.id\n\n    // 如果传递的是完整对象，直接使用\n    if (typeof promptOrId === 'object' && promptOrId.id) {\n      setDeletingPrompt(promptOrId)\n      setIsDeleteDialogOpen(true)\n      return\n    }\n\n    // 首先从 allPrompts 数组查找数据\n    let prompt = allPrompts.find(p => p.id === promptId)\n\n    // 如果在本地数组中找不到，直接从API获取最新数据\n    if (!prompt) {\n      try {\n        const response = await fetch('/api/prompts')\n        const result = await response.json()\n        const latestPrompts = result.data\n        prompt = latestPrompts.find(p => p.id === promptId)\n\n        // 同时更新本地数据\n        if (prompt) {\n          setAllPrompts(latestPrompts)\n        }\n      } catch (error) {\n        console.error('获取最新数据失败:', error)\n      }\n    }\n\n    if (prompt) {\n      setDeletingPrompt(prompt)\n      setIsDeleteDialogOpen(true)\n    } else {\n      console.error('未找到提示词:', promptId)\n      toast({\n        title: \"删除失败\",\n        description: \"未找到指定的提示词\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const handleCategoryCreate = () => {\n    setEditingCategory(null)\n    setIsCategoryFormModalOpen(true)\n  }\n\n  const handleCategoryEdit = (categoryId: string) => {\n    const category = categories.find(c => c.id === categoryId)\n    if (category) {\n      setEditingCategory(category)\n      setIsCategoryFormModalOpen(true)\n    }\n  }\n\n  const handleCategoryDelete = (categoryId: string) => {\n    const category = categories.find(c => c.id === categoryId)\n    if (category) {\n      setDeletingCategory(category)\n      setIsCategoryDeleteDialogOpen(true)\n    }\n  }\n\n  const loadMore = () => {\n    if (hasMore && !isLoading) {\n      setCurrentPage(prev => prev + 1)\n    }\n  }\n\n  const handleCreatePrompt = () => {\n    setEditingPrompt(null)\n    setIsFormModalOpen(true)\n  }\n\n  const handleFormSuccess = (newPrompt?: any) => {\n    setIsFormModalOpen(false)\n    setEditingPrompt(null)\n\n    // 如果是新创建的提示词，立即更新本地状态\n    if (newPrompt) {\n      setAllPrompts(prev => [newPrompt, ...prev])\n      setPrompts(prev => [newPrompt, ...prev.slice(0, 11)]) // 保持12个\n      setTotal(prev => prev + 1)\n      console.log('✅ 新提示词已添加到本地状态')\n    } else {\n      // 编辑模式，重新加载数据\n      loadAllPrompts()\n    }\n\n    loadCategories()\n  }\n\n  const handleDeleteConfirm = async () => {\n    if (!deletingPrompt) return\n\n    try {\n      setIsDeleting(true)\n      console.log('🚀 直接删除提示词')\n\n      // 直接调用 API 删除\n      await api.deletePrompt(deletingPrompt.id)\n      const success = true\n\n      if (success) {\n        // 立即从本地状态中移除\n        setAllPrompts(prev => prev.filter(p => p.id !== deletingPrompt.id))\n        setPrompts(prev => prev.filter(p => p.id !== deletingPrompt.id))\n\n        toast({\n          title: \"删除成功\",\n          description: \"提示词已成功删除\",\n        })\n      } else {\n        throw new Error('删除失败')\n      }\n\n      setIsDeleteDialogOpen(false)\n      setDeletingPrompt(null)\n      await loadPrompts()\n      await loadCategories()\n    } catch (error) {\n      console.error('删除提示词失败:', error)\n      toast({\n        title: \"删除失败\",\n        description: \"删除提示词时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsDeleting(false)\n    }\n  }\n\n  const handleClearSearchHistory = useCallback(async () => {\n    try {\n      await api.clearSearchHistory()\n      setSearchHistory([])\n      toast({\n        title: \"清除成功\",\n        description: \"搜索历史已清除\",\n      })\n    } catch (error) {\n      console.error('清除搜索历史失败:', error)\n      toast({\n        title: \"清除失败\",\n        description: \"清除搜索历史时出现错误\",\n        variant: \"destructive\",\n      })\n    }\n  }, [toast])\n\n  const handleCategoryFormSuccess = () => {\n    loadCategories()\n  }\n\n  // 处理数据导入完成\n  const handleImportComplete = () => {\n    // 重新加载所有数据\n    initializeApp()\n  }\n\n  const handleCategoryDeleteConfirm = async () => {\n    if (!deletingCategory) return\n\n    try {\n      setIsDeletingCategory(true)\n      console.log('🚀 直接删除分类（首页）')\n\n      // 直接调用 API 删除\n      await api.deleteCategory(deletingCategory.id)\n      const success = true\n\n      if (success) {\n        // 立即从本地状态中移除\n        setCategories(prev => prev.filter(c => c.id !== deletingCategory.id))\n\n        toast({\n          title: \"删除成功\",\n          description: \"分类已成功删除\",\n        })\n\n        setIsCategoryDeleteDialogOpen(false)\n        setDeletingCategory(null)\n        console.log('✅ 分类已从首页本地状态中移除')\n\n        // 删除成功\n        console.log('✅ 分类删除成功')\n      } else {\n        throw new Error('删除失败')\n      }\n    } catch (error) {\n      console.error('删除分类失败:', error)\n      toast({\n        title: \"删除失败\",\n        description: \"删除分类时出现错误\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setIsDeletingCategory(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <DashboardHeader />\n        <div className=\"flex\">\n          <div className=\"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-6\">\n            <DashboardSkeleton />\n          </div>\n          <div className=\"flex-1 p-6\">\n            <LoadingPage />\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* 顶部导航 */}\n      <DashboardHeader onCreatePrompt={handleCreatePrompt}>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsImportExportModalOpen(true)}\n          className=\"hidden sm:flex\"\n        >\n          <Icon name=\"database\" className=\"h-4 w-4 mr-2\" />\n          数据管理\n        </Button>\n        {/* 移除同步指示器，直接使用 API */}\n      </DashboardHeader>\n\n\n\n      <div className=\"flex h-[calc(100vh-64px)] relative\">\n        {/* 移动端遮罩层 */}\n        {!isSidebarCollapsed && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden\"\n            onClick={() => setIsSidebarCollapsed(true)}\n          />\n        )}\n\n        {/* 侧边栏 */}\n        <Sidebar\n          categories={categories}\n          selectedCategoryId={selectedCategoryId}\n          onCategorySelect={handleCategorySelect}\n          onCategoryCreate={handleCategoryCreate}\n          onCategoryEdit={handleCategoryEdit}\n          onCategoryDelete={handleCategoryDelete}\n          isCollapsed={isSidebarCollapsed}\n          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}\n        />\n\n        {/* 主内容区 */}\n        <div className=\"flex-1 flex overflow-hidden\">\n          {/* 左侧主内容 */}\n          <div className=\"flex-1 flex flex-col overflow-hidden\">\n            {/* 搜索栏 */}\n            <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4\">\n              <div className=\"flex items-center gap-4\">\n                {/* 移动端菜单按钮 */}\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"md:hidden\"\n                  onClick={() => setIsSidebarCollapsed(false)}\n                >\n                  <Icon name=\"bars\" className=\"h-5 w-5\" />\n                </Button>\n\n                <div className=\"flex-1 max-w-2xl\">\n                  <SearchBar\n                    value={searchQuery}\n                    onChange={setSearchQuery}\n                    onSearch={handleSearch}\n                    onRemoteSearch={handleRemoteSearch}\n                    searchHistory={searchHistory}\n                    onClearHistory={handleClearSearchHistory}\n                    showRemoteSearch={isLocalSearch && searchQuery.trim().length > 0}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* 内容区域 */}\n            <main className=\"flex-1 overflow-y-auto p-6\">\n            {/* 统计信息 */}\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                {selectedCategoryId \n                  ? categories.find(c => c.id === selectedCategoryId)?.name || '分类'\n                  : '全部提示词'\n                }\n              </h1>\n              <div className=\"flex items-center gap-4\">\n                <p className=\"text-muted-foreground\">\n                  共 {total} 个提示词\n                  {searchQuery && ` · 搜索 \"${searchQuery}\"`}\n                </p>\n                {searchQuery && (\n                  <div className=\"flex items-center gap-2\">\n                    {isLocalSearch ? (\n                      <span className=\"text-xs bg-green-100 text-green-700 px-2 py-1 rounded\">\n                        本地搜索\n                      </span>\n                    ) : (\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded\">\n                          在线搜索\n                        </span>\n                        <button\n                          onClick={() => setIsLocalSearch(true)}\n                          className=\"text-xs text-blue-600 hover:text-blue-800 underline\"\n                        >\n                          切换到本地搜索\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* 批量操作工具栏 */}\n            <BatchOperationsToolbar\n              selectionManager={selectionManager}\n              selectedIds={selectedIds}\n              allIds={prompts.map(p => p.id)}\n              categories={categories}\n              onOperationComplete={() => {\n                // 重新加载数据\n                initializeApp()\n              }}\n              className=\"mb-4\"\n            />\n\n            {/* 提示词网格 */}\n            {prompts.length > 0 ? (\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6\">\n                {prompts.map((prompt) => (\n                  <SelectablePromptCard\n                    key={prompt.id}\n                    prompt={prompt}\n                    categoryName={prompt.category?.name}\n                    categoryColor={prompt.category?.color}\n                    tags={prompt.tags}\n                    selectionManager={selectionManager}\n                    isSelected={selectionManager.isSelected(prompt.id)}\n                    isSelectionMode={isSelectionMode}\n                    onView={handlePromptView}\n                    onEdit={handlePromptEdit}\n                    onDelete={handlePromptDelete}\n                    onCopySuccess={() => {\n                      // 更新本地状态中的使用次数，避免重新加载\n                      setPrompts(prevPrompts =>\n                        prevPrompts.map(p =>\n                          p.id === prompt.id\n                            ? { ...p, usage_count: p.usage_count + 1 }\n                            : p\n                        )\n                      )\n                    }}\n                  />\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <Icon name=\"search\" className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {searchQuery ? '未找到匹配的提示词' : '暂无提示词'}\n                </h3>\n                <p className=\"text-muted-foreground mb-4\">\n                  {searchQuery \n                    ? '尝试调整搜索关键词或清除筛选条件'\n                    : '开始创建您的第一个提示词吧'\n                  }\n                </p>\n                <Button\n                  onClick={() => {\n                    if (searchQuery) {\n                      setSearchQuery('')\n                    } else {\n                      handleCreatePrompt()\n                    }\n                  }}\n                >\n                  <Icon name=\"plus\" className=\"h-4 w-4 mr-2\" />\n                  {searchQuery ? '清除搜索' : '新建提示词'}\n                </Button>\n              </div>\n            )}\n\n            {/* 加载更多 */}\n            {hasMore && (\n              <div className=\"text-center mt-8\">\n                <Button\n                  variant=\"outline\"\n                  onClick={loadMore}\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <>\n                      <Icon name=\"spinner\" className=\"h-4 w-4 mr-2 animate-spin\" />\n                      加载中...\n                    </>\n                  ) : (\n                    <>\n                      <Icon name=\"refresh\" className=\"h-4 w-4 mr-2\" />\n                      加载更多\n                    </>\n                  )}\n                </Button>\n              </div>\n            )}\n          </main>\n          </div>\n\n          {/* 右侧复制历史 - 仅在大屏幕显示 */}\n          <div\n            className=\"hidden xl:flex relative border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\"\n            style={{ width: copyHistoryWidth }}\n          >\n            {/* 拖拽调整手柄 */}\n            <div\n              className={`absolute left-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-400 transition-all duration-200 group ${\n                isResizing ? 'bg-blue-500 w-3' : 'bg-gray-300 hover:bg-blue-400'\n              }`}\n              onMouseDown={handleResizeStart}\n              title=\"拖拽调整宽度\"\n            >\n              {/* 拖拽指示器 */}\n              <div className=\"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-8 bg-white opacity-60 group-hover:opacity-100 transition-opacity\" />\n            </div>\n\n            {/* 复制历史内容 */}\n            <div className=\"flex-1 p-4 overflow-y-auto\">\n              <CopyHistory />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 模态框 */}\n      <PromptDetailModal\n        prompt={selectedPrompt}\n        isOpen={isDetailModalOpen}\n        onClose={() => {\n          setIsDetailModalOpen(false)\n          setSelectedPrompt(null)\n        }}\n        onEdit={handlePromptEdit}\n        onDelete={handlePromptDelete}\n        onCopy={handlePromptCopy}\n      />\n\n      <PromptFormModal\n        prompt={editingPrompt}\n        isOpen={isFormModalOpen}\n        onClose={() => {\n          setIsFormModalOpen(false)\n          setEditingPrompt(null)\n        }}\n        onSuccess={handleFormSuccess}\n      />\n\n      <DeleteConfirmDialog\n        isOpen={isDeleteDialogOpen}\n        onClose={() => {\n          setIsDeleteDialogOpen(false)\n          setDeletingPrompt(null)\n        }}\n        onConfirm={handleDeleteConfirm}\n        title=\"删除提示词\"\n        description=\"此操作无法撤销，确定要删除这个提示词吗？\"\n        itemName={deletingPrompt?.title}\n        isLoading={isDeleting}\n      />\n\n      <CategoryFormModal\n        category={editingCategory}\n        isOpen={isCategoryFormModalOpen}\n        onClose={() => {\n          setIsCategoryFormModalOpen(false)\n          setEditingCategory(null)\n        }}\n        onSuccess={handleCategoryFormSuccess}\n      />\n\n      <DeleteConfirmDialog\n        isOpen={isCategoryDeleteDialogOpen}\n        onClose={() => {\n          setIsCategoryDeleteDialogOpen(false)\n          setDeletingCategory(null)\n        }}\n        onConfirm={handleCategoryDeleteConfirm}\n        title=\"删除分类\"\n        description=\"此操作无法撤销，确定要删除这个分类吗？\"\n        itemName={deletingCategory?.name}\n        isLoading={isDeletingCategory}\n      />\n\n      {/* 数据导入/导出模态框 */}\n      <DataImportExportModal\n        open={isImportExportModalOpen}\n        onOpenChange={setIsImportExportModalOpen}\n        onImportComplete={handleImportComplete}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,sBAAsB;AAEtB,eAAe;AACf;;;AAzBA;;;;;;;;;;;;;;;;;;;AAiCe,SAAS;QA+rBJ;;IA9rBlB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE,EAAE,WAAW;;IACjF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,aAAa;;IAEtE,QAAQ;IACR,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACjF,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAEnF,YAAY;IACZ,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,SAAS;IACT,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAAE,IAAM,IAAI,6HAAA,CAAA,wBAAqB;;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,WAAW;;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEvC,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;iEAAwB,CAAC;oBAC7B,eAAe,MAAM,IAAI,CAAC;oBAC1B,mBAAmB,YAAY,IAAI,GAAG;gBACxC;;YAEA,iBAAiB,iBAAiB,CAAC;YAEnC;2CAAO;oBACL,iBAAiB,cAAc,CAAC;gBAClC;;QACF;kCAAG;QAAC;KAAiB;IAErB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,gBAAgB;QAClB;kCAAG,EAAE;IAEL,SAAS;IACT,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,OAAe;YACrD,IAAI,kBAAkB;YAEtB,QAAQ;YACR,IAAI,YAAY;gBACd,kBAAkB,gBAAgB,MAAM;qEAAC,CAAA,SAAU,OAAO,WAAW,KAAK;;YAC5E;YAEA,SAAS;YACT,IAAI,MAAM,IAAI,IAAI;gBAChB,MAAM,aAAa,MAAM,WAAW;gBACpC,kBAAkB,gBAAgB,MAAM;qEAAC,CAAA;4BAGvC;+BAFA,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,iBACtC,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,IAAI;6EAAC,CAAA,MAAO,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;;;;YAE7D;YAEA,UAAU;YACV,gBAAgB,IAAI;iEAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;;YAE9F,WAAW;YACX,SAAS,gBAAgB,MAAM;YAC/B,WAAW,QAAO,YAAY;QAChC;wDAAG;QAAC;KAAW;IAEf,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,iBAAiB,WAAW,MAAM,GAAG,GAAG;gBAC1C,SAAS;gBACT,mBAAmB,aAAa;YAClC,OAAO;gBACL,SAAS;gBACT;YACF;QACF;kCAAG;QAAC;QAAoB;QAAa;QAAa;QAAe,WAAW,MAAM;QAAE;KAAmB;IAEvG,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YAEb,QAAQ,GAAG,CAAC;YAEZ,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;gBACA;aACD;YAED,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,uHAAA,CAAA,aAAc,CAAC;gBACpC,QAAQ;gBACR,WAAW;gBACX,OAAO,KAAK,mBAAmB;YACjC;YAEA,cAAc,SAAS,IAAI;YAE3B,eAAe;YACf,IAAI,CAAC,eAAe,CAAC,oBAAoB;gBACvC,WAAW,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,MAAK,UAAU;gBACjD,SAAS,SAAS,IAAI,CAAC,MAAM;gBAC7B,WAAW,SAAS,IAAI,CAAC,MAAM,GAAG;YACpC;YAEA,QAAQ,GAAG,CAAC,AAAC,SAA6B,OAArB,SAAS,IAAI,CAAC,MAAM,EAAC;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,mBAAmB;YACnB,iBAAiB;QACnB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,oBAAoB;YACpB,MAAM,SAAuB;gBAC3B,OAAO,eAAe;gBACtB,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX,OAAO;gBACP,QAAQ,CAAC,cAAc,CAAC,IAAI;YAC9B;YAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,aAAc,CAAC;YAEtC,IAAI,gBAAgB,GAAG;gBACrB,WAAW,SAAS,IAAI;YAC1B,OAAO;gBACL,WAAW,CAAA,OAAQ;2BAAI;2BAAS,SAAS,IAAI;qBAAC;YAChD;YAEA,WAAW,SAAS,OAAO;YAC3B,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,uBAAuB;YACvB,QAAQ,GAAG,CAAC;YACZ,MAAM,OAAO,MAAM,uHAAA,CAAA,gBAAiB;YACpC,cAAc;YACd,QAAQ,GAAG,CAAC,AAAC,SAAoB,OAAZ,KAAK,MAAM,EAAC;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,OAAO,MAAM,uHAAA,CAAA,mBAAoB,CAAC;YACxC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACtC,mBAAmB;YACnB,eAAe;YACf,eAAe;YAEf,iBAAiB;YACjB,IAAI,MAAM,IAAI,IAAI;gBAChB,IAAI;oBACF,MAAM,uHAAA,CAAA,mBAAoB,CAAC,MAAM,IAAI;oBACrC,WAAW;oBACX;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,aAAa;gBAC7B;YACF;QACF;kDAAG,EAAE;IAEL,SAAS;IACT,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YAC5C,IAAI;gBACF,iBAAiB,QAAO,YAAY;gBACpC,eAAe;gBACf,eAAe;gBAEf,UAAU;gBACV,IAAI,MAAM,IAAI,IAAI;oBAChB,IAAI;wBACF,MAAM,uHAAA,CAAA,mBAAoB,CAAC,MAAM,IAAI;wBACrC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;QACF;wDAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,eAAe;IACjB;IAEA,eAAe;IACf,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,EAAE,cAAc;QAEhB,MAAM,SAAS,EAAE,OAAO;QACxB,MAAM,aAAa;QAEnB,MAAM,kBAAkB,CAAC;YACvB,MAAM,SAAS,SAAS,EAAE,OAAO;YACjC,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa;YAC1D,oBAAoB;QACtB;QAEA,MAAM,gBAAgB;YACpB,cAAc;YACd,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC1C;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;IACvC;IAEA,MAAM,mBAAmB,OAAO,SAAiB;QAC/C,IAAI;YACF,SAAS;YACT,MAAM,uHAAA,CAAA,uBAAwB,CAAC;YAE/B,UAAU;YACV,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1C,IAAI,QAAQ;gBACV,cACE,UACA,OAAO,KAAK,EACZ,SACA,OAAO,QAAQ,GAAG;oBAChB,MAAM,OAAO,QAAQ,CAAC,IAAI;oBAC1B,OAAO,OAAO,QAAQ,CAAC,KAAK;gBAC9B,IAAI;YAER;YAEA,sBAAsB;YACtB,WAAW,CAAA,cACT,YAAY,GAAG,CAAC,CAAA,IACd,EAAE,EAAE,KAAK,WACL;wBAAE,GAAG,CAAC;wBAAE,aAAa,EAAE,WAAW,GAAG;oBAAE,IACvC;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,0BAA0B;QAC1B,MAAM,WAAW,OAAO,eAAe,WAAW,aAAa,WAAW,EAAE;QAE5E,kBAAkB;QAClB,IAAI,OAAO,eAAe,YAAY,WAAW,EAAE,EAAE;YACnD,kBAAkB;YAClB,qBAAqB;YACrB;QACF;QAEA,wBAAwB;QACxB,IAAI,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE3C,2BAA2B;QAC3B,IAAI,CAAC,QAAQ;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,gBAAgB,OAAO,IAAI;gBACjC,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE1C,WAAW;gBACX,IAAI,QAAQ;oBACV,cAAc;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA,IAAI,QAAQ;YACV,kBAAkB;YAClB,qBAAqB;QACvB,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,0BAA0B;QAC1B,MAAM,WAAW,OAAO,eAAe,WAAW,aAAa,WAAW,EAAE;QAE5E,kBAAkB;QAClB,IAAI,OAAO,eAAe,YAAY,WAAW,EAAE,EAAE;YACnD,iBAAiB;YACjB,mBAAmB;YACnB;QACF;QAEA,wBAAwB;QACxB,IAAI,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE3C,2BAA2B;QAC3B,IAAI,CAAC,QAAQ;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,gBAAgB,OAAO,IAAI;gBACjC,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE1C,WAAW;gBACX,IAAI,QAAQ;oBACV,cAAc;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA,IAAI,QAAQ;YACV,iBAAiB;YACjB,mBAAmB;QACrB,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,0BAA0B;QAC1B,MAAM,WAAW,OAAO,eAAe,WAAW,aAAa,WAAW,EAAE;QAE5E,kBAAkB;QAClB,IAAI,OAAO,eAAe,YAAY,WAAW,EAAE,EAAE;YACnD,kBAAkB;YAClB,sBAAsB;YACtB;QACF;QAEA,wBAAwB;QACxB,IAAI,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE3C,2BAA2B;QAC3B,IAAI,CAAC,QAAQ;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,gBAAgB,OAAO,IAAI;gBACjC,SAAS,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE1C,WAAW;gBACX,IAAI,QAAQ;oBACV,cAAc;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA,IAAI,QAAQ;YACV,kBAAkB;YAClB,sBAAsB;QACxB,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,2BAA2B;IAC7B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,UAAU;YACZ,mBAAmB;YACnB,2BAA2B;QAC7B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,UAAU;YACZ,oBAAoB;YACpB,8BAA8B;QAChC;IACF;IAEA,MAAM,WAAW;QACf,IAAI,WAAW,CAAC,WAAW;YACzB,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,iBAAiB;QAEjB,sBAAsB;QACtB,IAAI,WAAW;YACb,cAAc,CAAA,OAAQ;oBAAC;uBAAc;iBAAK;YAC1C,WAAW,CAAA,OAAQ;oBAAC;uBAAc,KAAK,KAAK,CAAC,GAAG;iBAAI,GAAE,QAAQ;YAC9D,SAAS,CAAA,OAAQ,OAAO;YACxB,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,cAAc;YACd;QACF;QAEA;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC;YAEZ,cAAc;YACd,MAAM,uHAAA,CAAA,eAAgB,CAAC,eAAe,EAAE;YACxC,MAAM,UAAU;YAEhB,wCAAa;gBACX,aAAa;gBACb,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;gBACjE,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;gBAE9D,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;;YAIA,sBAAsB;YACtB,kBAAkB;YAClB,MAAM;YACN,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YAC3C,IAAI;gBACF,MAAM,uHAAA,CAAA,qBAAsB;gBAC5B,iBAAiB,EAAE;gBACnB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF;8DAAG;QAAC;KAAM;IAEV,MAAM,4BAA4B;QAChC;IACF;IAEA,WAAW;IACX,MAAM,uBAAuB;QAC3B,WAAW;QACX;IACF;IAEA,MAAM,8BAA8B;QAClC,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YAEZ,cAAc;YACd,MAAM,uHAAA,CAAA,iBAAkB,CAAC,iBAAiB,EAAE;YAC5C,MAAM,UAAU;YAEhB,wCAAa;gBACX,aAAa;gBACb,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB,EAAE;gBAEnE,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBAEA,8BAA8B;gBAC9B,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBAEZ,OAAO;gBACP,QAAQ,GAAG,CAAC;YACd;;QAGF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,kBAAe;;;;;8BAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,oBAAiB;;;;;;;;;;sCAEpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;IAKtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,qIAAA,CAAA,kBAAe;gBAAC,gBAAgB;0BAC/B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,2BAA2B;oBAC1C,WAAU;;sCAEV,6LAAC,4HAAA,CAAA,OAAI;4BAAC,MAAK;4BAAW,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAQrD,6LAAC;gBAAI,WAAU;;oBAEZ,CAAC,oCACA,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,sBAAsB;;;;;;kCAKzC,6LAAC,yHAAA,CAAA,UAAO;wBACN,YAAY;wBACZ,oBAAoB;wBACpB,kBAAkB;wBAClB,kBAAkB;wBAClB,gBAAgB;wBAChB,kBAAkB;wBAClB,aAAa;wBACb,kBAAkB,IAAM,sBAAsB,CAAC;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,sBAAsB;8DAErC,cAAA,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAK;wDAAO,WAAU;;;;;;;;;;;8DAG9B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+HAAA,CAAA,YAAS;wDACR,OAAO;wDACP,UAAU;wDACV,UAAU;wDACV,gBAAgB;wDAChB,eAAe;wDACf,gBAAgB;wDAChB,kBAAkB,iBAAiB,YAAY,IAAI,GAAG,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;kDAOvE,6LAAC;wCAAK,WAAU;;0DAEhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,qBACG,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iCAA9B,uCAAA,iBAAmD,IAAI,KAAI,OAC3D;;;;;;kEAGN,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;;oEAAwB;oEAChC;oEAAM;oEACR,eAAe,AAAC,UAAqB,OAAZ,aAAY;;;;;;;4DAEvC,6BACC,6LAAC;gEAAI,WAAU;0EACZ,8BACC,6LAAC;oEAAK,WAAU;8EAAwD;;;;;yFAIxE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAsD;;;;;;sFAGtE,6LAAC;4EACC,SAAS,IAAM,iBAAiB;4EAChC,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAWb,6LAAC,gJAAA,CAAA,yBAAsB;gDACrB,kBAAkB;gDAClB,aAAa;gDACb,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;gDAC7B,YAAY;gDACZ,qBAAqB;oDACnB,SAAS;oDACT;gDACF;gDACA,WAAU;;;;;;4CAIX,QAAQ,MAAM,GAAG,kBAChB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,GAAG,CAAC,CAAC;wDAII,kBACC;yEAJjB,6LAAC,8IAAA,CAAA,uBAAoB;wDAEnB,QAAQ;wDACR,YAAY,GAAE,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI;wDACnC,aAAa,GAAE,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,KAAK;wDACrC,MAAM,OAAO,IAAI;wDACjB,kBAAkB;wDAClB,YAAY,iBAAiB,UAAU,CAAC,OAAO,EAAE;wDACjD,iBAAiB;wDACjB,QAAQ;wDACR,QAAQ;wDACR,UAAU;wDACV,eAAe;4DACb,sBAAsB;4DACtB,WAAW,CAAA,cACT,YAAY,GAAG,CAAC,CAAA,IACd,EAAE,EAAE,KAAK,OAAO,EAAE,GACd;wEAAE,GAAG,CAAC;wEAAE,aAAa,EAAE,WAAW,GAAG;oEAAE,IACvC;wDAGV;uDApBK,OAAO,EAAE;;;;;;;;;;qEAyBpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,4HAAA,CAAA,OAAI;wDAAC,MAAK;wDAAS,WAAU;;;;;;kEAC9B,6LAAC;wDAAG,WAAU;kEACX,cAAc,cAAc;;;;;;kEAE/B,6LAAC;wDAAE,WAAU;kEACV,cACG,qBACA;;;;;;kEAGN,6LAAC,8HAAA,CAAA,SAAM;wDACL,SAAS;4DACP,IAAI,aAAa;gEACf,eAAe;4DACjB,OAAO;gEACL;4DACF;wDACF;;0EAEA,6LAAC,4HAAA,CAAA,OAAI;gEAAC,MAAK;gEAAO,WAAU;;;;;;4DAC3B,cAAc,SAAS;;;;;;;;;;;;;4CAM7B,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,UAAU;8DAET,0BACC;;0EACE,6LAAC,4HAAA,CAAA,OAAI;gEAAC,MAAK;gEAAU,WAAU;;;;;;4DAA8B;;qFAI/D;;0EACE,6LAAC,4HAAA,CAAA,OAAI;gEAAC,MAAK;gEAAU,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAW5D,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAiB;;kDAGjC,6LAAC;wCACC,WAAW,AAAC,4GAEX,OADC,aAAa,oBAAoB;wCAEnC,aAAa;wCACb,OAAM;kDAGN,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,6LAAC,2IAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,kBAAkB;gBACpB;gBACA,QAAQ;gBACR,UAAU;gBACV,QAAQ;;;;;;0BAGV,6LAAC,yIAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;oBACP,mBAAmB;oBACnB,iBAAiB;gBACnB;gBACA,WAAW;;;;;;0BAGb,6LAAC,6IAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS;oBACP,sBAAsB;oBACtB,kBAAkB;gBACpB;gBACA,WAAW;gBACX,OAAM;gBACN,aAAY;gBACZ,QAAQ,EAAE,2BAAA,qCAAA,eAAgB,KAAK;gBAC/B,WAAW;;;;;;0BAGb,6LAAC,2IAAA,CAAA,oBAAiB;gBAChB,UAAU;gBACV,QAAQ;gBACR,SAAS;oBACP,2BAA2B;oBAC3B,mBAAmB;gBACrB;gBACA,WAAW;;;;;;0BAGb,6LAAC,6IAAA,CAAA,sBAAmB;gBAClB,QAAQ;gBACR,SAAS;oBACP,8BAA8B;oBAC9B,oBAAoB;gBACtB;gBACA,WAAW;gBACX,OAAM;gBACN,aAAY;gBACZ,QAAQ,EAAE,6BAAA,uCAAA,iBAAkB,IAAI;gBAChC,WAAW;;;;;;0BAIb,6LAAC,mJAAA,CAAA,wBAAqB;gBACpB,MAAM;gBACN,cAAc;gBACd,kBAAkB;;;;;;;;;;;;AAI1B;GA15BwB;;QA8CJ,wHAAA,CAAA,WAAQ;QACA,iIAAA,CAAA,iBAAc;;;KA/ClB", "debugId": null}}]}