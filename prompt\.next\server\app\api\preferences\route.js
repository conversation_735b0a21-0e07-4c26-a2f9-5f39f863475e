(()=>{var a={};a.id=560,a.ids=[560],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18968:(a,b,c)=>{"use strict";c.d(b,{Ws:()=>g,eI:()=>h,nc:()=>i,uW:()=>f});class d{generateKey(a,b){if(!b)return a;let c=this.sortObject(b);return`${a}:${JSON.stringify(c)}`}sortObject(a){if(null===a||"object"!=typeof a)return a;if(Array.isArray(a))return a.map(a=>this.sortObject(a));let b={};return Object.keys(a).sort().forEach(c=>{b[c]=this.sortObject(a[c])}),b}set(a,b,c=3e5,d){let e=this.generateKey(a,d),f={data:b,timestamp:Date.now(),ttl:c,version:this.version};this.memoryCache.set(e,f)}get(a,b){let c=this.generateKey(a,b),d=this.memoryCache.get(c);return d?d.version!==this.version||Date.now()-d.timestamp>d.ttl?(this.delete(a,b),null):d.data:null}delete(a,b){let c=this.generateKey(a,b);this.memoryCache.delete(c)}clear(){this.memoryCache.clear()}clearPattern(a){for(let b of this.memoryCache.keys())b.includes(a)&&this.memoryCache.delete(b)}getStats(){return{memorySize:this.memoryCache.size,localStorageSize:0,keys:Array.from(new Set([...Array.from(this.memoryCache.keys())]))}}constructor(){this.memoryCache=new Map,this.version="1.0.0"}}let e=new d,f={CATEGORIES:"categories",PROMPTS:"prompts",TAGS:"tags",SEARCH_HISTORY:"search_history",PROMPT_DETAIL:"prompt_detail",APP_PREFERENCES:"app_preferences"},g={SHORT:3e4,MEDIUM:12e4,LONG:6e5,VERY_LONG:18e5};async function h(a,b,c,d){let f=e.get(a,d);if(null!==f)return console.log(`Client cache hit: ${a}`,d),f;console.log(`Client cache miss: ${a}`,d);let g=await c();return e.set(a,g,b,d),g}function i(a){a.forEach(a=>{e.clearPattern(a)}),console.log(`Invalidated client cache patterns: ${a.join(", ")}`)}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43779:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{P:()=>i,Rn:()=>j});var e=c(64939),f=a([e]);e=(f.then?(await f)():f)[0];let k={host:process.env.DATABASE_HOST||"*************",port:parseInt(process.env.DATABASE_PORT||"5432"),database:process.env.DATABASE_NAME||"prompt",user:process.env.DATABASE_USER||"Seven",password:process.env.DATABASE_PASSWORD||"Abc112211",max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3},l=null;function g(){return l||((l=new e.Pool(k)).on("error",a=>{console.error("数据库连接池错误:",a)}),l.on("connect",()=>{console.log("数据库连接成功")})),l}async function h(){let a=g();return await a.connect()}async function i(a,b){let c=g();try{return await c.query(a,b)}catch(a){throw console.error("数据库查询错误:",a),a}}async function j(a){let b=await h();try{await b.query("BEGIN");let c=await a(b);return await b.query("COMMIT"),c}catch(a){throw await b.query("ROLLBACK"),a}finally{b.release()}}d()}catch(a){d(a)}})},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53968:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(54461),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/preferences/route",pathname:"/api/preferences",filename:"route",bundlePath:"app/api/preferences/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\preferences\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/preferences/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},54461:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{GET:()=>h,PUT:()=>i});var e=c(32190),f=c(92650),g=a([f]);async function h(a){try{let a=await (0,f.S)();return e.NextResponse.json(a)}catch(a){return console.error("获取应用偏好设置失败:",a),e.NextResponse.json({error:"获取应用偏好设置失败"},{status:500})}}async function i(a){try{let b=await a.json(),c=await (0,f.F)(b);return e.NextResponse.json(c)}catch(a){return console.error("更新应用偏好设置失败:",a),e.NextResponse.json({error:a instanceof Error?a.message:"更新应用偏好设置失败"},{status:500})}}f=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:a=>{"use strict";a.exports=import("pg")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92650:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{F:()=>j,S:()=>h});var e=c(43779),f=c(18968),g=a([e]);async function h(){return(0,f.eI)(f.uW.APP_PREFERENCES,f.Ws.MEDIUM,async()=>{try{let a=await (0,e.P)(`
          SELECT * FROM app_preferences LIMIT 1
        `);if(0===a.rows.length)return await i();let b=a.rows[0];return{id:b.id,theme:b.theme,language:b.language,sidebarCollapsed:b.sidebar_collapsed,copyHistoryWidth:b.copy_history_width,itemsPerPage:b.items_per_page,defaultCategoryId:b.default_category_id,settings:b.settings,createdAt:b.created_at,updatedAt:b.updated_at}}catch(a){throw console.error("获取应用偏好设置失败:",a),Error("获取应用偏好设置失败")}})}async function i(){try{let a=(await (0,e.P)(`
      INSERT INTO app_preferences (
        theme, 
        language, 
        sidebar_collapsed, 
        copy_history_width, 
        items_per_page, 
        settings
      )
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `,["system","zh-CN",!1,300,12,{}])).rows[0];return{id:a.id,theme:a.theme,language:a.language,sidebarCollapsed:a.sidebar_collapsed,copyHistoryWidth:a.copy_history_width,itemsPerPage:a.items_per_page,defaultCategoryId:a.default_category_id,settings:a.settings,createdAt:a.created_at,updatedAt:a.updated_at}}catch(a){throw console.error("创建默认应用偏好设置失败:",a),Error("创建默认应用偏好设置失败")}}async function j(a){try{let b=await h();if(!b)throw Error("应用偏好设置不存在");let c=[],d=[],g=1;if(void 0!==a.theme&&(c.push(`theme = $${g}`),d.push(a.theme),g++),void 0!==a.language&&(c.push(`language = $${g}`),d.push(a.language),g++),void 0!==a.sidebarCollapsed&&(c.push(`sidebar_collapsed = $${g}`),d.push(a.sidebarCollapsed),g++),void 0!==a.copyHistoryWidth&&(c.push(`copy_history_width = $${g}`),d.push(a.copyHistoryWidth),g++),void 0!==a.itemsPerPage&&(c.push(`items_per_page = $${g}`),d.push(a.itemsPerPage),g++),void 0!==a.defaultCategoryId&&(c.push(`default_category_id = $${g}`),d.push(a.defaultCategoryId),g++),void 0!==a.settings&&(c.push(`settings = $${g}`),d.push(a.settings),g++),0===c.length)return b;c.push("updated_at = NOW()"),d.push(b.id);let i=await (0,e.P)(`
      UPDATE app_preferences 
      SET ${c.join(", ")}
      WHERE id = $${g}
      RETURNING *
    `,d);(0,f.nc)(f.uW.APP_PREFERENCES);let j=i.rows[0];return{id:j.id,theme:j.theme,language:j.language,sidebarCollapsed:j.sidebar_collapsed,copyHistoryWidth:j.copy_history_width,itemsPerPage:j.items_per_page,defaultCategoryId:j.default_category_id,settings:j.settings,createdAt:j.created_at,updatedAt:j.updated_at}}catch(a){throw console.error("更新应用偏好设置失败:",a),Error("更新应用偏好设置失败")}}e=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=53968));module.exports=c})();