(()=>{var a={};a.id=96,a.ids=[96],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18968:(a,b,c)=>{"use strict";c.d(b,{Ws:()=>g,eI:()=>h,nc:()=>i,uW:()=>f});class d{generateKey(a,b){if(!b)return a;let c=this.sortObject(b);return`${a}:${JSON.stringify(c)}`}sortObject(a){if(null===a||"object"!=typeof a)return a;if(Array.isArray(a))return a.map(a=>this.sortObject(a));let b={};return Object.keys(a).sort().forEach(c=>{b[c]=this.sortObject(a[c])}),b}set(a,b,c=3e5,d){let e=this.generateKey(a,d),f={data:b,timestamp:Date.now(),ttl:c,version:this.version};this.memoryCache.set(e,f)}get(a,b){let c=this.generateKey(a,b),d=this.memoryCache.get(c);return d?d.version!==this.version||Date.now()-d.timestamp>d.ttl?(this.delete(a,b),null):d.data:null}delete(a,b){let c=this.generateKey(a,b);this.memoryCache.delete(c)}clear(){this.memoryCache.clear()}clearPattern(a){for(let b of this.memoryCache.keys())b.includes(a)&&this.memoryCache.delete(b)}getStats(){return{memorySize:this.memoryCache.size,localStorageSize:0,keys:Array.from(new Set([...Array.from(this.memoryCache.keys())]))}}constructor(){this.memoryCache=new Map,this.version="1.0.0"}}let e=new d,f={CATEGORIES:"categories",PROMPTS:"prompts",TAGS:"tags",SEARCH_HISTORY:"search_history",PROMPT_DETAIL:"prompt_detail",APP_PREFERENCES:"app_preferences"},g={SHORT:3e4,MEDIUM:12e4,LONG:6e5,VERY_LONG:18e5};async function h(a,b,c,d){let f=e.get(a,d);if(null!==f)return console.log(`Client cache hit: ${a}`,d),f;console.log(`Client cache miss: ${a}`,d);let g=await c();return e.set(a,g,b,d),g}function i(a){a.forEach(a=>{e.clearPattern(a)}),console.log(`Invalidated client cache patterns: ${a.join(", ")}`)}},20788:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{$s:()=>i,K7:()=>l,bW:()=>h,st:()=>k,wp:()=>m,zZ:()=>j});var e=c(43779),f=c(18968),g=a([e]);async function h(){return(0,f.eI)(f.uW.CATEGORIES,f.Ws.LONG,async()=>{try{return(await (0,e.P)(`
          SELECT 
            c.id,
            c.name,
            c.description,
            c.color,
            c.icon,
            c.sort_order,
            c.created_at,
            c.updated_at,
            COUNT(p.id) as prompt_count
          FROM categories c
          LEFT JOIN prompts p ON c.id = p.category_id AND p.deleted_at IS NULL
          WHERE c.deleted_at IS NULL
          GROUP BY c.id, c.name, c.description, c.color, c.icon, c.sort_order, c.created_at, c.updated_at
          ORDER BY c.sort_order, c.name
        `)).rows.map(a=>({id:a.id,name:a.name,description:a.description,color:a.color,icon:a.icon,sort_order:a.sort_order,created_at:a.created_at,updated_at:a.updated_at,deleted_at:a.deleted_at,prompt_count:parseInt(a.prompt_count)}))}catch(a){return console.error("获取分类失败:",a),[]}})}async function i(a){try{let b=await (0,e.P)(`
      SELECT * FROM categories 
      WHERE id = $1 AND deleted_at IS NULL
    `,[a]);if(0===b.rows.length)return null;let c=b.rows[0];return{id:c.id,name:c.name,description:c.description,color:c.color,icon:c.icon,sortOrder:c.sort_order,createdAt:c.created_at,updatedAt:c.updated_at}}catch(a){throw console.error("获取分类失败:",a),Error("获取分类失败")}}async function j(a){try{let b=await (0,e.P)(`
      INSERT INTO categories (name, description, color, icon, sort_order)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `,[a.name,a.description,a.color,a.icon,a.sortOrder||0]);(0,f.nc)([f.uW.CATEGORIES]);let c=b.rows[0];return{id:c.id,name:c.name,description:c.description,color:c.color,icon:c.icon,sortOrder:c.sort_order,createdAt:c.created_at,updatedAt:c.updated_at}}catch(a){if(console.error("创建分类失败:",a),a instanceof Error&&a.message.includes("duplicate key"))throw Error("分类名称已存在");throw Error("创建分类失败")}}async function k(a,b){try{let c=[],d=[],g=1;if(void 0!==b.name&&(c.push(`name = $${g}`),d.push(b.name),g++),void 0!==b.description&&(c.push(`description = $${g}`),d.push(b.description),g++),void 0!==b.color&&(c.push(`color = $${g}`),d.push(b.color),g++),void 0!==b.icon&&(c.push(`icon = $${g}`),d.push(b.icon),g++),void 0!==b.sortOrder&&(c.push(`sort_order = $${g}`),d.push(b.sortOrder),g++),0===c.length)throw Error("没有要更新的字段");c.push("updated_at = NOW()"),d.push(a);let h=await (0,e.P)(`
      UPDATE categories 
      SET ${c.join(", ")}
      WHERE id = $${g} AND deleted_at IS NULL
      RETURNING *
    `,d);if(0===h.rows.length)throw Error("分类不存在");(0,f.nc)([f.uW.CATEGORIES]);let i=h.rows[0];return{id:i.id,name:i.name,description:i.description,color:i.color,icon:i.icon,sortOrder:i.sort_order,createdAt:i.created_at,updatedAt:i.updated_at}}catch(a){if(console.error("更新分类失败:",a),a instanceof Error&&a.message.includes("duplicate key"))throw Error("分类名称已存在");throw Error("更新分类失败")}}async function l(a){try{await (0,e.Rn)(async b=>{let c=await b.query(`
        SELECT COUNT(*) as count 
        FROM prompts 
        WHERE category_id = $1 AND deleted_at IS NULL
      `,[a]),d=parseInt(c.rows[0].count);if(d>0)throw Error(`无法删除分类，还有 ${d} 个提示词使用此分类`);let e=await b.query(`
        UPDATE categories 
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
        RETURNING id
      `,[a]);if(0===e.rows.length)throw Error("分类不存在")}),(0,f.nc)([f.uW.CATEGORIES])}catch(a){throw console.error("删除分类失败:",a),a}}async function m(a){try{await (0,e.Rn)(async b=>{for(let{id:c,sortOrder:d}of a)await b.query(`
          UPDATE categories 
          SET sort_order = $1, updated_at = NOW()
          WHERE id = $2 AND deleted_at IS NULL
        `,[d,c])}),(0,f.nc)([f.uW.CATEGORIES])}catch(a){throw console.error("更新分类排序失败:",a),Error("更新分类排序失败")}}e=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},27653:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{DELETE:()=>j,GET:()=>h,PUT:()=>i});var e=c(32190),f=c(20788),g=a([f]);async function h(a,{params:b}){try{let a=await (0,f.$s)(b.id);if(!a)return e.NextResponse.json({error:"分类不存在"},{status:404});return e.NextResponse.json(a)}catch(a){return console.error("获取分类失败:",a),e.NextResponse.json({error:"获取分类失败"},{status:500})}}async function i(a,{params:b}){try{let c=await a.json(),d=await (0,f.st)(b.id,c);return e.NextResponse.json(d)}catch(a){return console.error("更新分类失败:",a),e.NextResponse.json({error:a instanceof Error?a.message:"更新分类失败"},{status:500})}}async function j(a,{params:b}){try{return await (0,f.K7)(b.id),e.NextResponse.json({success:!0})}catch(a){return console.error("删除分类失败:",a),e.NextResponse.json({error:a instanceof Error?a.message:"删除分类失败"},{status:500})}}f=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43779:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{P:()=>i,Rn:()=>j});var e=c(64939),f=a([e]);e=(f.then?(await f)():f)[0];let k={host:process.env.DATABASE_HOST||"*************",port:parseInt(process.env.DATABASE_PORT||"5432"),database:process.env.DATABASE_NAME||"prompt",user:process.env.DATABASE_USER||"Seven",password:process.env.DATABASE_PASSWORD||"Abc112211",max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3},l=null;function g(){return l||((l=new e.Pool(k)).on("error",a=>{console.error("数据库连接池错误:",a)}),l.on("connect",()=>{console.log("数据库连接成功")})),l}async function h(){let a=g();return await a.connect()}async function i(a,b){let c=g();try{return await c.query(a,b)}catch(a){throw console.error("数据库查询错误:",a),a}}async function j(a){let b=await h();try{await b.query("BEGIN");let c=await a(b);return await b.query("COMMIT"),c}catch(a){throw await b.query("ROLLBACK"),a}finally{b.release()}}d()}catch(a){d(a)}})},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:a=>{"use strict";a.exports=import("pg")},78335:()=>{},81152:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(27653),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\Cursor Project\\prompy augment - 副本\\prompt\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/categories/[id]/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=81152));module.exports=c})();